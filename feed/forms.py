from django import forms
from .models import Invitation

class InvitationForm(forms.ModelForm):
    ROLE_CHOICES = (
        ('Administrator', 'Administrator'),
        ('Recruiter', 'Recruiter'),
        ('Hiring Manager', 'Hiring Manager'),
        ('Interviewer', 'Interviewer'),
        ('Read Only', 'Read Only'),
    )

    role = forms.ChoiceField(choices=ROLE_CHOICES)

    class Meta:
        model = Invitation
        fields = ['first_name', 'last_name', 'email', 'role']