import re
from django import template

register = template.Library()

@register.filter
def years_only_with_plus(experience_text):
    """
    Extract years from experience text and format as '+X years'.
    
    Input examples:
    - "6 years, 1 month" -> "+6 years"
    - "3 years" -> "+3 years"
    - "1 year, 6 months" -> "+1 years"
    - "0 years, 8 months" -> "+0 years"
    - None or empty -> "Not specified"
    
    Args:
        experience_text (str): Experience text in format "X years, Y months" or similar
        
    Returns:
        str: Formatted experience as "+X years"
    """
    if not experience_text:
        return "Not specified"
    
    try:
        # Use regex to extract the number before "year" or "years"
        # This handles cases like "6 years, 1 month", "3 years", "1 year", etc.
        match = re.search(r'(\d+)\s*years?', str(experience_text), re.IGNORECASE)
        
        if match:
            years = int(match.group(1))
            return f"+{years} years"
        else:
            # If no years pattern found, return the original text
            return str(experience_text)
            
    except (ValueError, AttributeError):
        # If any error occurs, return the original text
        return str(experience_text) if experience_text else "Not specified"
