body {
    background-color: #EBEBEB !important;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.header {
    width: 100%;
    box-shadow: 0px 5px 10px grey;
    position: relative;
    z-index: 99;

}


/* Header Styles from feed. */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #1a202c;
}

.avatar {
    display: inline-block;
    position: relative;
    width: 3rem;
    height: 3rem;
    text-align: center;
    border: #000000;
    border-radius: 50%;
    background: #ffffff;
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15);
    line-height: 1rem;
}

.page-heading {
    text-transform: uppercase;
    letter-spacing: .2em;
    font-weight: 50;
}

.page-header {
    margin-bottom: 1rem;
}

.page-holder {
    display: flex;
    overflow-x: hidden;
    width: 100%;
    min-height: calc(100vh - 72px);
    padding-top: 3rem;
    flex-wrap: wrap;
    background: #EBEBEB;
}

.card-header {
    position: relative;
    padding: 2rem 2rem;
    border-bottom: none;
    background-color: #fff;
    box-shadow: 0 .125rem .25rem rgba(0, 0, 0, .075);
    z-index: 2;
}

.card-profile-img {
    width: 70%;
    border-radius: 50%;
    margin: 0 auto;
    box-shadow: 0 0 10px rgba(0, 0, 0, .2);
}

.text-end {
    text-align: right !important;
}

.card-adjust-height-xl .card-body {
    overflow: auto;
    overflow-y: scroll;
    height: calc(100vh - 24px);
    margin-bottom: 2rem;
    padding-bottom: 1rem;
}

.list-group-timeline .list-group-item::before {
    position: absolute;
    left: 1.5rem;
    height: 100%;
    content: "";
    border-left: 1px solid #dee2e6;
}

.list-group-timeline .list-group-item {
    position: relative;
    border: 0;
    background-color: transparent;
}

.createjob-card-adjust-height-xl .card-body {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
}

#editor-container {
    height: 500px;
    margin-bottom: 2rem;
}

.dropdown-menu-animated.show {
    top: 100% !important;
}

.dropdown-menu-animated.dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

.ms-auto {
    margin-left: auto !important;
}

.pe-0 {
    padding-right: 0 !important;
}

.profile-photo-card {
    border-collapse: separate;
    padding: 1.5em .5em .5em;
    width: 18rem;
    border-radius: 2%;
    text-align: center;
    box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
    background: rgb(52, 53, 83);
}

.candidate-micro-cv {
    border-collapse: separate;
    padding: 1.5em .5em .5em;
    border-radius: 2%;
    text-align: center;
    box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
}

.feed-page-activites-card {
    border: none;
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15);
}

.feed-page-jobs-card {
    border: solid rgb(16, 16, 15) 0.5px;
    border-radius: 0.5em;
    box-shadow: 0 .5rem 1rem rgba(36, 51, 37, 0.15);
}

.job-details-card-border {
    border: solid rgb(151, 210, 106) 0.5px;
    border-radius: 0.5em;
    box-shadow: 0 .5rem 1rem rgba(51, 151, 59, 0.15);
}

.signin-field-card-border {
    border: solid black;
    border-radius: 0.5rem;
    box-shadow: 0 .5rem 1rem rgba(74, 93, 75, 0.15);
    margin-bottom: 2rem;
    padding-bottom: 1rem;
}

.jobs-page-card {
    border: none;
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15);
}

.create-new-job-card {
    border: none;
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15);
}

.active-skills-div {
    border: solid #667ead 0.5px;
    border-radius: 0.5rem;
    padding: 0.5rem;
    text-align: center;
    margin-left: 1rem;
    margin-bottom: 0.5rem;
}

.active-skills-div:hover span {
    display: none;
}

.active-skills-div:hover {
    color: #fff;
    background-color: #ac4f2f;
}

.active-skills-div:hover::after {
    display: block;
    margin: auto;
    content: "Remove";
    cursor: pointer;
}

.passive-skills-div {
    border: solid #667ead 0.5px;
    border-radius: 0.5rem;
    padding: 0.5rem;
    text-align: center;
    margin-left: 1rem;
    margin-bottom: 0.5rem;
}

.passive-skills-div:hover span {
    display: none;
}

.passive-skills-div:hover {
    color: #fff;
    background-color: #214e34;
}

.passive-skills-div:hover::after {
    display: block;
    margin: auto;
    content: "Add";
    cursor: pointer;
}

.deatils-job-page-title-card {
    border: solid rgb(100, 172, 230) 0.5px;
    border-radius: 0.5em;
    box-shadow: 0 .5rem 1rem rgba(51, 151, 59, 0.15);
}

.platform-metrics-card .card-footer {
    background: #90909F;
    color: white;
}

.platform-metrics-card {
    border: solid rgb(98, 25, 25) 0.5px;
    border-radius: 0.5em;
    box-shadow: 0 .5rem 1rem rgba(51, 151, 59, 0.15);
}

.limited-width-content {
    max-width: 1800px !important;
    margin: auto !important;
    background: #EBEBEB;
}

.settings-card {
    border: solid rgb(52, 53, 83) 0.5px;
    border-radius: 0.5em;
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15);
    padding: 1rem;
    background-color: #eaeaf4;
    margin-left: 5%;
    margin-right: 5%;
}

.vertical-line {
    border-left: 0.2rem solid gray;
    height: 1rem;
}

.vertical-line-long {
    border-left: 0.2rem solid gray;
    height: 2rem;
}

.vertical-line-dashed {
    border-left: 0.2rem dashed gray;
    height: 6rem;
}

.journey-past-icon {
    font-size: 1rem;
}

.journey-current-card {
    border-width: 6px !important;
    border-color: #214E34 !important;
}

.journey-pills {
    font-size: smaller;
}

.journey-start {
    font-size: 50px;
    padding-bottom: -12px;
    margin-bottom: -12px;
}

.journey-end {
    font-size: 50px;
    padding-top: -16px;
    margin-top: -16px;
}

.span .cut-text {
    display: inline-block;
    width: 180px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}


.small-text {
    font-size: smaller;
}

#progressbar {
    background-color: lightgray;
    border-radius: 8px;
    /* (height of inner div) / 2 + padding */
    padding: 1px;
    height: 10px;
}

#progressbar>div {
    background-color: darkgreen;
    width: 1%;
    /* Adjust with JavaScript */
    height: 95%;
    border-radius: 5px;
}

.form-group .form-control:focus {
    border: solid rgb(52, 53, 83) 0.1px;
    box-shadow: 0 0 0.1rem rgb(52, 53, 83);
}

#signin_button {
    color: #000;
    background-color: #fff;
    border: solid rgb(52, 53, 83) 1px;
    border-radius: .3rem;
    padding-left: 2rem;
    padding-right: 2rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

#signin_button:hover {
    color: #fff;
    background-color: rgb(52, 53, 83);
    border: solid rgb(52, 53, 83) 1px;
}

.bi-arrow-right-circle-fill {
    font-size: x-large;
    color: gray;
}

.bi-arrow-right-circle-fill:hover {
    font-size: x-large;
    color: rgb(52, 53, 83);
}

.shadow {
    box-shadow: 0px 0px 10px 0px gray;
}

.job-description-area-card {
    background: white;
    color: #000;
    border: solid rgb(16, 16, 15) 0.5px;
    border-radius: 0.5em;
    box-shadow: 0 .5rem 1rem rgba(36, 51, 37, 0.15);
}


.activity-list::-webkit-scrollbar {
    width: 8px;
}

.activity-list::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
    border-radius: 4px;
}

.activity-list::-webkit-scrollbar-thumb:hover {
    background-color: var(--secondary-color);
}

.activity-list::-webkit-scrollbar-track {
    background: transparent;
}

.selected-recruiters {
    margin-top: 10px;
}

.selected-name {
    display: inline-block;
    background-color: #e0e0e0;
    padding: 5px;
    margin-right: 5px;
    margin-bottom: 5px;
    border-radius: 3px;
}

.remove-name {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    margin-left: 5px;
}