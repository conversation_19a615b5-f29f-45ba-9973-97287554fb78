from django.utils import translation
from django.conf import settings


def language_context(request):
    """
    Context processor to add language information to all templates.
    This ensures language information is available even in Docker environments.
    """
    current_language = translation.get_language()

    # Get the current language name
    current_language_name = None
    for code, name in settings.LANGUAGES:
        if code == current_language:
            current_language_name = name
            break

    return {
        'CURRENT_LANGUAGE': current_language,
        'CURRENT_LANGUAGE_NAME': current_language_name,
        'AVAILABLE_LANGUAGES': settings.LANGUAGES,
    }


def role_permissions(request):
    """
    Add role-based permission information to template context.
    This allows templates to show/hide UI elements based on user role.
    """
    if not request.user.is_authenticated:
        return {}

    try:
        employee = request.user.employee
        user_role = employee.role

        # Define role permissions (same as in decorators.py)
        role_permissions_map = {
            'Administrator': [
                'create_jobs', 'manage_candidates', 'manage_team', 'delete_data',
                'view_analytics', 'conduct_interviews', 'make_decisions', 'admin_settings'
            ],
            'Recruiter': [
                'create_jobs', 'manage_candidates', 'view_analytics',
                'conduct_interviews', 'make_decisions'
            ],
            'Hiring Manager': [
                'manage_candidates', 'view_analytics', 'conduct_interviews', 'make_decisions'
            ],
            'Interviewer': [
                'manage_candidates', 'conduct_interviews'
            ],
            'Read Only': [
                'view_analytics'
            ]
        }

        user_permissions = role_permissions_map.get(user_role, [])

        # Administrators get all permissions
        is_administrator = user_role == 'Administrator'

        # Create permission flags for easy template usage
        permissions = {
            'can_create_jobs': is_administrator or 'create_jobs' in user_permissions,
            'can_manage_candidates': is_administrator or 'manage_candidates' in user_permissions,
            'can_manage_team': is_administrator or 'manage_team' in user_permissions,
            'can_delete_data': is_administrator or 'delete_data' in user_permissions,
            'can_view_analytics': is_administrator or 'view_analytics' in user_permissions,
            'can_conduct_interviews': is_administrator or 'conduct_interviews' in user_permissions,
            'can_make_decisions': is_administrator or 'make_decisions' in user_permissions,
            'can_admin_settings': is_administrator or 'admin_settings' in user_permissions,
        }

        return {
            'user_role': user_role,
            'user_permissions': permissions,
            'is_admin': user_role == 'Administrator',
            'is_recruiter': user_role == 'Recruiter',
            'is_hiring_manager': user_role == 'Hiring Manager',
            'is_interviewer': user_role == 'Interviewer',
            'is_read_only': user_role == 'Read Only',
        }

    except AttributeError:
        # User doesn't have an employee record
        return {}
