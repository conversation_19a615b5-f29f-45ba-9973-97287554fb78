from functools import wraps
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404, redirect
from django.contrib import messages
from django.http import HttpResponseForbidden
from .models import Employee, Employer, Vacancy, Application


def employer_required(view_func):
    """
    Decorator that ensures the user is authenticated and has an associated Employee record.
    This decorator relies on the EmployerIsolationMiddleware to set employer_id.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        # DEBUG: Print request attributes
        print(f"[EMPLOYER-REQUIRED-DEBUG] View: {view_func.__name__}")
        print(f"[EMPLOYER-REQUIRED-DEBUG] User: {request.user}")
        print(f"[EMPLOYER-REQUIRED-DEBUG] User authenticated: {request.user.is_authenticated}")
        print(f"[EMPLOYER-REQUIRED-DEBUG] Has employer_id: {hasattr(request, 'employer_id')}")
        if hasattr(request, 'employer_id'):
            print(f"[EMPLOYER-REQUIRED-DEBUG] Employer ID: {request.employer_id}")

        # The middleware should have already handled authentication and set employer_id
        # If we reach here and don't have employer_id, something is wrong
        if not hasattr(request, 'employer_id'):
            print(f"[EMPLOYER-REQUIRED-DEBUG] ERROR: No employer_id found!")
            messages.error(request, "Your account is not properly configured. Please contact support.")
            return redirect('signin')

        return view_func(request, *args, **kwargs)
    return _wrapped_view


def employer_data_access(model_field='employer_id'):
    """
    Decorator that ensures users can only access data belonging to their employer.
    
    Args:
        model_field: The field name that contains the employer reference in the model
    """
    def decorator(view_func):
        @wraps(view_func)
        @employer_required
        def _wrapped_view(request, *args, **kwargs):
            # Skip for superusers
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # Get the object ID from URL parameters
            # Common patterns: vacancy_id, application_id, etc.
            object_id = None
            for key, value in kwargs.items():
                if key.endswith('_id'):
                    object_id = value
                    break
            
            if object_id:
                # Determine the model based on the URL parameter name
                if 'vacancy_id' in kwargs:
                    obj = get_object_or_404(Vacancy, vacancy_id=object_id)
                    # Check if vacancy belongs to user's employer
                    if str(obj.employer_id) != str(request.employer_id):
                        return HttpResponseForbidden("You don't have permission to access this resource.")
                
                elif 'application_id' in kwargs:
                    obj = get_object_or_404(Application, application_id=object_id)
                    # Check if application's vacancy belongs to user's employer
                    if str(obj.vacancy_id.employer_id) != str(request.employer_id):
                        return HttpResponseForbidden("You don't have permission to access this resource.")
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def role_required(allowed_roles):
    """
    Decorator that restricts access based on employee role.

    Args:
        allowed_roles: List of roles that can access the view
    """
    def decorator(view_func):
        @wraps(view_func)
        @employer_required
        def _wrapped_view(request, *args, **kwargs):
            if request.employee.role not in allowed_roles:
                messages.error(request, "You don't have permission to access this page.")
                return redirect('feed')
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def permission_required(permission_type):
    """
    Advanced decorator that checks specific permissions based on role.

    Permission types:
    - 'create_jobs': Can create and publish job postings
    - 'manage_candidates': Can view and manage candidate applications
    - 'manage_team': Can invite users and manage team permissions
    - 'delete_data': Can delete jobs, applications, and other data
    - 'view_analytics': Can view reports and analytics
    - 'conduct_interviews': Can schedule and conduct interviews
    - 'make_decisions': Can change application states and make hiring decisions
    - 'admin_settings': Can access admin settings and configurations
    """
    def decorator(view_func):
        @wraps(view_func)
        @employer_required
        def _wrapped_view(request, *args, **kwargs):
            user_role = request.employee.role

            # Define role permissions
            role_permissions = {
                'Administrator': [
                    'create_jobs', 'manage_candidates', 'manage_team', 'delete_data',
                    'view_analytics', 'conduct_interviews', 'make_decisions', 'admin_settings'
                ],
                'Recruiter': [
                    'create_jobs', 'manage_candidates', 'view_analytics',
                    'conduct_interviews', 'make_decisions'
                ],
                'Hiring Manager': [
                    'manage_candidates', 'view_analytics', 'conduct_interviews', 'make_decisions'
                ],
                'Interviewer': [
                    'manage_candidates', 'conduct_interviews'
                ],
                'Read Only': [
                    'view_analytics'
                ]
            }

            # Check if user has the required permission
            user_permissions = role_permissions.get(user_role, [])
            if permission_type not in user_permissions:
                messages.error(request, f"Your role ({user_role}) doesn't have permission to {permission_type.replace('_', ' ')}.")
                return redirect('feed')

            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


class EmployerQuerySetMixin:
    """
    Mixin to automatically filter querysets by employer_id.
    Use this in your views to ensure data isolation.
    """
    
    def get_employer_filtered_queryset(self, model, request):
        """
        Get a queryset filtered by the current user's employer.
        """
        if request.user.is_superuser:
            return model.objects.all()
        
        try:
            employer_id = request.user.employee.employer_id.employer_id
            
            # Handle different model types
            if hasattr(model, 'employer_id'):
                return model.objects.filter(employer_id=employer_id)
            elif model.__name__ == 'Application':
                return model.objects.filter(vacancy_id__employer_id=employer_id)
            elif model.__name__ == 'ApplicationState':
                return model.objects.filter(application_id__vacancy_id__employer_id=employer_id)
            elif model.__name__ == 'ApplicationComment':
                return model.objects.filter(application_id__vacancy_id__employer_id=employer_id)
            elif model.__name__ == 'Appointment':
                return model.objects.filter(vacancy_id__employer_id=employer_id)
            else:
                # Default: assume direct employer_id relationship
                return model.objects.filter(employer_id=employer_id)
                
        except AttributeError:
            # User doesn't have employee record
            return model.objects.none()
