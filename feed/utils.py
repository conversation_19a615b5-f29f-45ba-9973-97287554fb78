from django.contrib.auth.models import AnonymousUser
from .models import (
    Vacancy, Application, ApplicationState, ApplicationComment, Appointment,
    Employee, JobTemplate, TalentPool, Invitation
)


def get_employer_filtered_queryset(model, user):
    """
    Utility function to get employer-filtered querysets for any model.
    
    Args:
        model: The Django model class
        user: The current user
    
    Returns:
        QuerySet filtered by user's employer
    """
    if isinstance(user, AnonymousUser) or not user.is_authenticated:
        return model.objects.none()
    
    if user.is_superuser:
        return model.objects.all()
    
    try:
        employer_id = user.employee.employer_id.employer_id
        
        # Handle different model types based on their employer relationship
        if model == Vacancy:
            return model.objects.filter(employer_id=employer_id)
        elif model == Application:
            return model.objects.filter(vacancy_id__employer_id=employer_id)
        elif model == ApplicationState:
            return model.objects.filter(application_id__vacancy_id__employer_id=employer_id)
        elif model == ApplicationComment:
            return model.objects.filter(application_id__vacancy_id__employer_id=employer_id)
        elif model == Appointment:
            return model.objects.filter(vacancy_id__employer_id=employer_id)
        elif model == Employee:
            return model.objects.filter(employer_id=employer_id)
        elif model == JobTemplate:
            return model.objects.filter(employer_id=employer_id)
        elif model == TalentPool:
            return model.objects.filter(employer_id=employer_id)
        elif model == Invitation:
            return model.objects.filter(employer_id=employer_id)
        elif hasattr(model, 'employer_id'):
            # For models with direct employer_id relationship
            return model.objects.filter(employer_id=employer_id)
        else:
            # Default: return empty queryset for unknown models
            return model.objects.none()
            
    except AttributeError:
        # User doesn't have employee record
        return model.objects.none()


def get_user_employer_id(user):
    """
    Get the employer_id for a given user.
    
    Args:
        user: The Django user object
    
    Returns:
        employer_id or None if user doesn't have an employee record
    """
    try:
        return user.employee.employer_id.employer_id
    except AttributeError:
        return None


def user_can_access_vacancy(user, vacancy_id):
    """
    Check if a user can access a specific vacancy.
    
    Args:
        user: The Django user object
        vacancy_id: The vacancy ID to check
    
    Returns:
        Boolean indicating access permission
    """
    if user.is_superuser:
        return True
    
    employer_id = get_user_employer_id(user)
    if not employer_id:
        return False
    
    try:
        vacancy = Vacancy.objects.get(vacancy_id=vacancy_id)
        return str(vacancy.employer_id) == str(employer_id)
    except Vacancy.DoesNotExist:
        return False


def user_can_access_application(user, application_id):
    """
    Check if a user can access a specific application.
    
    Args:
        user: The Django user object
        application_id: The application ID to check
    
    Returns:
        Boolean indicating access permission
    """
    if user.is_superuser:
        return True
    
    employer_id = get_user_employer_id(user)
    if not employer_id:
        return False
    
    try:
        application = Application.objects.select_related('vacancy_id').get(
            application_id=application_id
        )
        return str(application.vacancy_id.employer_id) == str(employer_id)
    except Application.DoesNotExist:
        return False


def get_employer_statistics(user):
    """
    Get statistics for the user's employer.
    
    Args:
        user: The Django user object
    
    Returns:
        Dictionary with employer statistics
    """
    employer_id = get_user_employer_id(user)
    if not employer_id:
        return {}
    
    # Get employer-specific statistics
    total_vacancies = Vacancy.objects.filter(employer_id=employer_id).count()
    active_vacancies = Vacancy.objects.filter(
        employer_id=employer_id, 
        vacancy_status='Active'
    ).count()
    total_applications = Application.objects.filter(
        vacancy_id__employer_id=employer_id
    ).count()
    total_employees = Employee.objects.filter(employer_id=employer_id).count()
    
    return {
        'total_vacancies': total_vacancies,
        'active_vacancies': active_vacancies,
        'total_applications': total_applications,
        'total_employees': total_employees,
    }


class EmployerFilterMixin:
    """
    Mixin class for views that need employer filtering.
    Use this in class-based views for automatic employer filtering.
    """
    
    def get_queryset(self):
        """Override to filter by employer."""
        queryset = super().get_queryset()
        if hasattr(self.request, 'employer_id'):
            # Apply employer filtering based on model type
            model = queryset.model
            return get_employer_filtered_queryset(model, self.request.user)
        return queryset.none()
    
    def dispatch(self, request, *args, **kwargs):
        """Ensure user has employer access before processing request."""
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        try:
            request.employer_id = request.user.employee.employer_id.employer_id
            request.employee = request.user.employee
        except AttributeError:
            return self.handle_no_permission()
        
        return super().dispatch(request, *args, **kwargs)
    
    def handle_no_permission(self):
        """Handle cases where user doesn't have permission."""
        from django.shortcuts import redirect
        from django.contrib import messages
        messages.error(self.request, "You don't have permission to access this resource.")
        return redirect('signin')
