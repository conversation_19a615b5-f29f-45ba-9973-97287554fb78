import io
import logging
import PyPDF2
import pdfplumber
import pytesseract
from pdf2image import convert_from_bytes
from PIL import Image
import tempfile
import os
from .personal_data_cencorship import UnicodeAwareCVCensor

logger = logging.getLogger(__name__)

def extract_text_from_pdf(pdf_content):
    """
    Extract text from PDF content using multiple methods.
    
    Args:
        pdf_content (bytes): PDF file content as bytes
        
    Returns:
        str: Extracted text from the PDF
    """
    if not pdf_content:
        logger.warning("PDF content is empty")
        return ""
    
    # Try multiple extraction methods in order of preference
    text = ""
    
    # Method 1: Try pdfplumber (best for text-based PDFs)
    try:
        text = _extract_with_pdfplumber(pdf_content)
        if text and len(text.strip()) > 50:  # If we got substantial text
            logger.info("Successfully extracted text using pdfplumber")
            return text
    except Exception as e:
        logger.warning(f"pdfplumber extraction failed: {str(e)}")
    
    # Method 2: Try PyPDF2 (fallback for text-based PDFs)
    try:
        text = _extract_with_pypdf2(pdf_content)
        if text and len(text.strip()) > 50:  # If we got substantial text
            logger.info("Successfully extracted text using PyPDF2")
            return text
    except Exception as e:
        logger.warning(f"PyPDF2 extraction failed: {str(e)}")
    
    # Method 3: OCR with pytesseract (for image-based PDFs)
    try:
        text = _extract_with_ocr(pdf_content)
        if text and len(text.strip()) > 20:  # Lower threshold for OCR
            logger.info("Successfully extracted text using OCR")
            return text
    except Exception as e:
        logger.warning(f"OCR extraction failed: {str(e)}")
    
    logger.error("All PDF text extraction methods failed")
    return ""

def _extract_with_pdfplumber(pdf_content):
    """Extract text using pdfplumber library."""
    text = ""
    with io.BytesIO(pdf_content) as pdf_file:
        with pdfplumber.open(pdf_file) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
    return text.strip()

def _extract_with_pypdf2(pdf_content):
    """Extract text using PyPDF2 library."""
    text = ""
    with io.BytesIO(pdf_content) as pdf_file:
        pdf_reader = PyPDF2.PdfReader(pdf_file)
        for page in pdf_reader.pages:
            page_text = page.extract_text()
            if page_text:
                text += page_text + "\n"
    return text.strip()

def _extract_with_ocr(pdf_content):
    """Extract text using OCR (Optical Character Recognition)."""
    text = ""
    try:
        # Convert PDF to images
        images = convert_from_bytes(pdf_content, dpi=300, first_page=1, last_page=3)  # Limit to first 3 pages
        
        for i, image in enumerate(images):
            try:
                # Use pytesseract to extract text from image
                page_text = pytesseract.image_to_string(image, lang='eng')
                if page_text:
                    text += page_text + "\n"
            except Exception as e:
                logger.warning(f"OCR failed for page {i+1}: {str(e)}")
                continue
                
    except Exception as e:
        logger.error(f"PDF to image conversion failed: {str(e)}")
        raise
    
    return text.strip()

def validate_pdf_content(pdf_content):
    """
    Validate that the content is a valid PDF.
    
    Args:
        pdf_content (bytes): PDF file content as bytes
        
    Returns:
        bool: True if valid PDF, False otherwise
    """
    if not pdf_content:
        return False
    
    try:
        # Check PDF header
        if pdf_content[:4] != b'%PDF':
            return False
        
        # Try to open with PyPDF2 to validate structure
        with io.BytesIO(pdf_content) as pdf_file:
            PyPDF2.PdfReader(pdf_file)
        
        return True
    except Exception as e:
        logger.warning(f"PDF validation failed: {str(e)}")
        return False

def get_pdf_info(pdf_content):
    """
    Get basic information about the PDF.
    
    Args:
        pdf_content (bytes): PDF file content as bytes
        
    Returns:
        dict: PDF information including page count, file size, etc.
    """
    info = {
        'valid': False,
        'page_count': 0,
        'file_size': len(pdf_content) if pdf_content else 0,
        'has_text': False
    }
    
    try:
        if not validate_pdf_content(pdf_content):
            return info
        
        with io.BytesIO(pdf_content) as pdf_file:
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            info['valid'] = True
            info['page_count'] = len(pdf_reader.pages)
            
            # Check if PDF has extractable text
            sample_text = ""
            for i, page in enumerate(pdf_reader.pages[:2]):  # Check first 2 pages
                page_text = page.extract_text()
                if page_text:
                    sample_text += page_text
                if len(sample_text) > 100:  # If we have enough text
                    break
            
            info['has_text'] = len(sample_text.strip()) > 50
            
    except Exception as e:
        logger.warning(f"Failed to get PDF info: {str(e)}")
    
    return info
