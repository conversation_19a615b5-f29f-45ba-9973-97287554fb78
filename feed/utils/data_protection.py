"""
Sensitive data protection and encryption utilities
"""
import base64
import hashlib
import os
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
from datetime import datetime, timedelta
from django.utils import timezone
import logging
import re

logger = logging.getLogger(__name__)


class DataEncryption:
    """
    Data encryption and decryption utilities
    """
    
    def __init__(self):
        self.encryption_key = self._get_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
    
    def _get_encryption_key(self):
        """
        Get or generate encryption key
        """
        # Try to get key from settings
        if hasattr(settings, 'DATA_ENCRYPTION_KEY'):
            return settings.DATA_ENCRYPTION_KEY.encode()
        
        # Try to get key from environment
        env_key = os.environ.get('DATA_ENCRYPTION_KEY')
        if env_key:
            return env_key.encode()
        
        # Generate key from SECRET_KEY (not recommended for production)
        logger.warning("Using SECRET_KEY for data encryption. Set DATA_ENCRYPTION_KEY for production.")
        password = settings.SECRET_KEY.encode()
        salt = b'canvider_salt_2024'  # Use a fixed salt (not ideal, but consistent)
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key
    
    def encrypt(self, data):
        """
        Encrypt sensitive data
        
        Args:
            data: String data to encrypt
            
        Returns:
            str: Base64 encoded encrypted data
        """
        if not data:
            return data
        
        try:
            encrypted_data = self.cipher_suite.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise
    
    def decrypt(self, encrypted_data):
        """
        Decrypt sensitive data
        
        Args:
            encrypted_data: Base64 encoded encrypted data
            
        Returns:
            str: Decrypted data
        """
        if not encrypted_data:
            return encrypted_data
        
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.cipher_suite.decrypt(encrypted_bytes)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise


class PIIProtection:
    """
    Personally Identifiable Information (PII) protection utilities
    """
    
    # PII patterns for detection
    PII_PATTERNS = {
        'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
        'phone': r'(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}',
        'ssn': r'\b\d{3}-\d{2}-\d{4}\b',
        'credit_card': r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',
        'ip_address': r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b',
    }
    
    @classmethod
    def detect_pii(cls, text):
        """
        Detect PII in text
        
        Args:
            text: Text to scan for PII
            
        Returns:
            dict: Dictionary of detected PII types and matches
        """
        if not text:
            return {}
        
        detected_pii = {}
        
        for pii_type, pattern in cls.PII_PATTERNS.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                detected_pii[pii_type] = matches
        
        return detected_pii
    
    @classmethod
    def mask_pii(cls, text, mask_char='*'):
        """
        Mask PII in text for logging/display
        
        Args:
            text: Text containing PII
            mask_char: Character to use for masking
            
        Returns:
            str: Text with PII masked
        """
        if not text:
            return text
        
        masked_text = text
        
        # Mask emails
        masked_text = re.sub(
            cls.PII_PATTERNS['email'],
            lambda m: m.group(0)[:2] + mask_char * (len(m.group(0)) - 4) + m.group(0)[-2:],
            masked_text,
            flags=re.IGNORECASE
        )
        
        # Mask phone numbers
        masked_text = re.sub(
            cls.PII_PATTERNS['phone'],
            lambda m: mask_char * (len(m.group(0)) - 4) + m.group(0)[-4:],
            masked_text
        )
        
        # Mask SSN
        masked_text = re.sub(
            cls.PII_PATTERNS['ssn'],
            f'{mask_char * 3}-{mask_char * 2}-****',
            masked_text
        )
        
        # Mask credit cards
        masked_text = re.sub(
            cls.PII_PATTERNS['credit_card'],
            lambda m: mask_char * (len(m.group(0)) - 4) + m.group(0)[-4:],
            masked_text
        )
        
        return masked_text
    
    @classmethod
    def hash_pii(cls, data, salt=None):
        """
        Hash PII for secure storage/comparison
        
        Args:
            data: PII data to hash
            salt: Optional salt for hashing
            
        Returns:
            str: Hashed data
        """
        if not data:
            return data
        
        if salt is None:
            salt = getattr(settings, 'PII_HASH_SALT', 'canvider_pii_salt_2024')
        
        combined = f"{data}{salt}"
        return hashlib.sha256(combined.encode()).hexdigest()


class DataRetentionManager:
    """
    Data retention and cleanup utilities
    """
    
    # Default retention periods (in days)
    RETENTION_PERIODS = {
        'application_data': 1095,  # 3 years
        'candidate_data': 1095,    # 3 years
        'cv_files': 1095,          # 3 years
        'email_logs': 365,         # 1 year
        'audit_logs': 2555,        # 7 years
        'session_data': 30,        # 30 days
        'temp_files': 7,           # 7 days
    }
    
    @classmethod
    def should_retain_data(cls, data_type, created_date):
        """
        Check if data should be retained based on retention policy
        
        Args:
            data_type: Type of data
            created_date: When the data was created
            
        Returns:
            bool: True if data should be retained
        """
        retention_days = cls.RETENTION_PERIODS.get(data_type, cls.RETENTION_PERIODS['application_data'])
        retention_period = timedelta(days=retention_days)
        
        if isinstance(created_date, str):
            created_date = datetime.fromisoformat(created_date)
        
        return timezone.now() - created_date < retention_period
    
    @classmethod
    def get_expired_data_query(cls, model_class, data_type, date_field='created_at'):
        """
        Get queryset for expired data that should be deleted
        
        Args:
            model_class: Django model class
            data_type: Type of data for retention policy
            date_field: Field name containing creation date
            
        Returns:
            QuerySet: Expired data queryset
        """
        retention_days = cls.RETENTION_PERIODS.get(data_type, cls.RETENTION_PERIODS['application_data'])
        cutoff_date = timezone.now() - timedelta(days=retention_days)
        
        filter_kwargs = {f"{date_field}__lt": cutoff_date}
        return model_class.objects.filter(**filter_kwargs)
    
    @classmethod
    def cleanup_expired_data(cls, model_class, data_type, date_field='created_at', batch_size=1000):
        """
        Clean up expired data in batches
        
        Args:
            model_class: Django model class
            data_type: Type of data for retention policy
            date_field: Field name containing creation date
            batch_size: Number of records to delete per batch
            
        Returns:
            int: Number of records deleted
        """
        expired_query = cls.get_expired_data_query(model_class, data_type, date_field)
        total_deleted = 0
        
        while True:
            # Get a batch of expired records
            expired_ids = list(expired_query.values_list('id', flat=True)[:batch_size])
            
            if not expired_ids:
                break
            
            # Delete the batch
            deleted_count = model_class.objects.filter(id__in=expired_ids).delete()[0]
            total_deleted += deleted_count
            
            logger.info(f"Deleted {deleted_count} expired {data_type} records")
        
        return total_deleted


class SecureDataHandler:
    """
    Secure data handling utilities
    """
    
    def __init__(self):
        self.encryptor = DataEncryption()
    
    def store_sensitive_field(self, model_instance, field_name, value):
        """
        Store sensitive data in encrypted form
        
        Args:
            model_instance: Django model instance
            field_name: Name of the field to encrypt
            value: Value to encrypt and store
        """
        if value:
            encrypted_value = self.encryptor.encrypt(str(value))
            setattr(model_instance, field_name, encrypted_value)
        else:
            setattr(model_instance, field_name, value)
    
    def retrieve_sensitive_field(self, model_instance, field_name):
        """
        Retrieve and decrypt sensitive data
        
        Args:
            model_instance: Django model instance
            field_name: Name of the field to decrypt
            
        Returns:
            str: Decrypted value
        """
        encrypted_value = getattr(model_instance, field_name)
        if encrypted_value:
            try:
                return self.encryptor.decrypt(encrypted_value)
            except Exception as e:
                logger.error(f"Failed to decrypt field {field_name}: {e}")
                return encrypted_value  # Return as-is if decryption fails
        return encrypted_value
    
    def sanitize_for_logging(self, data):
        """
        Sanitize data for safe logging (remove/mask PII)
        
        Args:
            data: Data to sanitize
            
        Returns:
            str: Sanitized data safe for logging
        """
        if isinstance(data, dict):
            sanitized = {}
            for key, value in data.items():
                if any(sensitive in key.lower() for sensitive in ['password', 'token', 'key', 'secret']):
                    sanitized[key] = '[REDACTED]'
                elif isinstance(value, str):
                    sanitized[key] = PIIProtection.mask_pii(value)
                else:
                    sanitized[key] = value
            return sanitized
        elif isinstance(data, str):
            return PIIProtection.mask_pii(data)
        else:
            return data


def encrypt_sensitive_data(field_name):
    """
    Decorator for model fields that should be encrypted
    """
    def decorator(func):
        def wrapper(self, value):
            if value:
                handler = SecureDataHandler()
                encrypted_value = handler.encryptor.encrypt(str(value))
                return func(self, encrypted_value)
            return func(self, value)
        return wrapper
    return decorator


def audit_data_access(action, resource_type, resource_id, user=None):
    """
    Audit data access for compliance
    
    Args:
        action: Type of action (read, write, delete, etc.)
        resource_type: Type of resource accessed
        resource_id: ID of the resource
        user: User performing the action
    """
    audit_entry = {
        'timestamp': timezone.now().isoformat(),
        'action': action,
        'resource_type': resource_type,
        'resource_id': resource_id,
        'user_id': user.id if user else None,
        'user_email': user.email if user else None,
    }
    
    # Log the audit entry
    logger.info(f"Data access audit: {audit_entry}")
    
    # In production, this could be sent to a separate audit system
    # or stored in a dedicated audit table
