"""
API security and access control utilities
"""
import json
import hashlib
import time
from datetime import datetime, timedelta
from functools import wraps
from django.http import JsonResponse
from django.core.cache import cache
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.core.exceptions import PermissionDenied
from django.utils import timezone
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class APIRateLimiter:
    """
    API rate limiting for different endpoint types
    """
    
    # Rate limits per hour for different API types
    RATE_LIMITS = {
        'auth': 10,      # Authentication endpoints
        'read': 1000,    # Read operations
        'write': 100,    # Write operations
        'upload': 20,    # File upload operations
        'email': 50,     # Email operations
        'ai': 30,        # AI processing operations
    }
    
    @classmethod
    def get_cache_key(cls, identifier, api_type):
        """
        Generate cache key for rate limiting
        """
        return f"api_rate_limit:{api_type}:{hashlib.md5(identifier.encode()).hexdigest()}"
    
    @classmethod
    def is_rate_limited(cls, identifier, api_type='read'):
        """
        Check if identifier is rate limited for API type
        
        Args:
            identifier: User ID, IP address, or API key
            api_type: Type of API operation
            
        Returns:
            tuple: (is_limited, remaining_requests, reset_time)
        """
        cache_key = cls.get_cache_key(identifier, api_type)
        limit = cls.RATE_LIMITS.get(api_type, cls.RATE_LIMITS['read'])
        
        # Get current request count
        current_count = cache.get(cache_key, 0)
        
        if current_count >= limit:
            # Get TTL for reset time
            ttl = cache.ttl(cache_key)
            reset_time = time.time() + ttl if ttl else time.time() + 3600
            return True, 0, reset_time
        
        return False, limit - current_count, time.time() + 3600
    
    @classmethod
    def record_request(cls, identifier, api_type='read'):
        """
        Record an API request
        """
        cache_key = cls.get_cache_key(identifier, api_type)
        
        # Increment counter with 1 hour expiry
        try:
            current_count = cache.get(cache_key, 0)
            cache.set(cache_key, current_count + 1, 3600)
        except Exception as e:
            logger.error(f"Failed to record API request: {e}")


class APIKeyManager:
    """
    API key management for external integrations
    """
    
    @classmethod
    def generate_api_key(cls, user_id, purpose='general'):
        """
        Generate a new API key for a user
        """
        import secrets
        import uuid
        
        # Generate secure random key
        key_data = f"{user_id}:{purpose}:{uuid.uuid4()}:{secrets.token_urlsafe(32)}"
        api_key = hashlib.sha256(key_data.encode()).hexdigest()
        
        # Store key metadata in cache (in production, use database)
        key_info = {
            'user_id': user_id,
            'purpose': purpose,
            'created_at': timezone.now().isoformat(),
            'last_used': None,
            'is_active': True
        }
        
        cache.set(f"api_key:{api_key}", key_info, 86400 * 365)  # 1 year
        
        return api_key
    
    @classmethod
    def validate_api_key(cls, api_key):
        """
        Validate an API key and return user info
        """
        if not api_key:
            return None
        
        key_info = cache.get(f"api_key:{api_key}")
        if not key_info or not key_info.get('is_active'):
            return None
        
        # Update last used timestamp
        key_info['last_used'] = timezone.now().isoformat()
        cache.set(f"api_key:{api_key}", key_info, 86400 * 365)
        
        return key_info
    
    @classmethod
    def revoke_api_key(cls, api_key):
        """
        Revoke an API key
        """
        key_info = cache.get(f"api_key:{api_key}")
        if key_info:
            key_info['is_active'] = False
            cache.set(f"api_key:{api_key}", key_info, 86400 * 365)
            return True
        return False


def api_rate_limit(api_type='read'):
    """
    Decorator for API rate limiting
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Get identifier (user ID or IP)
            if request.user.is_authenticated:
                identifier = str(request.user.id)
            else:
                identifier = request.META.get('REMOTE_ADDR', 'unknown')
            
            # Check rate limit
            is_limited, remaining, reset_time = APIRateLimiter.is_rate_limited(identifier, api_type)
            
            if is_limited:
                return JsonResponse({
                    'error': 'Rate limit exceeded',
                    'retry_after': int(reset_time - time.time())
                }, status=429)
            
            # Record the request
            APIRateLimiter.record_request(identifier, api_type)
            
            # Add rate limit headers to response
            response = view_func(request, *args, **kwargs)
            if hasattr(response, '__setitem__'):
                response['X-RateLimit-Limit'] = APIRateLimiter.RATE_LIMITS.get(api_type, 1000)
                response['X-RateLimit-Remaining'] = remaining - 1
                response['X-RateLimit-Reset'] = int(reset_time)
            
            return response
        return wrapper
    return decorator


def api_key_required(view_func):
    """
    Decorator to require API key authentication
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # Get API key from header or query parameter
        api_key = request.META.get('HTTP_X_API_KEY') or request.GET.get('api_key')
        
        if not api_key:
            return JsonResponse({
                'error': 'API key required',
                'message': 'Provide API key in X-API-Key header or api_key parameter'
            }, status=401)
        
        # Validate API key
        key_info = APIKeyManager.validate_api_key(api_key)
        if not key_info:
            return JsonResponse({
                'error': 'Invalid API key',
                'message': 'The provided API key is invalid or expired'
            }, status=401)
        
        # Add key info to request
        request.api_key_info = key_info
        
        return view_func(request, *args, **kwargs)
    return wrapper


def secure_api_endpoint(methods=['GET'], api_type='read', require_auth=True, require_api_key=False):
    """
    Comprehensive API security decorator
    
    Args:
        methods: Allowed HTTP methods
        api_type: Type of API for rate limiting
        require_auth: Whether to require user authentication
        require_api_key: Whether to require API key
    """
    def decorator(view_func):
        # Apply decorators in order
        secured_view = view_func
        
        # Rate limiting
        secured_view = api_rate_limit(api_type)(secured_view)
        
        # API key requirement
        if require_api_key:
            secured_view = api_key_required(secured_view)
        
        # Authentication requirement
        if require_auth:
            secured_view = login_required(secured_view)
        
        # HTTP method restriction
        secured_view = require_http_methods(methods)(secured_view)
        
        @wraps(secured_view)
        def wrapper(request, *args, **kwargs):
            # Additional security checks
            try:
                # Validate request content type for POST/PUT
                if request.method in ['POST', 'PUT', 'PATCH']:
                    content_type = request.META.get('CONTENT_TYPE', '')
                    if not content_type.startswith(('application/json', 'application/x-www-form-urlencoded', 'multipart/form-data')):
                        return JsonResponse({
                            'error': 'Invalid content type',
                            'message': 'Content-Type must be application/json, application/x-www-form-urlencoded, or multipart/form-data'
                        }, status=400)
                
                # Validate JSON for JSON requests
                if request.META.get('CONTENT_TYPE', '').startswith('application/json'):
                    try:
                        if hasattr(request, 'body') and request.body:
                            json.loads(request.body)
                    except json.JSONDecodeError:
                        return JsonResponse({
                            'error': 'Invalid JSON',
                            'message': 'Request body contains invalid JSON'
                        }, status=400)
                
                return secured_view(request, *args, **kwargs)
            
            except PermissionDenied as e:
                return JsonResponse({
                    'error': 'Permission denied',
                    'message': str(e)
                }, status=403)
            except Exception as e:
                logger.error(f"API endpoint error: {e}")
                return JsonResponse({
                    'error': 'Internal server error',
                    'message': 'An unexpected error occurred'
                }, status=500)
        
        return wrapper
    return decorator


class APIResponseFormatter:
    """
    Standardized API response formatting
    """
    
    @staticmethod
    def success(data=None, message=None, status=200):
        """
        Format successful API response
        """
        response_data = {
            'success': True,
            'timestamp': timezone.now().isoformat(),
        }
        
        if data is not None:
            response_data['data'] = data
        
        if message:
            response_data['message'] = message
        
        return JsonResponse(response_data, status=status)
    
    @staticmethod
    def error(message, error_code=None, details=None, status=400):
        """
        Format error API response
        """
        response_data = {
            'success': False,
            'error': message,
            'timestamp': timezone.now().isoformat(),
        }
        
        if error_code:
            response_data['error_code'] = error_code
        
        if details:
            response_data['details'] = details
        
        return JsonResponse(response_data, status=status)
    
    @staticmethod
    def paginated(data, page, per_page, total_count):
        """
        Format paginated API response
        """
        total_pages = (total_count + per_page - 1) // per_page
        
        response_data = {
            'success': True,
            'data': data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total_count': total_count,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_previous': page > 1
            },
            'timestamp': timezone.now().isoformat(),
        }
        
        return JsonResponse(response_data)


def validate_api_permissions(required_permissions):
    """
    Decorator to validate API permissions
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return JsonResponse({
                    'error': 'Authentication required',
                    'message': 'You must be logged in to access this endpoint'
                }, status=401)
            
            # Check user permissions
            for permission in required_permissions:
                if not request.user.has_perm(permission):
                    return JsonResponse({
                        'error': 'Insufficient permissions',
                        'message': f'You do not have the required permission: {permission}'
                    }, status=403)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator
