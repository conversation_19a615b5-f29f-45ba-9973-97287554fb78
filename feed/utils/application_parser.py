import re
import logging
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class PostJobFreeParser:
    """Simple parser for PostJobFree email applications."""

    def __init__(self):
        # Pattern to extract vacancy ID (e.g., "#Workloupe-36" -> 36)
        self.vacancy_id_pattern = r'#?Workloupe-(\d+)'
        # Pattern to extract candidate email from email body
        self.candidate_email_pattern = r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\s+applied\s+to'

    def parse_email_subject(self, subject: str) -> Dict[str, Optional[str]]:
        """
        Parse PostJobFree email subject line and extract vacancy ID.

        Args:
            subject (str): Email subject line

        Returns:
            dict: Parsed information with vacancy_id
        """
        result = {
            'vacancy_id': None
        }

        try:
            # Extract vacancy ID
            vacancy_match = re.search(self.vacancy_id_pattern, subject)
            if vacancy_match:
                result['vacancy_id'] = int(vacancy_match.group(1))
                logger.info(f"Extracted vacancy ID: {result['vacancy_id']}")
            else:
                logger.warning(f"Failed to find vacancy ID in subject: {subject}")
        except Exception as e:
            logger.error(f"Error parsing email subject: {str(e)}")

        return result

    def parse_email_body(self, email_body: str) -> Dict[str, Optional[str]]:
        """
        Parse PostJobFree email body to extract candidate information.

        Args:
            email_body (str): Email body content

        Returns:
            dict: Parsed information with candidate_email, job_title, location
        """
        result = {
            'candidate_email': None,
            'job_title': None,
            'location': None
        }

        if not email_body:
            return result

        try:
            # Extract candidate email from the first line
            email_match = re.search(self.candidate_email_pattern, email_body)
            if email_match:
                result['candidate_email'] = email_match.group(1).strip()
                logger.info(f"Extracted candidate email: {result['candidate_email']}")
            else:
                logger.warning(f"Failed to find candidate email in body")

            # Try to extract job title and location from the same line
            # Look for pattern like "email applied to JobTitle job in Location"
            full_pattern = r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\s+applied\s+to\s+(.+?)\s+job\s+in\s+(.+?)(?:\n|$)'
            full_match = re.search(full_pattern, email_body)
            if full_match:
                result['candidate_email'] = full_match.group(1).strip()
                result['job_title'] = full_match.group(2).strip()
                result['location'] = full_match.group(3).strip()
                logger.info(f"Extracted full info: email={result['candidate_email']}, job={result['job_title']}, location={result['location']}")

        except Exception as e:
            logger.error(f"Error parsing email body: {str(e)}")

        return result
