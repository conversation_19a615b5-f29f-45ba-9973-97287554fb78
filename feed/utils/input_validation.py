"""
Input validation and sanitization utilities for security
"""
import re
import html
import bleach
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.utils.html import strip_tags
import logging

logger = logging.getLogger(__name__)


class InputValidator:
    """
    Comprehensive input validation and sanitization class
    """
    
    # Allowed HTML tags for rich text content
    ALLOWED_HTML_TAGS = [
        'p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'blockquote', 'a', 'img', 'table', 'thead', 'tbody', 'tr', 'th', 'td'
    ]
    
    ALLOWED_HTML_ATTRIBUTES = {
        'a': ['href', 'title'],
        'img': ['src', 'alt', 'width', 'height'],
        'table': ['class'],
        'th': ['scope'],
        'td': ['colspan', 'rowspan']
    }
    
    # Dangerous patterns to detect
    DANGEROUS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'vbscript:',
        r'on\w+\s*=',
        r'eval\s*\(',
        r'expression\s*\(',
        r'url\s*\(',
        r'import\s*\(',
        r'@import',
        r'<iframe',
        r'<object',
        r'<embed',
        r'<link',
        r'<meta',
        r'<style',
        r'<base',
        r'<form',
        r'<input',
        r'<textarea',
        r'<select',
        r'<button',
    ]
    
    @classmethod
    def sanitize_html(cls, content, allow_tags=True):
        """
        Sanitize HTML content to prevent XSS attacks
        """
        if not content:
            return content
        
        if not allow_tags:
            return strip_tags(content)
        
        # Use bleach to sanitize HTML
        cleaned = bleach.clean(
            content,
            tags=cls.ALLOWED_HTML_TAGS,
            attributes=cls.ALLOWED_HTML_ATTRIBUTES,
            strip=True
        )
        
        return cleaned
    
    @classmethod
    def sanitize_text(cls, text):
        """
        Sanitize plain text input
        """
        if not text:
            return text
        
        # HTML escape the content
        sanitized = html.escape(text)
        
        # Check for dangerous patterns
        for pattern in cls.DANGEROUS_PATTERNS:
            if re.search(pattern, sanitized, re.IGNORECASE):
                logger.warning(f"Dangerous pattern detected in text input: {pattern}")
                raise ValidationError("Invalid content detected.")
        
        return sanitized
    
    @classmethod
    def validate_email_input(cls, email):
        """
        Validate email input
        """
        if not email:
            return email
        
        # Basic sanitization
        email = email.strip().lower()
        
        # Validate email format
        try:
            validate_email(email)
        except ValidationError:
            raise ValidationError("Invalid email format.")
        
        # Additional checks
        if len(email) > 254:  # RFC 5321 limit
            raise ValidationError("Email address too long.")
        
        return email
    
    @classmethod
    def validate_name_input(cls, name):
        """
        Validate name input (person names, company names, etc.)
        """
        if not name:
            return name
        
        # Sanitize
        name = cls.sanitize_text(name.strip())
        
        # Length validation
        if len(name) > 100:
            raise ValidationError("Name too long.")
        
        # Pattern validation - allow letters, spaces, hyphens, apostrophes
        if not re.match(r"^[a-zA-Z\s\-'\.]+$", name):
            raise ValidationError("Name contains invalid characters.")
        
        return name
    
    @classmethod
    def validate_phone_input(cls, phone):
        """
        Validate phone number input
        """
        if not phone:
            return phone
        
        # Remove common formatting
        phone = re.sub(r'[\s\-\(\)\+]', '', phone)
        
        # Validate format
        if not re.match(r'^\d{10,15}$', phone):
            raise ValidationError("Invalid phone number format.")
        
        return phone
    
    @classmethod
    def validate_url_input(cls, url):
        """
        Validate URL input
        """
        if not url:
            return url
        
        url = url.strip()
        
        # Basic URL validation
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if not url_pattern.match(url):
            raise ValidationError("Invalid URL format.")
        
        # Check for dangerous protocols
        if not url.startswith(('http://', 'https://')):
            raise ValidationError("Only HTTP and HTTPS URLs are allowed.")
        
        return url
    
    @classmethod
    def validate_file_name(cls, filename):
        """
        Validate file name for uploads
        """
        if not filename:
            return filename
        
        # Remove path components
        filename = filename.split('/')[-1].split('\\')[-1]
        
        # Length validation
        if len(filename) > 255:
            raise ValidationError("Filename too long.")
        
        # Character validation
        if not re.match(r'^[a-zA-Z0-9\-_\.\s]+$', filename):
            raise ValidationError("Filename contains invalid characters.")
        
        # Extension validation
        allowed_extensions = [
            '.pdf', '.doc', '.docx', '.txt', '.rtf',  # Documents
            '.jpg', '.jpeg', '.png', '.gif', '.bmp',  # Images
            '.zip', '.rar', '.7z'  # Archives
        ]
        
        file_ext = '.' + filename.split('.')[-1].lower() if '.' in filename else ''
        if file_ext not in allowed_extensions:
            raise ValidationError(f"File type {file_ext} not allowed.")
        
        return filename
    
    @classmethod
    def validate_json_input(cls, json_data):
        """
        Validate JSON input for dangerous content
        """
        if not json_data:
            return json_data
        
        # Convert to string for pattern checking
        json_str = str(json_data)
        
        # Check for dangerous patterns
        for pattern in cls.DANGEROUS_PATTERNS:
            if re.search(pattern, json_str, re.IGNORECASE):
                logger.warning(f"Dangerous pattern detected in JSON input: {pattern}")
                raise ValidationError("Invalid content detected in JSON data.")
        
        return json_data


def validate_form_data(form_data, field_validators=None):
    """
    Validate form data using specified validators

    Args:
        form_data: Dictionary of form data
        field_validators: Dictionary mapping field names to validator functions

    Returns:
        Dictionary of validated and sanitized data
    """
    if not field_validators:
        field_validators = {}

    validated_data = {}

    for field_name, value in form_data.items():
        try:
            if field_name in field_validators:
                validated_data[field_name] = field_validators[field_name](value)
            else:
                # Default sanitization
                if isinstance(value, str):
                    validated_data[field_name] = InputValidator.sanitize_text(value)
                else:
                    validated_data[field_name] = value
        except ValidationError as e:
            logger.warning(f"Validation error for field {field_name}: {e}")
            raise ValidationError(f"Invalid data in field {field_name}: {e}")

    return validated_data


def validate_input(field_validators=None):
    """
    Decorator to validate input data in views

    Args:
        field_validators: Dictionary mapping field names to validator functions

    Usage:
        @validate_input({
            'email': InputValidator.validate_email_input,
            'name': InputValidator.validate_name_input,
        })
        def my_view(request):
            # request.POST will be validated and sanitized
            pass
    """
    from functools import wraps
    from django.http import JsonResponse

    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            try:
                if request.method == 'POST' and hasattr(request, 'POST'):
                    validated_data = validate_form_data(request.POST.dict(), field_validators)
                    # Replace POST data with validated data
                    request._validated_post = validated_data

                if request.method == 'GET' and hasattr(request, 'GET'):
                    validated_data = validate_form_data(request.GET.dict(), field_validators)
                    request._validated_get = validated_data

                return view_func(request, *args, **kwargs)

            except ValidationError as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                }, status=400)

        return wrapper
    return decorator
