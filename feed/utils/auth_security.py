"""
Authentication and session security utilities
"""
import re
import hashlib
import time
from datetime import datetime, timedelta
from django.contrib.auth import authenticate, login
from django.contrib.auth.models import User
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.conf import settings
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


class PasswordValidator:
    """
    Enhanced password validation and security
    """
    
    MIN_LENGTH = 8
    MAX_LENGTH = 128
    
    @classmethod
    def validate_password_strength(cls, password):
        """
        Validate password strength according to security policies
        
        Requirements:
        - At least 8 characters long
        - Contains uppercase letter
        - Contains lowercase letter
        - Contains digit
        - Contains special character
        - Not a common password
        """
        errors = []
        
        if len(password) < cls.MIN_LENGTH:
            errors.append(f"Password must be at least {cls.MIN_LENGTH} characters long.")
        
        if len(password) > cls.MAX_LENGTH:
            errors.append(f"Password must be no more than {cls.MAX_LENGTH} characters long.")
        
        if not re.search(r'[A-Z]', password):
            errors.append("Password must contain at least one uppercase letter.")
        
        if not re.search(r'[a-z]', password):
            errors.append("Password must contain at least one lowercase letter.")
        
        if not re.search(r'\d', password):
            errors.append("Password must contain at least one digit.")
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("Password must contain at least one special character.")
        
        # Check for common passwords
        if cls.is_common_password(password):
            errors.append("Password is too common. Please choose a more unique password.")
        
        # Check for sequential characters
        if cls.has_sequential_characters(password):
            errors.append("Password should not contain sequential characters (e.g., 123, abc).")
        
        if errors:
            raise ValidationError(errors)
        
        return True
    
    @classmethod
    def is_common_password(cls, password):
        """
        Check if password is in common passwords list
        """
        common_passwords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey',
            'dragon', 'master', 'shadow', 'superman', 'michael',
            'football', 'baseball', 'liverpool', 'jordan', 'princess'
        ]
        
        return password.lower() in common_passwords
    
    @classmethod
    def has_sequential_characters(cls, password):
        """
        Check for sequential characters in password
        """
        # Check for sequential numbers
        for i in range(len(password) - 2):
            if password[i:i+3].isdigit():
                nums = [int(password[i+j]) for j in range(3)]
                if nums[1] == nums[0] + 1 and nums[2] == nums[1] + 1:
                    return True
        
        # Check for sequential letters
        for i in range(len(password) - 2):
            if password[i:i+3].isalpha():
                chars = password[i:i+3].lower()
                if ord(chars[1]) == ord(chars[0]) + 1 and ord(chars[2]) == ord(chars[1]) + 1:
                    return True
        
        return False


class AccountLockoutManager:
    """
    Account lockout and brute force protection
    """
    
    MAX_FAILED_ATTEMPTS = 5
    LOCKOUT_DURATION = 30 * 60  # 30 minutes in seconds
    ATTEMPT_WINDOW = 15 * 60  # 15 minutes window for counting attempts
    
    @classmethod
    def get_cache_key(cls, identifier):
        """
        Generate cache key for failed attempts
        """
        return f"failed_login_attempts:{hashlib.md5(identifier.encode()).hexdigest()}"
    
    @classmethod
    def get_lockout_key(cls, identifier):
        """
        Generate cache key for account lockout
        """
        return f"account_locked:{hashlib.md5(identifier.encode()).hexdigest()}"
    
    @classmethod
    def record_failed_attempt(cls, identifier):
        """
        Record a failed login attempt
        
        Args:
            identifier: Username, email, or IP address
        """
        cache_key = cls.get_cache_key(identifier)
        lockout_key = cls.get_lockout_key(identifier)
        
        # Get current failed attempts
        attempts = cache.get(cache_key, [])
        current_time = time.time()
        
        # Remove attempts outside the window
        attempts = [attempt_time for attempt_time in attempts 
                   if current_time - attempt_time < cls.ATTEMPT_WINDOW]
        
        # Add current attempt
        attempts.append(current_time)
        
        # Update cache
        cache.set(cache_key, attempts, cls.ATTEMPT_WINDOW)
        
        # Check if account should be locked
        if len(attempts) >= cls.MAX_FAILED_ATTEMPTS:
            cache.set(lockout_key, current_time, cls.LOCKOUT_DURATION)
            logger.warning(f"Account locked due to too many failed attempts: {identifier}")
            return True
        
        return False
    
    @classmethod
    def is_account_locked(cls, identifier):
        """
        Check if account is currently locked
        """
        lockout_key = cls.get_lockout_key(identifier)
        lockout_time = cache.get(lockout_key)
        
        if lockout_time:
            if time.time() - lockout_time < cls.LOCKOUT_DURATION:
                return True
            else:
                # Lockout expired, remove from cache
                cache.delete(lockout_key)
        
        return False
    
    @classmethod
    def clear_failed_attempts(cls, identifier):
        """
        Clear failed attempts for successful login
        """
        cache_key = cls.get_cache_key(identifier)
        lockout_key = cls.get_lockout_key(identifier)
        cache.delete(cache_key)
        cache.delete(lockout_key)
    
    @classmethod
    def get_remaining_lockout_time(cls, identifier):
        """
        Get remaining lockout time in seconds
        """
        lockout_key = cls.get_lockout_key(identifier)
        lockout_time = cache.get(lockout_key)
        
        if lockout_time:
            remaining = cls.LOCKOUT_DURATION - (time.time() - lockout_time)
            return max(0, remaining)
        
        return 0


class SecureAuthenticator:
    """
    Enhanced authentication with security features
    """
    
    @classmethod
    def authenticate_user(cls, request, username, password):
        """
        Secure user authentication with lockout protection
        
        Args:
            request: Django request object
            username: Username or email
            password: Password
            
        Returns:
            tuple: (user_object, success, error_message)
        """
        # Get client IP for rate limiting
        client_ip = cls.get_client_ip(request)
        
        # Check if account is locked (by username and IP)
        if AccountLockoutManager.is_account_locked(username):
            remaining_time = AccountLockoutManager.get_remaining_lockout_time(username)
            return None, False, f"Account locked. Try again in {int(remaining_time/60)} minutes."
        
        if AccountLockoutManager.is_account_locked(client_ip):
            remaining_time = AccountLockoutManager.get_remaining_lockout_time(client_ip)
            return None, False, f"Too many failed attempts from this IP. Try again in {int(remaining_time/60)} minutes."
        
        # Attempt authentication
        user = authenticate(request, username=username, password=password)
        
        if user is not None:
            if user.is_active:
                # Clear failed attempts on successful login
                AccountLockoutManager.clear_failed_attempts(username)
                AccountLockoutManager.clear_failed_attempts(client_ip)
                
                # Log successful login
                logger.info(f"Successful login for user: {username} from IP: {client_ip}")
                
                return user, True, None
            else:
                return None, False, "Account is disabled."
        else:
            # Record failed attempt
            AccountLockoutManager.record_failed_attempt(username)
            AccountLockoutManager.record_failed_attempt(client_ip)
            
            # Log failed attempt
            logger.warning(f"Failed login attempt for user: {username} from IP: {client_ip}")
            
            return None, False, "Invalid username or password."
    
    @classmethod
    def get_client_ip(cls, request):
        """
        Get client IP address from request
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    @classmethod
    def create_secure_session(cls, request, user):
        """
        Create a secure session for authenticated user
        """
        # Login the user
        login(request, user)
        
        # Set session security attributes
        request.session.set_expiry(settings.SESSION_COOKIE_AGE)
        request.session['login_time'] = timezone.now().isoformat()
        request.session['client_ip'] = cls.get_client_ip(request)
        request.session['user_agent'] = request.META.get('HTTP_USER_AGENT', '')[:200]
        
        # Regenerate session key for security
        request.session.cycle_key()
        
        logger.info(f"Secure session created for user: {user.username}")


class SessionSecurityManager:
    """
    Session security and validation
    """
    
    @classmethod
    def validate_session_security(cls, request):
        """
        Validate session security attributes
        """
        if not request.user.is_authenticated:
            return True  # No session to validate
        
        # Check if session has required security attributes
        if 'login_time' not in request.session:
            logger.warning("Session missing login_time")
            return False
        
        # Check session age
        login_time = datetime.fromisoformat(request.session['login_time'])
        if timezone.now() - login_time > timedelta(hours=24):
            logger.warning("Session expired due to age")
            return False
        
        # Check IP consistency (optional, can be disabled for mobile users)
        session_ip = request.session.get('client_ip')
        current_ip = SecureAuthenticator.get_client_ip(request)
        if session_ip and session_ip != current_ip:
            logger.warning(f"Session IP mismatch: {session_ip} vs {current_ip}")
            # Don't fail for IP mismatch as users may have dynamic IPs
            # return False
        
        return True
    
    @classmethod
    def refresh_session(cls, request):
        """
        Refresh session security attributes
        """
        if request.user.is_authenticated:
            request.session['last_activity'] = timezone.now().isoformat()
            request.session.set_expiry(settings.SESSION_COOKIE_AGE)


def require_secure_session(view_func):
    """
    Decorator to require secure session validation
    """
    from functools import wraps
    from django.contrib.auth import logout
    from django.shortcuts import redirect
    
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not SessionSecurityManager.validate_session_security(request):
            logout(request)
            return redirect('login')
        
        # Refresh session on activity
        SessionSecurityManager.refresh_session(request)
        
        return view_func(request, *args, **kwargs)
    
    return wrapper
