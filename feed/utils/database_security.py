"""
Database security utilities and query optimization
"""
import logging
from django.db import connection
from django.core.exceptions import PermissionDenied
from django.contrib.auth.models import User
from feed.models import Employee, Employer
from functools import wraps

logger = logging.getLogger(__name__)


class DatabaseSecurityManager:
    """
    Database security and access control manager
    """
    
    @staticmethod
    def validate_employer_access(user, employer_id):
        """
        Validate that a user has access to a specific employer's data
        
        Args:
            user: Django User object
            employer_id: Employer ID to check access for
            
        Returns:
            bool: True if user has access, False otherwise
            
        Raises:
            PermissionDenied: If user doesn't have access
        """
        if not user.is_authenticated:
            raise PermissionDenied("User not authenticated")
        
        try:
            employee = Employee.objects.get(user=user)
            if str(employee.employer_id.employer_id) != str(employer_id):
                logger.warning(f"User {user.id} attempted to access employer {employer_id} data without permission")
                raise PermissionDenied("Access denied to employer data")
            return True
        except Employee.DoesNotExist:
            logger.warning(f"User {user.id} has no employee record")
            raise PermissionDenied("No employee record found")
    
    @staticmethod
    def validate_application_access(user, application_id):
        """
        Validate that a user has access to a specific application
        """
        if not user.is_authenticated:
            raise PermissionDenied("User not authenticated")
        
        try:
            from feed.models import Application
            employee = Employee.objects.get(user=user)
            application = Application.objects.select_related('vacancy_id').get(
                application_id=application_id
            )
            
            if application.vacancy_id.employer_id != employee.employer_id:
                logger.warning(f"User {user.id} attempted to access application {application_id} without permission")
                raise PermissionDenied("Access denied to application data")
            return True
        except (Employee.DoesNotExist, Application.DoesNotExist):
            raise PermissionDenied("Access denied")
    
    @staticmethod
    def validate_vacancy_access(user, vacancy_id):
        """
        Validate that a user has access to a specific vacancy
        """
        if not user.is_authenticated:
            raise PermissionDenied("User not authenticated")
        
        try:
            from feed.models import Vacancy
            employee = Employee.objects.get(user=user)
            vacancy = Vacancy.objects.get(vacancy_id=vacancy_id)
            
            if vacancy.employer_id != employee.employer_id:
                logger.warning(f"User {user.id} attempted to access vacancy {vacancy_id} without permission")
                raise PermissionDenied("Access denied to vacancy data")
            return True
        except (Employee.DoesNotExist, Vacancy.DoesNotExist):
            raise PermissionDenied("Access denied")
    
    @staticmethod
    def get_user_employer_id(user):
        """
        Get the employer ID for a user
        """
        if not user.is_authenticated:
            return None
        
        try:
            employee = Employee.objects.get(user=user)
            return employee.employer_id.employer_id
        except Employee.DoesNotExist:
            return None
    
    @staticmethod
    def execute_safe_query(query, params=None, employer_id=None):
        """
        Execute a raw SQL query with additional security checks
        
        Args:
            query: SQL query string with parameterized placeholders
            params: List of parameters for the query
            employer_id: Optional employer ID for additional filtering
            
        Returns:
            Query results
        """
        if params is None:
            params = []
        
        # Log the query for security auditing
        logger.info(f"Executing query with {len(params)} parameters")
        
        # Validate query doesn't contain dangerous patterns
        dangerous_patterns = [
            'DROP ', 'DELETE ', 'UPDATE ', 'INSERT ', 'ALTER ', 'CREATE ',
            'TRUNCATE ', 'EXEC ', 'EXECUTE ', 'UNION ', '--', '/*', '*/',
            'xp_', 'sp_', 'INFORMATION_SCHEMA', 'sys.', 'master.',
        ]
        
        query_upper = query.upper()
        for pattern in dangerous_patterns:
            if pattern in query_upper and not query_upper.startswith('SELECT'):
                logger.error(f"Dangerous pattern detected in query: {pattern}")
                raise PermissionDenied("Query contains dangerous operations")
        
        # Execute the query
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            if query_upper.strip().startswith('SELECT'):
                return cursor.fetchall()
            else:
                return cursor.rowcount


def require_employer_access(employer_param='employer_id'):
    """
    Decorator to require employer access validation
    
    Args:
        employer_param: Name of the parameter containing employer ID
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Get employer ID from various sources
            employer_id = None
            
            # Try to get from URL parameters
            if employer_param in kwargs:
                employer_id = kwargs[employer_param]
            # Try to get from request data
            elif hasattr(request, 'POST') and employer_param in request.POST:
                employer_id = request.POST[employer_param]
            elif hasattr(request, 'GET') and employer_param in request.GET:
                employer_id = request.GET[employer_param]
            # Try to get from user's employee record
            else:
                employer_id = DatabaseSecurityManager.get_user_employer_id(request.user)
            
            if employer_id:
                DatabaseSecurityManager.validate_employer_access(request.user, employer_id)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def require_application_access(application_param='application_id'):
    """
    Decorator to require application access validation
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Get application ID from various sources
            application_id = None
            
            if application_param in kwargs:
                application_id = kwargs[application_param]
            elif hasattr(request, 'POST') and application_param in request.POST:
                application_id = request.POST[application_param]
            elif hasattr(request, 'GET') and application_param in request.GET:
                application_id = request.GET[application_param]
            
            if application_id:
                DatabaseSecurityManager.validate_application_access(request.user, application_id)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def require_vacancy_access(vacancy_param='vacancy_id'):
    """
    Decorator to require vacancy access validation
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Get vacancy ID from various sources
            vacancy_id = None
            
            if vacancy_param in kwargs:
                vacancy_id = kwargs[vacancy_param]
            elif hasattr(request, 'POST') and vacancy_param in request.POST:
                vacancy_id = request.POST[vacancy_param]
            elif hasattr(request, 'GET') and vacancy_param in request.GET:
                vacancy_id = request.GET[vacancy_param]
            
            if vacancy_id:
                DatabaseSecurityManager.validate_vacancy_access(request.user, vacancy_id)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


class QueryOptimizer:
    """
    Database query optimization utilities
    """
    
    @staticmethod
    def optimize_application_queries():
        """
        Suggestions for optimizing application-related queries
        """
        optimizations = [
            "Add index on feed_application.vacancy_id for faster joins",
            "Add index on feed_application.application_state for filtering",
            "Add index on feed_application.created_at for date-based queries",
            "Add composite index on (vacancy_id, application_state) for common filters",
            "Consider partitioning large tables by date or employer_id",
        ]
        return optimizations
    
    @staticmethod
    def get_query_performance_tips():
        """
        General query performance tips
        """
        tips = [
            "Use select_related() for foreign key relationships",
            "Use prefetch_related() for many-to-many and reverse foreign key relationships",
            "Use only() to limit fields retrieved from database",
            "Use defer() to exclude large fields when not needed",
            "Use exists() instead of count() when checking for existence",
            "Use iterator() for processing large querysets",
            "Use bulk_create() and bulk_update() for batch operations",
            "Avoid N+1 queries by using proper prefetching",
        ]
        return tips
    
    @staticmethod
    def analyze_slow_queries():
        """
        Analyze and suggest improvements for slow queries
        """
        # This would typically connect to database logs or monitoring tools
        # For now, return general recommendations
        recommendations = [
            "Enable query logging in Django settings for development",
            "Use django-debug-toolbar to identify slow queries",
            "Monitor database performance with tools like pg_stat_statements",
            "Consider adding database connection pooling",
            "Implement query result caching for frequently accessed data",
        ]
        return recommendations


def audit_database_access(user, action, resource_type, resource_id):
    """
    Audit database access for security monitoring
    
    Args:
        user: User performing the action
        action: Type of action (read, write, delete, etc.)
        resource_type: Type of resource (application, vacancy, etc.)
        resource_id: ID of the resource
    """
    logger.info(
        f"Database access audit: User {user.id} performed {action} on {resource_type} {resource_id}"
    )
    
    # In a production environment, this could write to a separate audit log
    # or send to a security monitoring system
