from django.contrib import admin
from .models import *
from djangoql.admin import DjangoQLSearchMixin

@admin.register(Candidate)
class CandidateAdmin(DjangoQLSearchMixin, admin.ModelAdmin):
    list_display = ('candidate_firstname', 'candidate_lastname', 'candidate_email', 'candidate_phone', 'candidate_address', 'candidate_date_of_birth')
    search_fields = ('candidate_firstname', 'candidate_lastname', 'candidate_email')
    list_filter = ('candidate_date_of_birth',)

@admin.register(Vacancy)
class VacancyAdmin(DjangoQLSearchMixin, admin.ModelAdmin):
    list_display = ('vacancy_title', 'vacancy_bus_unit', 'number_of_applicants_temp', 'vacancy_country',
                     'vacancy_city', 'vacancy_creation_date', 'vacancy_status', 
                       'work_schedule', 'office_schedule', 'salary_min', 'salary_max', 'salary_currency',
                         'job_portals', 'jobtags', 'employer_id')
    readonly_fields = ('vacancy_creation_date',)
    ordering = ('-vacancy_creation_date',)
    search_fields = ('vacancy_title', 'vacancy_country', 'vacancy_city')
    list_filter = ('vacancy_status', 'vacancy_bus_unit', 'vacancy_country')
    date_hierarchy = 'vacancy_creation_date'

@admin.register(TalentPool)
class TalentPoolAdmin(DjangoQLSearchMixin, admin.ModelAdmin):
    list_display = ('talent_id', 'talent_firstname', 'talent_lastname', 'talent_email', 'talent_phone', 'talent_country', 'talent_status', 'cv_location', 'talent_added_at')
    search_fields = ('talent_firstname', 'talent_lastname', 'talent_email', 'talent_phone')
    list_filter = ('talent_status', 'talent_country')
    date_hierarchy = 'talent_added_at'

@admin.register(Employer)
class EmployerAdmin(DjangoQLSearchMixin, admin.ModelAdmin):
    list_display = ('employer_name', 'employer_email', 'employer_phone', 'employer_address', 'office_locations',
                    'employer_created_at', 'employer_logo_url', 'employer_banner_url', 'employer_website',
                    'employer_description', 'employer_industry', 'employer_headcount', 'employer_social_portals',
                    'employer_status')
    search_fields = ('employer_name', 'employer_email')
    list_filter = ('employer_status',)
    date_hierarchy = 'employer_created_at'

@admin.register(Application)
class ApplicationAdmin(DjangoQLSearchMixin, admin.ModelAdmin):
    list_display = ('application_id', 'application_source', 'application_status', 'application_state', 'education_level', 'notice_period', 'current_position', 'current_employer', 'total_exp_years', 'application_date', 'candidate_id', 'vacancy_id')
    search_fields = ('application_id', 'current_position', 'current_employer')
    list_filter = ('application_status', 'application_state', 'education_level', 'application_source')
    date_hierarchy = 'application_date'
    
@admin.register(ApplicationState)
class ApplicationStateAdmin(DjangoQLSearchMixin, admin.ModelAdmin):
    list_display = ('state_id', 'state_name', 'state_notes', 'state_started_at', 'application_id', 'committed_by')
    search_fields = ('state_name', 'state_notes')
    list_filter = ('state_name', 'committed_by')
    date_hierarchy = 'state_started_at'


@admin.register(ApplicationComment)
class ApplicationCommentAdmin(DjangoQLSearchMixin, admin.ModelAdmin):
    list_display = ('comment_id', 'commented_by', 'comment_body', 'comment_date', 'application_id')
    search_fields = ('comment_body',)
    list_filter = ('commented_by', 'comment_date')
    date_hierarchy = 'comment_date'

@admin.register(JobTemplate)
class JobTemplateAdmin(admin.ModelAdmin):
    list_display = ('title', 'description', 'created_at', 'updated_at', 'usage_count')
    search_fields = ('title', 'description')
    list_filter = ('created_at', 'updated_at')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(Invitation)
class InvitationAdmin(DjangoQLSearchMixin, admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'email', 'role', 'invitation_status', 'sent_date', 'expiry_date')
    search_fields = ('first_name', 'last_name', 'email')
    list_filter = ('role', 'invitation_status')
    date_hierarchy = 'sent_date'

@admin.register(Appointment)
class AppointmentAdmin(admin.ModelAdmin):
    list_display = ('title', 'created_by', 'appointment_kind', 'start_time', 'end_time', 'meeting_status', 'interviewers', 'invited_candidates')
    search_fields = ('title', 'appointment_kind')
    list_filter = ('appointment_kind', 'created_at')
    date_hierarchy = 'start_time'
    ordering = ('start_time',) 

@admin.register(PotentialEmployer)
class PotentialEmployerAdmin(DjangoQLSearchMixin, admin.ModelAdmin):
    list_display = ('id','company_name', 'contact_name', 'email', 'phone', 'website', 'status', 'created_at')
    search_fields = ('company_name', 'contact_name', 'email')
    list_filter = ('status',)
    date_hierarchy = 'created_at'

@admin.register(TalentRequest)
class TalentRequestAdmin(DjangoQLSearchMixin, admin.ModelAdmin):
    list_display = ('id', 'employer_id', 'vacancy_id', 'committed_by', 'request_date', 'status')
    list_filter = ('status', 'request_date')
    date_hierarchy = 'request_date'

@admin.register(WorkSchedule)
class WorkScheduleAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'created_at', 'updated_at')
    search_fields = ('name', 'description')
    list_filter = ('created_at',)

@admin.register(OfficeSchedule)
class OfficeScheduleAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'created_at', 'updated_at')
    search_fields = ('name', 'description')
    list_filter = ('created_at',)

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'created_at', 'updated_at')
    search_fields = ('name', 'description')
    list_filter = ('created_at',)

@admin.register(OfficeLocation)
class OfficeLocationAdmin(admin.ModelAdmin):
    list_display = ('country', 'city', 'created_at', 'updated_at')
    search_fields = ('country', 'city')
    list_filter = ('country', 'city', 'created_at')

@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ('user', 'employer_id', 'role', 'permissions', 'status', 'created_at')
    search_fields = ('user', 'employer_id', 'role', 'permissions', 'status', 'created_at')
    list_filter = ('user', 'employer_id', 'role', 'permissions', 'status', 'created_at')
    date_hierarchy = 'created_at'

#add ApplicationCvText model to admin
@admin.register(ApplicationCvText)
class ApplicationCvTextAdmin(admin.ModelAdmin):
    list_display = ('application_id', 'cv_text', 'is_cv_analyzed', 'ai_analysis_result')
    search_fields = ('application_id',)
    list_filter = ('is_cv_analyzed',)

@admin.register(PortalConfigurations)
class PortalConfigurationsAdmin(admin.ModelAdmin):
    list_display = ('portal_name', 'portal_status')
    search_fields = ('portal_name',)
    list_filter = ('portal_status',)
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
