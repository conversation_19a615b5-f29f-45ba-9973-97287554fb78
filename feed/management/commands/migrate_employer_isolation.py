from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from feed.models import <PERSON>T<PERSON><PERSON>, TalentPool, Invitation, Employer, Employee


class Command(BaseCommand):
    help = 'Migrate existing data to include employer isolation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--default-employer-id',
            type=int,
            help='Default employer ID to assign to existing records',
            default=1
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be migrated without making changes'
        )

    def handle(self, *args, **options):
        default_employer_id = options['default_employer_id']
        dry_run = options['dry_run']
        
        self.stdout.write(self.style.SUCCESS('=== Employer Isolation Migration ==='))
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Get default employer
        try:
            default_employer = Employer.objects.get(employer_id=default_employer_id)
            self.stdout.write(f'Using default employer: {default_employer.employer_name} (ID: {default_employer.employer_id})')
        except Employer.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'Employer with ID {default_employer_id} not found!'))
            self.stdout.write('Available employers:')
            for emp in Employer.objects.all():
                self.stdout.write(f'  - {emp.employer_name} (ID: {emp.employer_id})')
            return
        
        # Get default user (first superuser or first user)
        try:
            default_user = User.objects.filter(is_superuser=True).first()
            if not default_user:
                default_user = User.objects.first()
            
            if not default_user:
                self.stdout.write(self.style.ERROR('No users found! Create a user first.'))
                return
                
            self.stdout.write(f'Using default user: {default_user.username} ({default_user.email})')
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error finding default user: {e}'))
            return
        
        # Migrate JobTemplate records
        self.stdout.write('\n--- Migrating JobTemplate records ---')
        job_templates = JobTemplate.objects.filter(employer_id__isnull=True)
        self.stdout.write(f'Found {job_templates.count()} JobTemplate records without employer_id')
        
        if not dry_run:
            for template in job_templates:
                template.employer_id = default_employer
                template.created_by = default_user
                template.save()
                self.stdout.write(f'  ✅ Updated template: {template.title}')
        else:
            for template in job_templates:
                self.stdout.write(f'  📋 Would update template: {template.title}')
        
        # Migrate TalentPool records
        self.stdout.write('\n--- Migrating TalentPool records ---')
        talent_pool = TalentPool.objects.filter(employer_id__isnull=True)
        self.stdout.write(f'Found {talent_pool.count()} TalentPool records without employer_id')
        
        if not dry_run:
            for talent in talent_pool:
                talent.employer_id = default_employer
                talent.added_by = default_user
                talent.save()
                self.stdout.write(f'  ✅ Updated talent: {talent.talent_firstname} {talent.talent_lastname}')
        else:
            for talent in talent_pool:
                self.stdout.write(f'  📋 Would update talent: {talent.talent_firstname} {talent.talent_lastname}')
        
        # Migrate Invitation records
        self.stdout.write('\n--- Migrating Invitation records ---')
        invitations = Invitation.objects.filter(employer_id__isnull=True)
        self.stdout.write(f'Found {invitations.count()} Invitation records without employer_id')
        
        if not dry_run:
            for invitation in invitations:
                invitation.employer_id = default_employer
                invitation.invited_by = default_user
                invitation.save()
                self.stdout.write(f'  ✅ Updated invitation: {invitation.email}')
        else:
            for invitation in invitations:
                self.stdout.write(f'  📋 Would update invitation: {invitation.email}')
        
        # Summary
        self.stdout.write(self.style.SUCCESS('\n=== Migration Summary ==='))
        if dry_run:
            self.stdout.write(f'📋 Would migrate:')
            self.stdout.write(f'  - {JobTemplate.objects.filter(employer_id__isnull=True).count()} JobTemplate records')
            self.stdout.write(f'  - {TalentPool.objects.filter(employer_id__isnull=True).count()} TalentPool records')
            self.stdout.write(f'  - {Invitation.objects.filter(employer_id__isnull=True).count()} Invitation records')
            self.stdout.write('\nRun without --dry-run to apply changes')
        else:
            self.stdout.write(f'✅ Successfully migrated:')
            self.stdout.write(f'  - {job_templates.count()} JobTemplate records')
            self.stdout.write(f'  - {talent_pool.count()} TalentPool records')
            self.stdout.write(f'  - {invitations.count()} Invitation records')
            self.stdout.write(f'\nAll records now have employer isolation!')
        
        # Check for any remaining issues
        self.stdout.write('\n--- Final Validation ---')
        remaining_templates = JobTemplate.objects.filter(employer_id__isnull=True).count()
        remaining_talents = TalentPool.objects.filter(employer_id__isnull=True).count()
        remaining_invitations = Invitation.objects.filter(employer_id__isnull=True).count()
        
        if remaining_templates + remaining_talents + remaining_invitations == 0:
            self.stdout.write(self.style.SUCCESS('✅ All records have employer isolation!'))
        else:
            self.stdout.write(self.style.WARNING(f'⚠️  Still missing employer_id:'))
            if remaining_templates:
                self.stdout.write(f'  - {remaining_templates} JobTemplate records')
            if remaining_talents:
                self.stdout.write(f'  - {remaining_talents} TalentPool records')
            if remaining_invitations:
                self.stdout.write(f'  - {remaining_invitations} Invitation records')
