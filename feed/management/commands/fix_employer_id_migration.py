from django.core.management.base import BaseCommand
from django.db import connection


class Command(BaseCommand):
    help = 'Fix employer_id migration by handling database views manually'

    def add_arguments(self, parser):
        parser.add_argument(
            '--step',
            type=str,
            choices=['check', 'drop-views', 'recreate-views', 'all'],
            default='check',
            help='Step to execute: check, drop-views, recreate-views, or all'
        )

    def handle(self, *args, **options):
        step = options['step']
        
        if step in ['check', 'all']:
            self.check_views()
        
        if step in ['drop-views', 'all']:
            self.drop_views()
        
        if step in ['recreate-views', 'all']:
            self.recreate_views()

    def check_views(self):
        """Check what views exist that might depend on employer_id"""
        self.stdout.write(self.style.SUCCESS('=== Checking Database Views ==='))
        
        with connection.cursor() as cursor:
            # Check for views
            cursor.execute("""
                SELECT schemaname, viewname, definition 
                FROM pg_views 
                WHERE schemaname = 'public' 
                AND definition LIKE '%employer_id%';
            """)
            
            views = cursor.fetchall()
            
            if views:
                self.stdout.write(f'Found {len(views)} views that reference employer_id:')
                for schema, view_name, definition in views:
                    self.stdout.write(f'  - {view_name}')
                    # Show first 100 chars of definition
                    short_def = definition[:100] + '...' if len(definition) > 100 else definition
                    self.stdout.write(f'    Definition: {short_def}')
            else:
                self.stdout.write('No views found that reference employer_id')
            
            # Check for rules
            cursor.execute("""
                SELECT schemaname, tablename, rulename 
                FROM pg_rules 
                WHERE schemaname = 'public';
            """)
            
            rules = cursor.fetchall()
            if rules:
                self.stdout.write(f'\nFound {len(rules)} rules:')
                for schema, table_name, rule_name in rules:
                    self.stdout.write(f'  - {rule_name} on {table_name}')
            else:
                self.stdout.write('\nNo rules found')

    def drop_views(self):
        """Drop views that depend on employer_id"""
        self.stdout.write(self.style.WARNING('=== Dropping Views ==='))
        
        views_to_drop = [
            'employer_cards',
            # Add other view names here if needed
        ]
        
        with connection.cursor() as cursor:
            for view_name in views_to_drop:
                try:
                    cursor.execute(f"DROP VIEW IF EXISTS {view_name} CASCADE;")
                    self.stdout.write(f'✅ Dropped view: {view_name}')
                except Exception as e:
                    self.stdout.write(f'❌ Error dropping view {view_name}: {e}')

    def recreate_views(self):
        """Recreate views with updated schema"""
        self.stdout.write(self.style.SUCCESS('=== Recreating Views ==='))
        
        # Employer cards view
        employer_cards_sql = """
            CREATE OR REPLACE VIEW employer_cards AS
            SELECT 
                e.employer_id,
                e.employer_name,
                e.employer_email,
                e.employer_website,
                e.employer_description,
                COUNT(v.vacancy_id) as total_vacancies,
                COUNT(CASE WHEN v.vacancy_status = 'Active' THEN 1 END) as active_vacancies,
                COUNT(CASE WHEN v.vacancy_status = 'Draft' THEN 1 END) as draft_vacancies,
                MAX(v.vacancy_creation_date) as latest_vacancy_date
            FROM feed_employer e
            LEFT JOIN feed_vacancy v ON e.employer_id = v.employer_id
            GROUP BY e.employer_id, e.employer_name, e.employer_email, e.employer_website, e.employer_description;
        """
        
        views_to_create = [
            ('employer_cards', employer_cards_sql),
            # Add other views here if needed
        ]
        
        with connection.cursor() as cursor:
            for view_name, sql in views_to_create:
                try:
                    cursor.execute(sql)
                    self.stdout.write(f'✅ Created view: {view_name}')
                except Exception as e:
                    self.stdout.write(f'❌ Error creating view {view_name}: {e}')
                    self.stdout.write(f'   SQL: {sql[:100]}...')

    def get_table_info(self):
        """Get information about the current table structure"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'feed_vacancy' 
                AND column_name = 'employer_id';
            """)
            
            result = cursor.fetchone()
            if result:
                column_name, data_type, is_nullable = result
                self.stdout.write(f'Current employer_id column: {data_type}, nullable: {is_nullable}')
            else:
                self.stdout.write('employer_id column not found in feed_vacancy table')
