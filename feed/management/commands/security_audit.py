"""
Django management command for security auditing
"""
import os
import re
from django.core.management.base import BaseCommand
from django.conf import settings
from django.db import connection
from django.contrib.auth.models import User
from feed.models import Employee, Employer, Application
from feed.utils.data_protection import DataRetentionManager
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Run comprehensive security audit of the Canvider application'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--check-type',
            type=str,
            choices=['all', 'config', 'data', 'permissions', 'cleanup'],
            default='all',
            help='Type of security check to run'
        )
        
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Attempt to fix issues automatically where possible'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Verbose output'
        )
    
    def handle(self, *args, **options):
        self.verbosity = options['verbosity']
        self.fix_issues = options['fix']
        check_type = options['check_type']
        
        self.stdout.write(
            self.style.SUCCESS('Starting Canvider Security Audit...\n')
        )
        
        issues_found = 0
        
        if check_type in ['all', 'config']:
            issues_found += self.check_configuration_security()
        
        if check_type in ['all', 'data']:
            issues_found += self.check_data_security()
        
        if check_type in ['all', 'permissions']:
            issues_found += self.check_permissions_security()
        
        if check_type in ['all', 'cleanup']:
            issues_found += self.check_data_retention()
        
        # Summary
        if issues_found == 0:
            self.stdout.write(
                self.style.SUCCESS(f'\n✅ Security audit completed successfully! No issues found.')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'\n⚠️  Security audit completed with {issues_found} issues found.')
            )
    
    def check_configuration_security(self):
        """
        Check Django configuration security
        """
        self.stdout.write('\n🔧 Checking Configuration Security...')
        issues = 0
        
        # Check DEBUG setting
        if settings.DEBUG:
            self.stdout.write(
                self.style.WARNING('  ⚠️  DEBUG is enabled - should be False in production')
            )
            issues += 1
        else:
            self.stdout.write('  ✅ DEBUG is properly disabled')
        
        # Check SECRET_KEY
        if hasattr(settings, 'SECRET_KEY'):
            if len(settings.SECRET_KEY) < 50:
                self.stdout.write(
                    self.style.WARNING('  ⚠️  SECRET_KEY is too short')
                )
                issues += 1
            elif 'django-insecure' in settings.SECRET_KEY:
                self.stdout.write(
                    self.style.WARNING('  ⚠️  SECRET_KEY appears to be a default Django key')
                )
                issues += 1
            else:
                self.stdout.write('  ✅ SECRET_KEY appears secure')
        
        # Check ALLOWED_HOSTS
        if not settings.ALLOWED_HOSTS or settings.ALLOWED_HOSTS == ['*']:
            self.stdout.write(
                self.style.WARNING('  ⚠️  ALLOWED_HOSTS is not properly configured')
            )
            issues += 1
        else:
            self.stdout.write('  ✅ ALLOWED_HOSTS is configured')
        
        # Check security middleware
        required_middleware = [
            'django.middleware.security.SecurityMiddleware',
            'django.middleware.csrf.CsrfViewMiddleware',
            'django.contrib.auth.middleware.AuthenticationMiddleware',
        ]
        
        for middleware in required_middleware:
            if middleware not in settings.MIDDLEWARE:
                self.stdout.write(
                    self.style.WARNING(f'  ⚠️  Missing security middleware: {middleware}')
                )
                issues += 1
        
        if issues == 0:
            self.stdout.write('  ✅ All security middleware present')
        
        # Check security headers settings
        security_settings = [
            'SECURE_BROWSER_XSS_FILTER',
            'SECURE_CONTENT_TYPE_NOSNIFF',
            'X_FRAME_OPTIONS',
            'SECURE_HSTS_SECONDS',
        ]
        
        for setting in security_settings:
            if not getattr(settings, setting, None):
                self.stdout.write(
                    self.style.WARNING(f'  ⚠️  Security setting not configured: {setting}')
                )
                issues += 1
        
        return issues
    
    def check_data_security(self):
        """
        Check data security and encryption
        """
        self.stdout.write('\n🔒 Checking Data Security...')
        issues = 0
        
        # Check for users with weak passwords
        weak_password_users = []
        for user in User.objects.all()[:100]:  # Limit to first 100 users
            if user.password and len(user.password) < 20:  # Hashed passwords should be longer
                weak_password_users.append(user.username)
        
        if weak_password_users:
            self.stdout.write(
                self.style.WARNING(f'  ⚠️  {len(weak_password_users)} users may have weak passwords')
            )
            issues += len(weak_password_users)
        else:
            self.stdout.write('  ✅ User passwords appear properly hashed')
        
        # Check for sensitive data in logs
        log_files = [
            '/var/log/django.log',
            'logs/django.log',
            'canvider.log'
        ]
        
        for log_file in log_files:
            if os.path.exists(log_file):
                if self.check_log_file_for_sensitive_data(log_file):
                    self.stdout.write(
                        self.style.WARNING(f'  ⚠️  Sensitive data found in log file: {log_file}')
                    )
                    issues += 1
        
        # Check database connection security
        db_config = connection.settings_dict
        if 'sslmode' not in db_config.get('OPTIONS', {}):
            self.stdout.write(
                self.style.WARNING('  ⚠️  Database connection may not use SSL')
            )
            issues += 1
        
        return issues
    
    def check_permissions_security(self):
        """
        Check role-based access control and permissions
        """
        self.stdout.write('\n👥 Checking Permissions Security...')
        issues = 0
        
        # Check for users without employee records
        users_without_employees = User.objects.exclude(
            id__in=Employee.objects.values_list('user_id', flat=True)
        ).count()
        
        if users_without_employees > 0:
            self.stdout.write(
                self.style.WARNING(f'  ⚠️  {users_without_employees} users without employee records')
            )
            issues += users_without_employees
        
        # Check for inactive users with active sessions
        # This would require session store analysis in a real implementation
        
        # Check for orphaned data
        orphaned_applications = Application.objects.filter(
            vacancy_id__isnull=True
        ).count()
        
        if orphaned_applications > 0:
            self.stdout.write(
                self.style.WARNING(f'  ⚠️  {orphaned_applications} orphaned applications found')
            )
            issues += 1
        
        return issues
    
    def check_data_retention(self):
        """
        Check data retention policies and cleanup
        """
        self.stdout.write('\n🗂️  Checking Data Retention...')
        issues = 0
        
        # Check for expired application data
        expired_applications = DataRetentionManager.get_expired_data_query(
            Application, 'application_data', 'created_at'
        ).count()
        
        if expired_applications > 0:
            self.stdout.write(
                self.style.WARNING(f'  ⚠️  {expired_applications} expired applications need cleanup')
            )
            
            if self.fix_issues:
                deleted_count = DataRetentionManager.cleanup_expired_data(
                    Application, 'application_data', 'created_at'
                )
                self.stdout.write(
                    self.style.SUCCESS(f'  ✅ Cleaned up {deleted_count} expired applications')
                )
            else:
                issues += 1
        else:
            self.stdout.write('  ✅ No expired applications found')
        
        # Check for old session data
        # This would require session store analysis
        
        # Check for temporary files
        temp_dirs = [
            '/tmp/canvider',
            'temp/',
            'uploads/temp/'
        ]
        
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                old_files = self.find_old_files(temp_dir, days=7)
                if old_files:
                    self.stdout.write(
                        self.style.WARNING(f'  ⚠️  {len(old_files)} old temporary files in {temp_dir}')
                    )
                    
                    if self.fix_issues:
                        for file_path in old_files:
                            try:
                                os.remove(file_path)
                            except OSError:
                                pass
                        self.stdout.write(
                            self.style.SUCCESS(f'  ✅ Cleaned up old files in {temp_dir}')
                        )
                    else:
                        issues += 1
        
        return issues
    
    def check_log_file_for_sensitive_data(self, log_file):
        """
        Check log file for sensitive data patterns
        """
        sensitive_patterns = [
            r'password\s*[:=]\s*[^\s]+',
            r'token\s*[:=]\s*[^\s]+',
            r'secret\s*[:=]\s*[^\s]+',
            r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',  # Credit card
            r'\b\d{3}-\d{2}-\d{4}\b',  # SSN
        ]
        
        try:
            with open(log_file, 'r') as f:
                content = f.read()
                for pattern in sensitive_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        return True
        except (IOError, OSError):
            pass
        
        return False
    
    def find_old_files(self, directory, days=7):
        """
        Find files older than specified days
        """
        import time
        
        old_files = []
        cutoff_time = time.time() - (days * 24 * 60 * 60)
        
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.getmtime(file_path) < cutoff_time:
                        old_files.append(file_path)
        except (IOError, OSError):
            pass
        
        return old_files
