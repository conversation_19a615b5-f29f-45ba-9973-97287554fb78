from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from feed.models import Employee, Employer


class Command(BaseCommand):
    help = 'Check authentication setup and data'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== Authentication System Check ==='))
        
        # Check Users
        users = User.objects.all()
        self.stdout.write(f'Total Users: {users.count()}')
        
        for user in users:
            self.stdout.write(f'  - User: {user.username} ({user.email}) - Active: {user.is_active}')
            
            # Check if user has employee record
            try:
                employee = user.employee
                self.stdout.write(f'    Employee: {employee.role} - Status: {employee.status}')
                self.stdout.write(f'    Employer: {employee.employer_id.employer_name} (ID: {employee.employer_id.employer_id})')
            except AttributeError:
                self.stdout.write(self.style.ERROR(f'    ERROR: No Employee record for user {user.username}'))
        
        # Check Employers
        employers = Employer.objects.all()
        self.stdout.write(f'\nTotal Employers: {employers.count()}')
        
        for employer in employers:
            self.stdout.write(f'  - Employer: {employer.employer_name} (ID: {employer.employer_id})')
            employee_count = Employee.objects.filter(employer_id=employer).count()
            self.stdout.write(f'    Employees: {employee_count}')
        
        # Check Employees
        employees = Employee.objects.all()
        self.stdout.write(f'\nTotal Employees: {employees.count()}')
        
        for employee in employees:
            self.stdout.write(f'  - Employee: {employee.user.username} - Role: {employee.role} - Status: {employee.status}')
            self.stdout.write(f'    Employer: {employee.employer_id.employer_name}')
        
        self.stdout.write(self.style.SUCCESS('\n=== Check Complete ==='))
