# Generated by Django 4.1 on 2023-10-22 16:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0012_alter_vacancy_number_of_applicants_temp_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Application',
            fields=[
                ('application_id', models.AutoField(primary_key=True, serialize=False)),
                ('candidate_id', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('vacancy_id', models.<PERSON>r<PERSON>ield(max_length=255)),
                ('application_date', models.DateTimeField(auto_now_add=True)),
                ('application_source', models.Char<PERSON>ield(default='Careers Page', max_length=255)),
                ('application_status', models.Char<PERSON>ield(default='Active', max_length=255)),
                ('application_state', models.Char<PERSON>ield(default='New', max_length=255)),
                ('watching_users', models.JSONField(null=True)),
                ('application_details_id', models.Cha<PERSON><PERSON><PERSON>(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='Application_Detail',
            fields=[
                ('application_details_id', models.AutoField(primary_key=True, serialize=False)),
                ('applicant_resume_location', models.Char<PERSON>ield(max_length=255, null=True)),
                ('applicant_overall_score', models.CharField(max_length=255, null=True)),
                ('applicant_match_score', models.CharField(max_length=255, null=True)),
                ('applicant_experience_details', models.JSONField(null=True)),
            ],
        ),
    ]
