# Generated by Django 4.1 on 2023-11-02 02:21

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0017_alter_applicationjourney_application_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='applicationjourney',
            name='main_state_category',
            field=models.CharField(choices=[('new', 'New'), ('init_check', 'Initial Check'), ('phone', 'Phone Interview'), ('online', 'Online Interview'), ('inface', 'Face to Face Interview'), ('decision', 'Decision Stage')], default='New', max_length=255),
        ),
        migrations.AddField(
            model_name='applicationjourney',
            name='state_started_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
    ]
