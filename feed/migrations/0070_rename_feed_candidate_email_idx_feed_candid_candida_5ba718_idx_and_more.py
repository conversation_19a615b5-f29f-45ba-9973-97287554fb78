# Generated by Django 4.2.3 on 2025-07-10 19:33

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0069_merge_20250710_1921'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='candidate',
            new_name='feed_candid_candida_5ba718_idx',
            old_name='feed_candidate_email_idx',
        ),
        migrations.RenameIndex(
            model_name='candidate',
            new_name='feed_candid_candida_fdd697_idx',
            old_name='feed_candidate_created_idx',
        ),
        migrations.RenameIndex(
            model_name='invitation',
            new_name='feed_invita_employe_1422d3_idx',
            old_name='feed_invitation_employer_status_idx',
        ),
        migrations.RenameIndex(
            model_name='invitation',
            new_name='feed_invita_token_a7f6b5_idx',
            old_name='feed_invitation_token_idx',
        ),
        migrations.RenameIndex(
            model_name='jobtemplate',
            new_name='feed_jobtem_employe_07fcc8_idx',
            old_name='feed_jobtemplate_employer_created_idx',
        ),
        migrations.RenameIndex(
            model_name='jobtemplate',
            new_name='feed_jobtem_employe_6a29c6_idx',
            old_name='feed_jobtemplate_employer_title_idx',
        ),
        migrations.RenameIndex(
            model_name='talentpool',
            new_name='feed_talent_employe_f1c176_idx',
            old_name='feed_talentpool_employer_added_idx',
        ),
        migrations.RenameIndex(
            model_name='talentpool',
            new_name='feed_talent_employe_7cf6c1_idx',
            old_name='feed_talentpool_employer_status_idx',
        ),
    ]
