# Generated by Django 4.1 on 2023-10-22 22:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0014_delete_application_detail_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='application',
            name='current_employer',
            field=models.CharField(default='Unknown', max_length=255),
        ),
        migrations.AddField(
            model_name='application',
            name='current_position',
            field=models.CharField(default='Unknown', max_length=255),
        ),
        migrations.AddField(
            model_name='application',
            name='education_level',
            field=models.CharField(choices=[('Primary Education', 'Primary Education'), ('Lower Secondary Education', 'Lower Secondary Education'), ("Bachelor's or Equivalent", "Bachelor's or Equivalent"), ("Master's or Equivalent", "Master's or Equivalent"), ('Doctorate or Equivalent', 'Doctorate or Equivalent')], default='Primary Education', max_length=255),
        ),
        migrations.AddField(
            model_name='application',
            name='notice_period',
            field=models.CharField(default='1 Month', max_length=255),
        ),
        migrations.AddField(
            model_name='application',
            name='total_exp_years',
            field=models.FloatField(default=0.0),
        ),
        migrations.AlterField(
            model_name='application',
            name='application_source',
            field=models.CharField(choices=[('Careers Page', 'Careers Page'), ('LinkedIn', 'LinkedIn'), ('Glassdoor', 'Glassdoor'), ('Canvider', 'Canvider'), ('Indeed', 'Indeed'), ('Referral', 'Referral'), ('Pracuj.pl', 'Pracuj.pl')], default='Careers Page', max_length=255),
        ),
    ]
