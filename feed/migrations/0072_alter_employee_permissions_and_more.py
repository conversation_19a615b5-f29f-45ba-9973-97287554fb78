# Generated by Django 4.2.20 on 2025-07-18 23:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0071_employee_last_activity_cleared'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='employee',
            name='permissions',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='employee',
            name='profile_photo',
            field=models.TextField(blank=True, default='iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAAAAACoWZBhAAAAAmJLR0QA/4ePzL8AAAAJcEhZcwAAFiUAABYlAUlSJPAAAAAHdElNRQfpBxIXEStTo8xL', null=True),
        ),
        migrations.AlterField(
            model_name='employee',
            name='role',
            field=models.Char<PERSON>ield(choices=[('Administrator', 'Administrator'), ('Hiring Manager', 'Hiring Manager'), ('Interviewer', 'Interviewer'), ('Read Only', 'Read Only'), ('Recruiter', 'Recruiter')], db_index=True, default='Recruiter', max_length=100, null=True),
        ),
    ]
