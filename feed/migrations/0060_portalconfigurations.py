# Generated by Django 4.2.20 on 2025-05-02 20:27

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0059_application_score'),
    ]

    operations = [
        migrations.CreateModel(
            name='PortalConfigurations',
            fields=[
                ('config_id', models.AutoField(primary_key=True, serialize=False)),
                ('portal_name', models.CharField(choices=[('LinkedIn', 'LinkedIn'), ('Glassdoor', 'Glassdoor'), ('Jobloupe', 'Jobloupe')], max_length=100)),
                ('portal_url', models.CharField(max_length=255, null=True)),
                ('portal_status', models.Char<PERSON>ield(default='Inactive', max_length=50)),
                ('config_json', models.J<PERSON><PERSON><PERSON>(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('employer_id', models.Foreign<PERSON>ey(db_column='employer_id', on_delete=django.db.models.deletion.CASCADE, to='feed.employer')),
            ],
        ),
    ]
