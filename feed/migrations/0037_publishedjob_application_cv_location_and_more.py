# Generated by Django 4.2.20 on 2025-03-30 02:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0036_applicationstate_alter_candidate_avatar_bg_color_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PublishedJob',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role_title', models.Char<PERSON>ield(max_length=255)),
                ('office_location', models.Char<PERSON>ield(max_length=255)),
                ('work_schedule', models.CharField(blank=True, max_length=255, null=True)),
                ('office_schedule', models.CharField(blank=True, max_length=255, null=True)),
                ('skills', models.TextField(blank=True, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('published_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.Add<PERSON>ield(
            model_name='application',
            name='cv_location',
            field=models.Char<PERSON>ield(max_length=255, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='application',
            name='application_source',
            field=models.CharField(choices=[('Careers Page', 'Careers Page'), ('LinkedIn', 'LinkedIn'), ('Glassdoor', 'Glassdoor'), ('Canvider', 'Canvider'), ('Indeed', 'Indeed'), ('Referral', 'Referral'), ('Pracuj.pl', 'Pracuj.pl'), ('JustJoinIT', 'JustJoinIT'), ('NoFluffJobs', 'NoFluffJobs'), ('RemoteOK', 'RemoteOK'), ('StackOverflow', 'StackOverflow'), ('GitHub', 'GitHub'), ('Other', 'Other'), ('Unknown', 'Unknown'), ('jobloupe', 'jobloupe')], default='Careers Page', max_length=255),
        ),
        migrations.AlterField(
            model_name='application',
            name='education_level',
            field=models.CharField(choices=[('Primary Education', 'Primary Education'), ('Lower Secondary Education', 'Lower Secondary Education'), ("Bachelor's or Equivalent", "Bachelor's or Equivalent"), ("Master's or Equivalent", "Master's or Equivalent"), ('Doctorate or Equivalent', 'Doctorate or Equivalent'), ('Vocational Training', 'Vocational Training'), ('Postgraduate Education', 'Postgraduate Education'), ('Other', 'Other'), ('Unknown', 'Unknown')], default='Primary Education', max_length=255),
        ),
    ]
