# Generated by Django 4.1 on 2023-11-02 20:00

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('feed', '0019_applicationjourney_journey_survey'),
    ]

    operations = [
        migrations.AddField(
            model_name='applicationjourney',
            name='committed_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='applicationjourney',
            name='main_state_category',
            field=models.CharField(choices=[('new', 'New'), ('init_check', 'Initial Check'), ('phone', 'Phone Interview'), ('online', 'Online Interview'), ('inface', 'Face to Face Interview'), ('decision', 'Decision Stage')], default='new', max_length=255),
        ),
    ]
