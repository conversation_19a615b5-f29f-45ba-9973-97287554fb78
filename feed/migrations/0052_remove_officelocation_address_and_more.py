# Generated by Django 4.2.3 on 2025-04-21 12:53

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('feed', '0051_merge_20250421_1124'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='officelocation',
            name='address',
        ),
        migrations.RemoveField(
            model_name='officelocation',
            name='description',
        ),
        migrations.RemoveField(
            model_name='officelocation',
            name='name',
        ),
        migrations.AddField(
            model_name='department',
            name='employer_id',
            field=models.ForeignKey(db_column='employer_id', default=1, on_delete=django.db.models.deletion.CASCADE, to='feed.employer'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='officelocation',
            name='employer_id',
            field=models.ForeignKey(db_column='employer_id', default=1, on_delete=django.db.models.deletion.CASCADE, to='feed.employer'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='officeschedule',
            name='employer_id',
            field=models.ForeignKey(db_column='employer_id', default=1, on_delete=django.db.models.deletion.CASCADE, to='feed.employer'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='senioritylevel',
            name='employer_id',
            field=models.ForeignKey(db_column='employer_id', default=1, on_delete=django.db.models.deletion.CASCADE, to='feed.employer'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='workschedule',
            name='employer_id',
            field=models.ForeignKey(db_column='employer_id', default=1, on_delete=django.db.models.deletion.CASCADE, to='feed.employer'),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to=settings.AUTH_USER_MODEL)),
                ('role', models.CharField(default='Recruiter', max_length=100, null=True)),
                ('permissions', models.JSONField(null=True)),
                ('status', models.CharField(default='Active', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('employer_id', models.ForeignKey(db_column='employer_id', on_delete=django.db.models.deletion.CASCADE, to='feed.employer')),
            ],
        ),
    ]
