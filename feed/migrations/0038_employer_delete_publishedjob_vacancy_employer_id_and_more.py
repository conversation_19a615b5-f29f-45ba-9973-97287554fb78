# Generated by Django 4.2.20 on 2025-03-30 03:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0037_publishedjob_application_cv_location_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Employer',
            fields=[
                ('employer_id', models.AutoField(primary_key=True, serialize=False)),
                ('employer_name', models.CharField(max_length=255)),
                ('employer_email', models.EmailField(max_length=255)),
                ('employer_phone', models.CharField(max_length=255, null=True)),
                ('employer_address', models.CharField(max_length=255, null=True)),
                ('office_locations', models.CharField(max_length=255, null=True)),
                ('employer_created_at', models.DateTimeField(auto_now_add=True)),
                ('employer_logo_url', models.CharField(max_length=255, null=True)),
                ('employer_banner_url', models.Char<PERSON>ield(max_length=255, null=True)),
                ('employer_website', models.CharField(max_length=255)),
                ('employer_description', models.TextField()),
                ('employer_industry', models.CharField(max_length=255, null=True)),
                ('employer_headcount', models.IntegerField(null=True)),
                ('employer_social_portals', models.TextField(null=True)),
                ('employer_status', models.CharField(default='Active', max_length=255)),
            ],
        ),
        migrations.DeleteModel(
            name='PublishedJob',
        ),
        migrations.AddField(
            model_name='vacancy',
            name='employer_id',
            field=models.CharField(default=1, max_length=255),
        ),
        migrations.AddField(
            model_name='vacancy',
            name='job_portals',
            field=models.CharField(choices=[('Careers Page', 'Careers Page'), ('LinkedIn', 'LinkedIn'), ('Glassdoor', 'Glassdoor'), ('Canvider', 'Canvider'), ('Indeed', 'Indeed'), ('Referral', 'Referral'), ('Pracuj.pl', 'Pracuj.pl'), ('JustJoinIT', 'JustJoinIT'), ('NoFluffJobs', 'NoFluffJobs'), ('RemoteOK', 'RemoteOK'), ('StackOverflow', 'StackOverflow'), ('GitHub', 'GitHub'), ('Other', 'Other'), ('Unknown', 'Unknown'), ('jobloupe', 'jobloupe')], default='jobloupe', max_length=255),
        ),
        migrations.AddField(
            model_name='vacancy',
            name='jobtags',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='vacancy',
            name='office_schedule',
            field=models.CharField(default='Remote', max_length=255),
        ),
        migrations.AddField(
            model_name='vacancy',
            name='salary_currency',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='vacancy',
            name='salary_max',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='vacancy',
            name='salary_min',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='vacancy',
            name='skills',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='vacancy',
            name='vacancy_job_description',
            field=models.TextField(null=True),
        ),
        migrations.AddField(
            model_name='vacancy',
            name='work_schedule',
            field=models.CharField(default='Full Time', max_length=255),
        ),
        migrations.AlterField(
            model_name='application',
            name='application_source',
            field=models.CharField(choices=[('Careers Page', 'Careers Page'), ('LinkedIn', 'LinkedIn'), ('Glassdoor', 'Glassdoor'), ('Canvider', 'Canvider'), ('Indeed', 'Indeed'), ('Referral', 'Referral'), ('Pracuj.pl', 'Pracuj.pl'), ('JustJoinIT', 'JustJoinIT'), ('NoFluffJobs', 'NoFluffJobs'), ('RemoteOK', 'RemoteOK'), ('StackOverflow', 'StackOverflow'), ('GitHub', 'GitHub'), ('Other', 'Other'), ('Unknown', 'Unknown'), ('jobloupe', 'jobloupe')], default='jobloupe', max_length=255),
        ),
        migrations.AlterField(
            model_name='application',
            name='education_level',
            field=models.CharField(choices=[('Primary Education', 'Primary Education'), ('Lower Secondary Education', 'Lower Secondary Education'), ("Bachelor's or Equivalent", "Bachelor's or Equivalent"), ("Master's or Equivalent", "Master's or Equivalent"), ('Doctorate or Equivalent', 'Doctorate or Equivalent'), ('Vocational Training', 'Vocational Training'), ('Postgraduate Education', 'Postgraduate Education'), ('Other', 'Other'), ('Unknown', 'Unknown')], default='Unknown', max_length=255),
        ),
        migrations.AlterField(
            model_name='vacancy',
            name='number_of_applicants_temp',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='vacancy',
            name='vacancy_bus_unit',
            field=models.CharField(default='IT', max_length=255),
        ),
        migrations.AlterField(
            model_name='vacancy',
            name='vacancy_status',
            field=models.CharField(choices=[('Active', 'Active'), ('Draft', 'Draft'), ('Closed', 'Closed'), ('Reviewing', 'Reviewing')], default='Reviewing', max_length=255),
        ),
    ]
