# Generated by Django 4.2.20 on 2025-05-03 20:43

from django.db import migrations, models
import feed.models


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0061_alter_portalconfigurations_config_json'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='invitation',
            name='location',
        ),
        migrations.AddField(
            model_name='invitation',
            name='token',
            field=models.CharField(default=1, max_length=64, unique=True),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='invitation',
            name='email',
            field=models.EmailField(max_length=254),
        ),
        migrations.AlterField(
            model_name='invitation',
            name='expiry_date',
            field=models.DateTimeField(default=feed.models.Invitation.default_expiry_date, null=True),
        ),
        migrations.AlterField(
            model_name='invitation',
            name='invitation_status',
            field=models.CharField(choices=[('Pending', 'Pending'), ('Accepted', 'Accepted'), ('Declined', 'Declined'), ('Expired', 'Expired'), ('Canceled', 'Canceled')], default='Pending', max_length=20),
        ),
        migrations.Alter<PERSON>ield(
            model_name='invitation',
            name='role',
            field=models.CharField(choices=[('Administrator', 'Administrator'), ('Recruiter', 'Recruiter'), ('Hiring Manager', 'Hiring Manager'), ('Interviewer', 'Interviewer'), ('Read Only', 'Read Only')], max_length=100),
        ),
    ]
