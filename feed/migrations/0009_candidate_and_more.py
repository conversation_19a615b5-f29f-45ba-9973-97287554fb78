# Generated by Django 4.1 on 2023-10-15 22:28

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('feed', '0008_alter_applicationcomments_comment_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='Candidate',
            fields=[
                ('candidate_id', models.Char<PERSON>ield(max_length=255, primary_key=True, serialize=False, unique=True)),
                ('candidate_firstname', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('candidate_lastname', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('candidate_email', models.EmailField(max_length=255)),
                ('candidate_phone', models.Char<PERSON><PERSON>(max_length=255)),
                ('candidate_address', models.Char<PERSON><PERSON>(max_length=255)),
                ('candidate_date_of_birth', models.DateField()),
                ('candidate_gender', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('candidate_created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
            ],
        ),
        migrations.RenameModel(
            old_name='ApplicationComments',
            new_name='ApplicationComment',
        ),
    ]
