# Generated by Django 4.2.3 on 2025-03-09 11:23

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0033_appointment'),
    ]

    operations = [
        migrations.AddField(
            model_name='jobtemplate',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='jobtemplate',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='jobtemplate',
            name='usage_count',
            field=models.IntegerField(default=0),
        ),
    ]
