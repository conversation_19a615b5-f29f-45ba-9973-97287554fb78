# Generated migration for employer isolation
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0001_initial'),  # Adjust this to your latest migration
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        
        # Add employer_id to JobTemplate
        migrations.AddField(
            model_name='jobtemplate',
            name='employer_id',
            field=models.ForeignKey(
                db_column='employer_id',
                on_delete=django.db.models.deletion.CASCADE,
                to='feed.employer',
                default=1  # You may need to adjust this default
            ),
            preserve_default=False,
        ),
        
        # Add created_by to JobTemplate
        migrations.AddField(
            model_name='jobtemplate',
            name='created_by',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to='auth.user',
                default=1  # You may need to adjust this default
            ),
            preserve_default=False,
        ),
        
        # Add employer_id to TalentPool
        migrations.AddField(
            model_name='talentpool',
            name='employer_id',
            field=models.ForeignKey(
                db_column='employer_id',
                on_delete=django.db.models.deletion.CASCADE,
                to='feed.employer',
                default=1  # You may need to adjust this default
            ),
            preserve_default=False,
        ),
        
        # Add added_by to TalentPool
        migrations.AddField(
            model_name='talentpool',
            name='added_by',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to='auth.user',
                default=1  # You may need to adjust this default
            ),
            preserve_default=False,
        ),
        
        # Add employer_id to Invitation
        migrations.AddField(
            model_name='invitation',
            name='employer_id',
            field=models.ForeignKey(
                db_column='employer_id',
                on_delete=django.db.models.deletion.CASCADE,
                to='feed.employer',
                default=1  # You may need to adjust this default
            ),
            preserve_default=False,
        ),
        
        # Add invited_by to Invitation
        migrations.AddField(
            model_name='invitation',
            name='invited_by',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to='auth.user',
                default=1  # You may need to adjust this default
            ),
            preserve_default=False,
        ),
        
        # Add indexes for JobTemplate
        migrations.AddIndex(
            model_name='jobtemplate',
            index=models.Index(fields=['employer_id', 'created_at'], name='feed_jobtemplate_employer_created_idx'),
        ),
        migrations.AddIndex(
            model_name='jobtemplate',
            index=models.Index(fields=['employer_id', 'title'], name='feed_jobtemplate_employer_title_idx'),
        ),
        
        # Add indexes for TalentPool
        migrations.AddIndex(
            model_name='talentpool',
            index=models.Index(fields=['employer_id', 'talent_added_at'], name='feed_talentpool_employer_added_idx'),
        ),
        migrations.AddIndex(
            model_name='talentpool',
            index=models.Index(fields=['employer_id', 'talent_status'], name='feed_talentpool_employer_status_idx'),
        ),
        
        # Add indexes for Invitation
        migrations.AddIndex(
            model_name='invitation',
            index=models.Index(fields=['employer_id', 'invitation_status'], name='feed_invitation_employer_status_idx'),
        ),
        migrations.AddIndex(
            model_name='invitation',
            index=models.Index(fields=['token'], name='feed_invitation_token_idx'),
        ),
        
        # Add indexes for Candidate
        migrations.AddIndex(
            model_name='candidate',
            index=models.Index(fields=['candidate_email'], name='feed_candidate_email_idx'),
        ),
        migrations.AddIndex(
            model_name='candidate',
            index=models.Index(fields=['candidate_created_at'], name='feed_candidate_created_idx'),
        ),

    ]
