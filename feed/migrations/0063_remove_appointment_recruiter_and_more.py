# Generated by Django 4.2.20 on 2025-05-04 23:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('feed', '0062_remove_invitation_location_invitation_token_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='appointment',
            name='recruiter',
        ),
        migrations.AddField(
            model_name='appointment',
            name='appointment_kind',
            field=models.CharField(choices=[('Phone Call', 'Phone Call'), ('Video Call', 'Video Call'), ('Online Interview', 'Online Interview'), ('Technical Assessment', 'Technical Assessment'), ('Final Interview', 'Final Interview'), ('Face to Face Interview', 'Face to Face Interview'), ('Office Visit', 'Office Visit'), ('Other', 'Other')], default='Phone Call', max_length=255),
        ),
        migrations.AddField(
            model_name='appointment',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='appointment',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='appointment',
            name='inform_invitees',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='interviewers',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='invited_candidates',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='meeting_link',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='meeting_status',
            field=models.CharField(default='Scheduled', max_length=255),
        ),
    ]
