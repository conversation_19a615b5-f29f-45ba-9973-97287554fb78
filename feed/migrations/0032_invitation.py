# Generated by Django 4.2.3 on 2025-01-12 15:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0031_jobtemplate'),
    ]

    operations = [
        migrations.CreateModel(
            name='Invitation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.Char<PERSON>ield(max_length=100)),
                ('last_name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('location', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('role', models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                ('invitation_status', models.Char<PERSON>ield(default='Pending', max_length=20)),
            ],
        ),
    ]
