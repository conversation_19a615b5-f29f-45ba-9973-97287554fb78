# Generated by Django 4.2.20 on 2025-04-04 22:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0041_alter_talentpool_talent_added_at'),
    ]

    operations = [
        migrations.CreateModel(
            name='PotentialEmployer',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('company_name', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('contact_name', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('email', models.<PERSON>ail<PERSON>ield(max_length=255)),
                ('phone', models.CharField(max_length=255)),
                ('website', models.CharField(max_length=255)),
                ('verification_info', models.TextField(max_length=500, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(default='Pending', max_length=255)),
            ],
        ),
    ]
