# Generated by Django 4.2.20 on 2025-03-29 21:49

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import feed.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('feed', '0035_invitation_expiry_date_invitation_sent_date'),
    ]

    operations = [
        migrations.CreateModel(
            name='ApplicationState',
            fields=[
                ('state_id', models.AutoField(primary_key=True, serialize=False)),
                ('state_name', models.CharField(choices=[('New', 'New'), ('Review_1', 'Review #1'), ('Review_2', 'Review #2'), ('Review_3', 'Review #3'), ('Review_4', 'Review #4'), ('Review_5', 'Review #5'), ('Decision', 'Ready for Decision'), ('Eliminated', 'Eliminated'), ('Offer', 'Offer Made'), ('Accept', 'Candidate Accepted'), ('Reject', 'Candidate Rejected')], default='New', max_length=255)),
                ('state_notes', models.TextField(null=True)),
                ('state_started_at', models.DateTimeField(auto_now_add=True)),
                ('application_id', models.ForeignKey(db_column='application_id', on_delete=django.db.models.deletion.CASCADE, to='feed.application')),
                ('committed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AlterField(
            model_name='candidate',
            name='avatar_bg_color',
            field=models.CharField(default=feed.models.generate_random_color, max_length=7),
        ),
        migrations.AlterField(
            model_name='candidate',
            name='candidate_address',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='candidate',
            name='candidate_date_of_birth',
            field=models.DateField(null=True),
        ),
        migrations.DeleteModel(
            name='ApplicationJourney',
        ),
    ]
