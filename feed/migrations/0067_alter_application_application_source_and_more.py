# Generated by Django 4.2.20 on 2025-05-30 00:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0066_officelocation_location_details_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='application',
            name='application_source',
            field=models.CharField(choices=[('Careers Page', 'Careers Page'), ('LinkedIn', 'LinkedIn'), ('Glassdoor', 'Glassdoor'), ('Canvider', 'Canvider'), ('Indeed', 'Indeed'), ('Referral', 'Referral'), ('Pracuj.pl', 'Pracuj.pl'), ('JustJoinIT', 'JustJoinIT'), ('NoFluffJobs', 'NoFluffJobs'), ('RemoteOK', 'RemoteOK'), ('StackOverflow', 'StackOverflow'), ('GitHub', 'GitHub'), ('PostJobFree', 'PostJobFree'), ('Other', 'Other'), ('Unknown', 'Unknown'), ('jobloupe', 'jobloupe')], default='jobloupe', max_length=255),
        ),
        migrations.AlterField(
            model_name='vacancy',
            name='vacancy_status',
            field=models.CharField(choices=[('Active', 'Active'), ('Draft', 'Draft'), ('Closed', 'Closed'), ('On-Hold', 'On-Hold'), ('Archived', 'Archived'), ('Reviewing', 'Reviewing'), ('Deleted', 'Deleted')], default='Reviewing', max_length=255),
        ),
    ]
