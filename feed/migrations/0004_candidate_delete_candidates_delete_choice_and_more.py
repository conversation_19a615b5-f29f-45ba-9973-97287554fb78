# Generated by Django 4.2.3 on 2023-07-13 20:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0003_candidates'),
    ]

    operations = [
        migrations.CreateModel(
            name='Candidate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('candidate_id', models.IntegerField()),
                ('first_name', models.<PERSON>r<PERSON><PERSON>(max_length=200)),
                ('last_name', models.<PERSON>r<PERSON><PERSON>(max_length=200)),
                ('email', models.EmailField(max_length=200)),
                ('phone', models.Char<PERSON>ield(max_length=50)),
                ('age', models.IntegerField()),
                ('gender', models.CharField(choices=[('Female', 'Female'), ('Male', 'Male')], max_length=6)),
                ('resume_id', models.IntegerField()),
                ('pub_date', models.DateTimeField(verbose_name='Data published')),
            ],
        ),
        migrations.DeleteModel(
            name='Candidates',
        ),
        migrations.DeleteModel(
            name='Choice',
        ),
        migrations.DeleteModel(
            name='Question',
        ),
    ]
