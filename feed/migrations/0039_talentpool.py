# Generated by Django 4.2.20 on 2025-04-04 17:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0038_employer_delete_publishedjob_vacancy_employer_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='TalentPool',
            fields=[
                ('talent_id', models.AutoField(primary_key=True, serialize=False)),
                ('talent_firstname', models.Char<PERSON>ield(max_length=255)),
                ('talent_lastname', models.Char<PERSON>ield(max_length=255)),
                ('talent_email', models.EmailField(max_length=255)),
                ('talent_phone', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255, null=True)),
                ('talent_country', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255, null=True)),
                ('talent_status', models.Char<PERSON>ield(default='Active', max_length=255)),
                ('cv_location', models.Char<PERSON>ield(max_length=255)),
            ],
        ),
    ]
