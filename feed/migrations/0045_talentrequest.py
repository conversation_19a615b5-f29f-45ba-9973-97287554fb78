# Generated by Django 4.2.20 on 2025-04-06 21:35

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('feed', '0044_alter_vacancy_vacancy_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='TalentRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_date', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(default='New', max_length=50)),
                ('committed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('employer_id', models.ForeignKey(db_column='employer_id', on_delete=django.db.models.deletion.CASCADE, to='feed.employer')),
                ('vacancy_id', models.ForeignKey(db_column='vacancy_id', on_delete=django.db.models.deletion.CASCADE, to='feed.vacancy')),
            ],
        ),
    ]
