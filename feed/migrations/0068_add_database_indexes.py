# Generated by Django 4.2.20 on 2025-06-20 01:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0067_alter_application_application_source_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='application',
            name='application_date',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='application',
            name='application_state',
            field=models.CharField(db_index=True, default='New', max_length=255),
        ),
        migrations.AlterField(
            model_name='application',
            name='score',
            field=models.IntegerField(db_index=True, default=-1),
        ),
        migrations.AlterField(
            model_name='applicationstate',
            name='state_started_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='appointment',
            name='end_time',
            field=models.DateTime<PERSON><PERSON>(db_index=True),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name='appointment',
            name='start_time',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='employee',
            name='role',
            field=models.CharField(db_index=True, default='Recruiter', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='employee',
            name='status',
            field=models.CharField(db_index=True, default='Active', max_length=50),
        ),
        migrations.AlterField(
            model_name='vacancy',
            name='employer_id',
            field=models.CharField(db_index=True, default=1, max_length=255),
        ),
        migrations.AlterField(
            model_name='vacancy',
            name='vacancy_bus_unit',
            field=models.CharField(db_index=True, default='IT', max_length=255),
        ),
        migrations.AlterField(
            model_name='vacancy',
            name='vacancy_creation_date',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='vacancy',
            name='vacancy_status',
            field=models.CharField(choices=[('Active', 'Active'), ('Draft', 'Draft'), ('Closed', 'Closed'), ('On-Hold', 'On-Hold'), ('Archived', 'Archived'), ('Reviewing', 'Reviewing'), ('Deleted', 'Deleted')], db_index=True, default='Reviewing', max_length=255),
        ),
        migrations.AlterField(
            model_name='vacancy',
            name='vacancy_title',
            field=models.CharField(db_index=True, max_length=255),
        ),
        migrations.AddIndex(
            model_name='application',
            index=models.Index(fields=['vacancy_id', 'application_date'], name='feed_applic_vacancy_d1249a_idx'),
        ),
        migrations.AddIndex(
            model_name='application',
            index=models.Index(fields=['application_date', 'application_state'], name='feed_applic_applica_35d194_idx'),
        ),
        migrations.AddIndex(
            model_name='application',
            index=models.Index(fields=['vacancy_id', 'score'], name='feed_applic_vacancy_decfe3_idx'),
        ),
        migrations.AddIndex(
            model_name='applicationstate',
            index=models.Index(fields=['application_id', 'state_started_at'], name='feed_applic_applica_691ab1_idx'),
        ),
        migrations.AddIndex(
            model_name='applicationstate',
            index=models.Index(fields=['state_started_at', 'state_name'], name='feed_applic_state_s_b64103_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['start_time', 'end_time'], name='feed_appoin_start_t_aaf374_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['vacancy_id', 'start_time'], name='feed_appoin_vacancy_a5b351_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['employer_id', 'role'], name='feed_employ_employe_da62b8_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['employer_id', 'status'], name='feed_employ_employe_80c542_idx'),
        ),
        migrations.AddIndex(
            model_name='vacancy',
            index=models.Index(fields=['vacancy_status', 'vacancy_creation_date'], name='feed_vacanc_vacancy_d01a32_idx'),
        ),
        migrations.AddIndex(
            model_name='vacancy',
            index=models.Index(fields=['vacancy_city', 'vacancy_country'], name='feed_vacanc_vacancy_e773d6_idx'),
        ),
        migrations.AddIndex(
            model_name='vacancy',
            index=models.Index(fields=['employer_id', 'vacancy_status'], name='feed_vacanc_employe_4531c8_idx'),
        ),
    ]
