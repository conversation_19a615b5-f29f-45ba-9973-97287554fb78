from django.db import models
from django.contrib.auth.models import AnonymousUser


class EmployerFilteredManager(models.Manager):
    """
    Custom manager that automatically filters querysets by employer_id.
    This ensures data isolation at the model level.
    """
    
    def __init__(self, employer_field='employer_id'):
        super().__init__()
        self.employer_field = employer_field
    
    def get_queryset_for_user(self, user):
        """
        Get queryset filtered by user's employer.
        """
        if isinstance(user, AnonymousUser) or not user.is_authenticated:
            return self.get_queryset().none()
        
        if user.is_superuser:
            return self.get_queryset()
        
        try:
            employer_id = user.employee.employer_id.employer_id
            filter_kwargs = {self.employer_field: employer_id}
            return self.get_queryset().filter(**filter_kwargs)
        except AttributeError:
            # User doesn't have employee record
            return self.get_queryset().none()


class VacancyManager(EmployerFilteredManager):
    """Manager for Vacancy model with employer filtering."""
    
    def __init__(self):
        super().__init__(employer_field='employer_id')


class ApplicationManager(models.Manager):
    """Manager for Application model with employer filtering through vacancy."""
    
    def get_queryset_for_user(self, user):
        """
        Get applications filtered by user's employer through vacancy relationship.
        """
        if isinstance(user, AnonymousUser) or not user.is_authenticated:
            return self.get_queryset().none()
        
        if user.is_superuser:
            return self.get_queryset()
        
        try:
            employer_id = user.employee.employer_id.employer_id
            return self.get_queryset().filter(vacancy_id__employer_id=employer_id)
        except AttributeError:
            return self.get_queryset().none()


class ApplicationStateManager(models.Manager):
    """Manager for ApplicationState model with employer filtering."""
    
    def get_queryset_for_user(self, user):
        """
        Get application states filtered by user's employer.
        """
        if isinstance(user, AnonymousUser) or not user.is_authenticated:
            return self.get_queryset().none()
        
        if user.is_superuser:
            return self.get_queryset()
        
        try:
            employer_id = user.employee.employer_id.employer_id
            return self.get_queryset().filter(
                application_id__vacancy_id__employer_id=employer_id
            )
        except AttributeError:
            return self.get_queryset().none()


class ApplicationCommentManager(models.Manager):
    """Manager for ApplicationComment model with employer filtering."""
    
    def get_queryset_for_user(self, user):
        """
        Get application comments filtered by user's employer.
        """
        if isinstance(user, AnonymousUser) or not user.is_authenticated:
            return self.get_queryset().none()
        
        if user.is_superuser:
            return self.get_queryset()
        
        try:
            employer_id = user.employee.employer_id.employer_id
            return self.get_queryset().filter(
                application_id__vacancy_id__employer_id=employer_id
            )
        except AttributeError:
            return self.get_queryset().none()


class AppointmentManager(models.Manager):
    """Manager for Appointment model with employer filtering."""
    
    def get_queryset_for_user(self, user):
        """
        Get appointments filtered by user's employer.
        """
        if isinstance(user, AnonymousUser) or not user.is_authenticated:
            return self.get_queryset().none()
        
        if user.is_superuser:
            return self.get_queryset()
        
        try:
            employer_id = user.employee.employer_id.employer_id
            return self.get_queryset().filter(vacancy_id__employer_id=employer_id)
        except AttributeError:
            return self.get_queryset().none()


class EmployeeManager(EmployerFilteredManager):
    """Manager for Employee model with employer filtering."""

    def __init__(self):
        super().__init__(employer_field='employer_id')


class JobTemplateManager(EmployerFilteredManager):
    """Manager for JobTemplate model with employer filtering."""

    def __init__(self):
        super().__init__(employer_field='employer_id')


class TalentPoolManager(EmployerFilteredManager):
    """Manager for TalentPool model with employer filtering."""

    def __init__(self):
        super().__init__(employer_field='employer_id')


class InvitationManager(EmployerFilteredManager):
    """Manager for Invitation model with employer filtering."""

    def __init__(self):
        super().__init__(employer_field='employer_id')
