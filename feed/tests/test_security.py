"""
Comprehensive security testing suite
"""
import json
import time
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.cache import cache
from django.core.files.uploadedfile import SimpleUploadedFile
from feed.models import Employee, Employer, Application, Vacancy
from feed.utils.input_validation import InputValidator
from feed.utils.file_security import FileSecurityValidator
from feed.utils.auth_security import PasswordValidator, AccountLockoutManager
from feed.utils.api_security import APIRateLimiter
from feed.utils.data_protection import DataEncryption, PIIProtection


class SecurityTestCase(TestCase):
    """
    Base class for security tests
    """
    
    def setUp(self):
        """
        Set up test data
        """
        # Create test employer
        self.employer = Employer.objects.create(
            employer_id='test_employer_123',
            employer_name='Test Company',
            employer_email='<EMAIL>'
        )
        
        # Create test users
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='SecurePass123!'
        )
        
        self.regular_user = User.objects.create_user(
            username='user',
            email='<EMAIL>',
            password='SecurePass123!'
        )
        
        # Create employee records
        Employee.objects.create(
            user=self.admin_user,
            employer_id=self.employer,
            employee_role='Administrator'
        )
        
        Employee.objects.create(
            user=self.regular_user,
            employer_id=self.employer,
            employee_role='Recruiter'
        )
        
        self.client = Client()


class CSRFProtectionTest(SecurityTestCase):
    """
    Test CSRF protection implementation
    """
    
    def test_csrf_protection_enabled(self):
        """
        Test that CSRF protection is enabled for POST requests
        """
        # Try to make POST request without CSRF token
        response = self.client.post('/feed/save_published_job/', {
            'job_title': 'Test Job',
            'job_description': 'Test Description'
        })
        
        # Should be forbidden due to missing CSRF token
        self.assertEqual(response.status_code, 403)
    
    def test_csrf_token_validation(self):
        """
        Test CSRF token validation
        """
        self.client.login(username='admin', password='SecurePass123!')
        
        # Get CSRF token
        response = self.client.get('/feed/')
        csrf_token = response.context['csrf_token']
        
        # Make POST request with valid CSRF token
        response = self.client.post('/feed/save_published_job/', {
            'job_title': 'Test Job',
            'job_description': 'Test Description',
            'csrfmiddlewaretoken': csrf_token
        })
        
        # Should not be forbidden
        self.assertNotEqual(response.status_code, 403)


class InputValidationTest(SecurityTestCase):
    """
    Test input validation and sanitization
    """
    
    def test_xss_prevention(self):
        """
        Test XSS attack prevention
        """
        malicious_input = '<script>alert("XSS")</script>'
        
        # Test HTML sanitization
        sanitized = InputValidator.sanitize_html(malicious_input, allow_tags=False)
        self.assertNotIn('<script>', sanitized)
        self.assertNotIn('alert', sanitized)
    
    def test_email_validation(self):
        """
        Test email input validation
        """
        # Valid email
        valid_email = '<EMAIL>'
        result = InputValidator.validate_email_input(valid_email)
        self.assertEqual(result, valid_email.lower())
        
        # Invalid email
        with self.assertRaises(Exception):
            InputValidator.validate_email_input('invalid-email')
    
    def test_dangerous_pattern_detection(self):
        """
        Test detection of dangerous patterns
        """
        dangerous_inputs = [
            'javascript:alert(1)',
            '<script>evil()</script>',
            'eval(malicious_code)',
            'document.cookie'
        ]
        
        for dangerous_input in dangerous_inputs:
            with self.assertRaises(Exception):
                InputValidator.sanitize_text(dangerous_input)


class FileUploadSecurityTest(SecurityTestCase):
    """
    Test file upload security
    """
    
    def test_file_type_validation(self):
        """
        Test file type validation
        """
        # Valid image file
        valid_image = SimpleUploadedFile(
            "test.jpg",
            b"file_content",
            content_type="image/jpeg"
        )
        
        # This should not raise an exception
        try:
            FileSecurityValidator.validate_filename(valid_image.name)
        except Exception:
            self.fail("Valid image file should pass validation")
        
        # Invalid file type
        malicious_file = SimpleUploadedFile(
            "malware.exe",
            b"malicious_content",
            content_type="application/octet-stream"
        )
        
        with self.assertRaises(Exception):
            FileSecurityValidator.validate_file_upload(malicious_file, 'image')
    
    def test_file_size_limits(self):
        """
        Test file size limits
        """
        # Create a large file
        large_content = b"x" * (10 * 1024 * 1024)  # 10MB
        large_file = SimpleUploadedFile(
            "large.jpg",
            large_content,
            content_type="image/jpeg"
        )
        
        # Should fail for image upload (5MB limit)
        with self.assertRaises(Exception):
            FileSecurityValidator.validate_file_upload(large_file, 'image')


class AuthenticationSecurityTest(SecurityTestCase):
    """
    Test authentication security features
    """
    
    def test_password_strength_validation(self):
        """
        Test password strength requirements
        """
        # Weak passwords should fail
        weak_passwords = [
            'password',
            '123456',
            'abc123',
            'Password',  # Missing special char and number
            'password123',  # Missing uppercase and special char
        ]
        
        for weak_password in weak_passwords:
            with self.assertRaises(Exception):
                PasswordValidator.validate_password_strength(weak_password)
        
        # Strong password should pass
        strong_password = 'SecurePass123!'
        try:
            PasswordValidator.validate_password_strength(strong_password)
        except Exception:
            self.fail("Strong password should pass validation")
    
    def test_account_lockout(self):
        """
        Test account lockout after failed attempts
        """
        username = 'test_user'
        
        # Clear any existing lockout
        AccountLockoutManager.clear_failed_attempts(username)
        
        # Record multiple failed attempts
        for i in range(6):  # More than MAX_FAILED_ATTEMPTS (5)
            AccountLockoutManager.record_failed_attempt(username)
        
        # Account should be locked
        self.assertTrue(AccountLockoutManager.is_account_locked(username))
    
    def test_session_security(self):
        """
        Test session security features
        """
        # Login user
        self.client.login(username='admin', password='SecurePass123!')
        
        # Check that session has security attributes
        session = self.client.session
        self.assertIn('login_time', session)


class APISecurityTest(SecurityTestCase):
    """
    Test API security features
    """
    
    def test_rate_limiting(self):
        """
        Test API rate limiting
        """
        identifier = 'test_user'
        api_type = 'test'
        
        # Clear any existing rate limit data
        cache.clear()
        
        # Set a low limit for testing
        APIRateLimiter.RATE_LIMITS[api_type] = 3
        
        # Make requests up to the limit
        for i in range(3):
            is_limited, remaining, reset_time = APIRateLimiter.is_rate_limited(identifier, api_type)
            self.assertFalse(is_limited)
            APIRateLimiter.record_request(identifier, api_type)
        
        # Next request should be rate limited
        is_limited, remaining, reset_time = APIRateLimiter.is_rate_limited(identifier, api_type)
        self.assertTrue(is_limited)
    
    def test_api_authentication(self):
        """
        Test API authentication requirements
        """
        # Try to access protected endpoint without authentication
        response = self.client.get('/feed/api/applications/')
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Login and try again
        self.client.login(username='admin', password='SecurePass123!')
        response = self.client.get('/feed/api/applications/')
        self.assertNotEqual(response.status_code, 302)


class DataProtectionTest(SecurityTestCase):
    """
    Test data protection and encryption
    """
    
    def test_data_encryption(self):
        """
        Test data encryption and decryption
        """
        encryptor = DataEncryption()
        
        original_data = "Sensitive information"
        
        # Encrypt data
        encrypted_data = encryptor.encrypt(original_data)
        self.assertNotEqual(encrypted_data, original_data)
        
        # Decrypt data
        decrypted_data = encryptor.decrypt(encrypted_data)
        self.assertEqual(decrypted_data, original_data)
    
    def test_pii_detection(self):
        """
        Test PII detection and masking
        """
        text_with_pii = "Contact <NAME_EMAIL> or call ************"
        
        # Detect PII
        detected_pii = PIIProtection.detect_pii(text_with_pii)
        self.assertIn('email', detected_pii)
        self.assertIn('phone', detected_pii)
        
        # Mask PII
        masked_text = PIIProtection.mask_pii(text_with_pii)
        self.assertNotIn('<EMAIL>', masked_text)
        self.assertNotIn('************', masked_text)


class AccessControlTest(SecurityTestCase):
    """
    Test role-based access control
    """
    
    def test_employer_isolation(self):
        """
        Test that users can only access their employer's data
        """
        # Create another employer and user
        other_employer = Employer.objects.create(
            employer_id='other_employer_456',
            employer_name='Other Company',
            employer_email='<EMAIL>'
        )
        
        other_user = User.objects.create_user(
            username='other_user',
            email='<EMAIL>',
            password='SecurePass123!'
        )
        
        Employee.objects.create(
            user=other_user,
            employer_id=other_employer,
            employee_role='Recruiter'
        )
        
        # Create vacancy for other employer
        other_vacancy = Vacancy.objects.create(
            vacancy_id='other_vacancy_123',
            employer_id=other_employer,
            job_title='Other Job',
            job_description='Other Description'
        )
        
        # Login as regular user (from first employer)
        self.client.login(username='user', password='SecurePass123!')
        
        # Try to access other employer's vacancy
        response = self.client.get(f'/feed/vacancy/{other_vacancy.vacancy_id}/')
        
        # Should be denied or redirected
        self.assertIn(response.status_code, [403, 404, 302])
    
    def test_permission_decorators(self):
        """
        Test permission-based access control
        """
        # Test that certain endpoints require specific permissions
        self.client.login(username='user', password='SecurePass123!')
        
        # Try to access admin-only functionality
        response = self.client.post('/feed/admin/delete_employer/', {
            'employer_id': self.employer.employer_id
        })
        
        # Should be denied
        self.assertIn(response.status_code, [403, 404, 405])


class SecurityHeadersTest(SecurityTestCase):
    """
    Test security headers implementation
    """
    
    def test_security_headers_present(self):
        """
        Test that security headers are present in responses
        """
        response = self.client.get('/feed/')
        
        # Check for important security headers
        expected_headers = [
            'X-Content-Type-Options',
            'X-Frame-Options',
            'X-XSS-Protection',
        ]
        
        for header in expected_headers:
            self.assertIn(header, response)


class IntegrationSecurityTest(SecurityTestCase):
    """
    Integration tests for security features
    """
    
    def test_complete_user_workflow_security(self):
        """
        Test security throughout a complete user workflow
        """
        # 1. Login with security checks
        login_response = self.client.post('/accounts/login/', {
            'username': 'admin',
            'password': 'SecurePass123!'
        })
        
        # 2. Access protected page
        dashboard_response = self.client.get('/feed/')
        self.assertEqual(dashboard_response.status_code, 200)
        
        # 3. Upload file with security validation
        test_file = SimpleUploadedFile(
            "test_cv.pdf",
            b"PDF content",
            content_type="application/pdf"
        )
        
        upload_response = self.client.post('/feed/upload_cv/', {
            'cv_file': test_file
        })
        
        # Should not result in server error
        self.assertNotEqual(upload_response.status_code, 500)
        
        # 4. Make API request with rate limiting
        api_response = self.client.get('/feed/api/applications/')
        
        # Should include rate limit headers
        if hasattr(api_response, 'get'):
            rate_limit_headers = [
                'X-RateLimit-Limit',
                'X-RateLimit-Remaining',
                'X-RateLimit-Reset'
            ]
            # Note: Headers might not be present in test environment
    
    def test_security_under_load(self):
        """
        Test security features under simulated load
        """
        # Simulate multiple rapid requests
        for i in range(10):
            response = self.client.get('/feed/')
            # Should not cause security failures
            self.assertNotEqual(response.status_code, 500)
            time.sleep(0.1)  # Small delay to avoid overwhelming test system


class SecurityAuditTest(SecurityTestCase):
    """
    Security audit and compliance tests
    """

    def test_no_hardcoded_secrets(self):
        """
        Test that no secrets are hardcoded in the codebase
        """
        # This would typically scan source files for patterns
        # For now, just check that settings use environment variables
        from django.conf import settings

        # Check that SECRET_KEY is not a default value
        self.assertNotEqual(settings.SECRET_KEY, 'django-insecure-default-key')

        # Check that DEBUG is False in production
        # (This test would be environment-specific)
        if hasattr(settings, 'ENVIRONMENT') and settings.ENVIRONMENT == 'production':
            self.assertFalse(settings.DEBUG)

    def test_database_security(self):
        """
        Test database security configurations
        """
        from django.db import connection

        # Check that database connections use SSL in production
        # This would be environment-specific
        db_settings = connection.settings_dict

        # Ensure no default passwords are used
        self.assertNotIn('password', db_settings.get('PASSWORD', '').lower())
        self.assertNotIn('admin', db_settings.get('PASSWORD', '').lower())

    def test_security_middleware_order(self):
        """
        Test that security middleware is in correct order
        """
        from django.conf import settings

        middleware = settings.MIDDLEWARE

        # Security middleware should be early in the list
        security_middleware = [
            'django.middleware.security.SecurityMiddleware',
            'feed.security_middleware.SecurityHeadersMiddleware',
            'feed.security_middleware.RateLimitMiddleware',
        ]

        for middleware_class in security_middleware:
            self.assertIn(middleware_class, middleware)

    def test_sensitive_data_handling(self):
        """
        Test that sensitive data is properly handled
        """
        # Test that passwords are hashed
        user = User.objects.create_user(
            username='test_hash',
            password='TestPassword123!'
        )

        # Password should be hashed, not stored in plain text
        self.assertNotEqual(user.password, 'TestPassword123!')
        self.assertTrue(user.password.startswith('pbkdf2_sha256$'))

    def test_error_handling_security(self):
        """
        Test that error handling doesn't leak sensitive information
        """
        # Try to trigger an error and check response
        response = self.client.get('/feed/nonexistent_endpoint/')

        # Should return 404, not expose internal details
        self.assertEqual(response.status_code, 404)

        # Response should not contain sensitive information
        response_content = response.content.decode()
        sensitive_patterns = [
            'Traceback',
            'Exception',
            'Database',
            'SECRET_KEY',
            'password',
        ]

        for pattern in sensitive_patterns:
            self.assertNotIn(pattern, response_content)
