# PostJobFree Integration Documentation

## Overview

This document describes the simplified PostJobFree application processing system integrated into the Canvider recruitment platform. The system processes job applications received via email from PostJobFree.com using a simple button-triggered approach, extracts candidate information from PDF resumes using AI, and creates application records in the database.

## Features

### Email Processing
- Manual processing triggered by "Refresh Applicants" button in the UI
- Parses email subjects with format: `[email] applied to [job_title] job in [location]`
- Extracts PDF attachments containing candidate resumes
- Marks processed emails to avoid duplicates

### PDF Text Extraction
- Multi-method PDF text extraction (pdfplumber, PyPDF2, OCR)
- Handles both text-based and image-based PDFs
- OCR fallback using Tesseract for scanned documents
- Comprehensive error handling and logging

### AI-Powered Data Extraction
- Uses DeepSeek AI to extract candidate information from resume text:
  - First name and last name
  - Email address and phone number
  - Current location
  - Current position and employer
  - Total years of experience
  - Education level
- Intelligent parsing with proper fallback to default values

### Job Matching
- Matches applications to existing vacancies based on job title
- Supports both exact and partial matching
- Only processes applications for active vacancies

### Database Integration
- Creates or updates candidate records
- Creates application records with proper relationships
- Stores extracted resume text for AI analysis
- Handles missing data gracefully with default values

### Manual Processing
- Simple button-triggered processing in the UI
- Manual trigger endpoint for API access
- Comprehensive logging and error handling
- Processes up to 10 emails per request

## Architecture

### Components

1. **PDF Processing** (`feed/utils/pdf_utils.py`)
   - `extract_text_from_pdf()`: Main text extraction function
   - `validate_pdf_content()`: PDF validation
   - `get_pdf_info()`: PDF metadata extraction

2. **Application Parser** (`feed/utils/application_parser.py`)
   - `PostJobFreeParser`: Simple email subject parsing class
   - Email subject parsing only (AI handles data extraction)

3. **AI Services** (`canviderAi/services.py`)
   - `extract_candidate_info_from_cv()`: AI-powered candidate information extraction
   - Uses DeepSeek API for intelligent parsing
   - Returns structured JSON data

4. **API Endpoints** (`feed/views.py`)
   - Manual trigger endpoint for UI button
   - Processing functions with database integration

5. **UI Integration** (`templates/people.html`)
   - "Refresh Applicants" button in the people page
   - JavaScript handling for API calls and user feedback

### Database Schema

The system uses existing models with the following key relationships:

- `Candidate`: Stores candidate information
- `Application`: Links candidates to vacancies
- `ApplicationCvText`: Stores extracted resume text and AI analysis
- `Vacancy`: Job postings to match against

## Installation and Setup

### Dependencies

The following packages are added to `requirements.txt`:
```
PyPDF2==3.0.1
pdfplumber==0.11.4
pytesseract==0.3.13
pdf2image==1.17.0
```

### System Dependencies

The Docker container includes:
- `tesseract-ocr`: OCR engine
- `tesseract-ocr-eng`: English language pack
- `poppler-utils`: PDF utilities
- `libpoppler-cpp-dev`: Poppler development libraries

### Docker Configuration

The system includes:
1. **Main web service**: Runs the Django application with PostJobFree integration

## Usage

### UI Button Processing

1. Navigate to the People page in the Canvider application
2. Click the "Refresh Applicants" button in the header
3. The system will process PostJobFree emails and show a success/error message
4. The page will reload to show any new applicants

### Manual API Processing

#### Via API Endpoint
```bash
curl -X POST http://localhost:8000/api/process-postjobfree/
```

#### Via Python Function
```python
from feed.views import process_postjobfree_applications_manual
processed_count = process_postjobfree_applications_manual()
```

### Testing

Run the test suite to verify functionality:
```bash
python test_postjobfree_integration.py
```

## Configuration

### Environment Variables

Required email configuration:
- `IMAP_MAIL_HOST`: IMAP server hostname
- `MAIL_USERNAME`: Email username
- `MAIL_PASSWORD`: Email password



### Email Format

PostJobFree emails must follow this format:
```
Subject: [candidate_email] applied to [job_title] job in [location]
Body: Resume content and candidate information
Attachment: PDF resume file
```

Example:
```
Subject: <EMAIL> applied to Software Engineer job in Warsaw, Poland
```

## Monitoring and Logging

### Log Files

The system creates detailed logs in:
- Console output (Docker logs)
- `postjobfree_processing.log` file

### Log Levels

- **INFO**: Successful processing events
- **WARNING**: Non-critical issues (missing data, parsing failures)
- **ERROR**: Critical failures requiring attention

### Monitoring Commands

```bash
# View Docker logs
docker logs django-docker

# View processing logs in Django logs
docker logs django-docker | grep "PostJobFree"
```

## Error Handling

### Common Issues and Solutions

1. **PDF Text Extraction Fails**
   - System tries multiple extraction methods
   - Falls back to OCR for image-based PDFs
   - Logs detailed error information

2. **No Matching Vacancy Found**
   - Applications are skipped with warning log
   - Consider creating matching vacancy or updating job titles

3. **Email Connection Issues**
   - Check email credentials and IMAP settings
   - Verify network connectivity
   - Review firewall settings

4. **Database Errors**
   - All operations use database transactions
   - Failed applications don't affect successful ones
   - Check database connectivity and permissions

### Data Quality

The system handles missing or invalid data by:
- Using default values for optional fields
- Skipping applications with critical missing data
- Logging all data quality issues
- Continuing processing despite individual failures

## Future Enhancements

### Planned Features

1. **AI-Powered Parsing**: Enhanced candidate information extraction using LLM
2. **Duplicate Detection**: Advanced duplicate candidate detection
3. **Email Templates**: Automated responses to candidates
4. **Analytics Dashboard**: Processing statistics and success rates
5. **Multi-Portal Support**: Support for additional job boards

### Scalability

The system is designed for simplicity and easy maintenance:
- Simple button-triggered processing
- AI-powered data extraction for accuracy
- Modular architecture for easy extension
- Comprehensive logging for monitoring

## Support

For issues or questions:
1. Check the logs for detailed error information
2. Run the test suite to verify functionality
3. Review this documentation for configuration details
4. Contact the development team for assistance
