# Turkish translation for Canvider
# Copyright (C) 2025 Canvider
# This file is distributed under the same license as the Canvider package.
#
msgid ""
msgstr ""
"Project-Id-Version: Canvider 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-23 14:37+0000\n"
"PO-Revision-Date: 2025-06-23 14:37+0000\n"
"Last-Translator: Canvider Team\n"
"Language-Team: Turkish\n"
"Language: tr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: feed/models.py:64
msgid "Active"
msgstr "Aktif"

#: feed/models.py:65
msgid "Draft"
msgstr "Taslak"

#: feed/models.py:66
msgid "Closed"
msgstr "Kapalı"

#: feed/models.py:67
msgid "On-Hold"
msgstr "Beklemede"

#: feed/models.py:68
msgid "Archived"
msgstr "Arşivlendi"

#: feed/models.py:69
msgid "Reviewing"
msgstr "Gözden geçiriliyor"

#: feed/models.py:70
msgid "Deleted"
msgstr "Silindi"

#: feed/models.py:204 templates/applicant_dev.html:1044
msgid "New"
msgstr "Yeni"

#: feed/models.py:205 templates/applicant_dev.html:1045
msgid "Review #1"
msgstr "Görüşme #1"

#: feed/models.py:206 templates/applicant_dev.html:1046
msgid "Review #2"
msgstr "Görüşme #2"

#: feed/models.py:207 templates/applicant_dev.html:1047
msgid "Review #3"
msgstr "Görüşme #3"

#: feed/models.py:208 templates/applicant_dev.html:1048
msgid "Review #4"
msgstr "Görüşme #4"

#: feed/models.py:209 templates/applicant_dev.html:1049
msgid "Review #5"
msgstr "Görüşme #5"

#: feed/models.py:210 templates/applicant_dev.html:1050
msgid "Ready for Decision"
msgstr "Karar için hazır"

#: feed/models.py:211 templates/applicant_dev.html:1051
msgid "Eliminated"
msgstr "Elimine edildi"

#: feed/models.py:212 templates/applicant_dev.html:1052
msgid "Offer Made"
msgstr "Teklif verildi"

#: feed/models.py:213 templates/applicant_dev.html:1053
msgid "Candidate Accepted"
msgstr "Aday kabul etti"

#: feed/models.py:214 templates/jobs.html:141
msgid "Hired"
msgstr "İşe alındı"

#: feed/models.py:215 templates/applicant_dev.html:1054
msgid "Candidate Rejected"
msgstr "Aday reddetti"

#: feed/models.py:241
msgid "Phone Call"
msgstr "Telefon görüşmesi"

#: feed/models.py:242
msgid "Video Call"
msgstr "Video görüşmesi"

#: feed/models.py:243
msgid "Online Interview"
msgstr "Online görüşmesi"

#: feed/models.py:244
msgid "Technical Assessment"
msgstr "Teknik değerlendirme"

#: feed/models.py:245
msgid "Final Interview"
msgstr "Son görüşme"

#: feed/models.py:246
msgid "Face to Face Interview"
msgstr "Yüz yüze görüşme"

#: feed/models.py:247
msgid "Office Visit"
msgstr "Ofis ziyareti"

#: feed/models.py:248
msgid "Other"
msgstr "Diğer"

#: feed/views.py:87
#, python-format
msgid "<strong>%(name)s</strong> applied for <strong>%(position)s</strong>"
msgstr "<strong>%(name)s</strong>, <strong>%(position)s</strong> pozisyonuna başvurdu"

#: feed/views.py:123
#, python-format
msgid ""
"<strong>%(name)s</strong> moved to <strong>%(state)s</strong> for "
"<strong>%(position)s</strong>"
msgstr ""
" <strong>%(name)s</strong>, başvurduğu <strong>%(position)s</strong> "
"pozisyonu için <strong>%(state)s</strong> durumuna taşındı "

#: feed/views.py:149
#, python-format
msgid "A new vacancy <strong>%(vacancy_title)s</strong> is published"
msgstr "Yeni iş ilanı: <strong>%(vacancy_title)s</strong> yayınlandı"

#: feed/views.py:172
msgid "New comment on application of"
msgstr "Yeni yorum eklendi:"

#: feed/views.py:174
msgid "New comment on application ID"
msgstr "Yeni yorum eklendi:"

#: feed/views.py:858
msgid "Profile photo changed successfully!"
msgstr "Profil fotoğrafı başarıyla değiştirildi!"

#: feed/views.py:863
msgid "Please select a photo."
msgstr "Lütfen bir fotoğraf seçin."

#: feed/views.py:1264
#, python-format
msgid "Language changed to %(language)s"
msgstr "Dil %(language)s olarak değiştirildi"

#: feed/views.py:1268
msgid "Invalid language selection"
msgstr "Geçersiz dil seçimi"

#: feed/views.py:1297 templates/feed.html:16
msgid "Dashboard"
msgstr "Panel"

#: feed/views.py:1449
msgid "Invitation mail sent successfully!"
msgstr "Davet e-postası başarıyla gönderildi!"

#: feed/views.py:1452 feed/views.py:1595
msgid "Failed to send the invitation. Please check the form."
msgstr "Davet gönderilemedi. Lütfen formu kontrol edin."

#: feed/views.py:1484
msgid "Passwords do not match."
msgstr "Şifreler eşleşmiyor."

#: feed/views.py:1521
msgid "No employer found to associate with this account."
msgstr "Bu hesapla ilişkilendirilecek işveren bulunamadı."

#: feed/views.py:1531
msgid "Registration completed successfully! You can now log in."
msgstr "Kayıt başarıyla tamamlandı! Artık giriş yapabilirsiniz."

#: feed/views.py:1535
#, python-format
msgid "Error creating account: %(error)s"
msgstr "Hesap oluşturulurken hata: %(error)s"

#: feed/views.py:1577
msgid "Invitation sent successfully!"
msgstr "Davet başarıyla gönderildi!"

#: feed/views.py:1605
msgid "User removed successfully!"
msgstr "Kullanıcı başarıyla kaldırıldı!"

#: feed/views.py:1618
msgid "User status changed successfully!"
msgstr "Kullanıcı durumu başarıyla değiştirildi!"

#: feed/views.py:1731
msgid "Talent request sent successfully! Our team will get back to you soon."
msgstr ""
"Yetenek talebi başarıyla gönderildi! Ekibimiz en kısa sürede size dönüş "
"yapacak."

#: feed/views.py:1735
msgid "Invalid request method."
msgstr "Geçersiz istek yöntemi."

#: templates/applicant_dev.html:30
msgid "Applied for:"
msgstr "Başvurduğu pozisyon:"

#: templates/applicant_dev.html:89 templates/applicant_dev.html:825
msgid "Schedule Interview"
msgstr "Görüşme Planla"

#: templates/applicant_dev.html:99
msgid "Change State"
msgstr "Durumu Değiştir"

#: templates/applicant_dev.html:114
msgid "Dashboard & AI"
msgstr "Panel & AI"

#: templates/applicant_dev.html:123
msgid "Candidate Background"
msgstr "Aday Hakkında"

#: templates/applicant_dev.html:132 templates/applicant_dev.html:559
msgid "Resume"
msgstr "Özgeçmiş"

#: templates/applicant_dev.html:141
msgid "Journey"
msgstr "Süreç"

#: templates/applicant_dev.html:150 templates/applicant_dev.html:740
msgid "Comments"
msgstr "Yorumlar"

#: templates/applicant_dev.html:159 templates/applicant_dev.html:642
msgid "Emails"
msgstr "E-postalar"

#: templates/applicant_dev.html:167 templates/applicant_dev.html:809
msgid "Job Details"
msgstr "İş Detayları"

#: templates/applicant_dev.html:191
msgid "Profile Match Analysis"
msgstr "Profil Uyum Analizi"

#: templates/applicant_dev.html:199
msgid "Key Highlights"
msgstr "Öne Çıkan Özellikler"

#: templates/applicant_dev.html:225
msgid "AI analysis will provide candidate highlights."
msgstr "AI analizi aday öne çıkan özelliklerini sağlayacak."

#: templates/applicant_dev.html:235
msgid "Areas for Improvement"
msgstr "Gelişim Alanları"

#: templates/applicant_dev.html:272
msgid "Candidate Summary"
msgstr "Aday Özeti"

#: templates/applicant_dev.html:321
msgid ""
"Based on the AI analysis, when the resume is compared to the job "
"requirements,"
msgstr "AI analizine göre, özgeçmiş iş gereksinimleriyle karşılaştırıldığında,"

#: templates/applicant_dev.html:323
msgid "This candidate is an"
msgstr "Bu aday"

#: templates/applicant_dev.html:324
msgid "excellent match"
msgstr "mükemmel bir eşleşme"

#: templates/applicant_dev.html:326 templates/applicant_dev.html:329
#: templates/applicant_dev.html:332
msgid "This candidate is a"
msgstr "Bu aday"

#: templates/applicant_dev.html:327
msgid "good match"
msgstr "iyi bir eşleşme"

#: templates/applicant_dev.html:330
msgid "fair match"
msgstr "orta bir eşleşme"

#: templates/applicant_dev.html:333
msgid "weak match"
msgstr "zayıf bir eşleşme"

#: templates/applicant_dev.html:336
msgid "Analyze the CV with AI to see match details."
msgstr "Eşleşme detaylarını görmek için CV'yi AI ile analiz edin."

#: templates/applicant_dev.html:363
msgid "AI Analysis Available"
msgstr "AI Analizi Mevcut"

#: templates/applicant_dev.html:366
msgid "Leverage AI to analyze this candidate's CV against the job description."
msgstr ""
"Bu adayın CV'sini iş tanımına karşı analiz etmek için AI'dan yararlanın."

#: templates/applicant_dev.html:387
msgid "Analyze with CanviderAI"
msgstr "CanviderAI ile Analiz Et"

#: templates/applicant_dev.html:402
msgid "Analyzing CV..."
msgstr "CV analiz ediliyor..."

#: templates/applicant_dev.html:403
msgid "This may take a moment"
msgstr "Bu biraz zaman alabilir"

#: templates/applicant_dev.html:428 templates/applicant_dev.html:449
msgid "Analysis complete"
msgstr "Analiz tamamlandı"

#: templates/applicant_dev.html:460
msgid "Candidate Facts"
msgstr "Aday Bilgileri"

#: templates/applicant_dev.html:467
msgid "Applied Position"
msgstr "Başvurulan Pozisyon"

#: templates/applicant_dev.html:473
msgid "Candidate's Address"
msgstr "Adayın Adresi"

#: templates/applicant_dev.html:476 templates/applicant_dev.html:499
#: templates/applicant_dev.html:506 templates/applicant_dev.html:529
#: templates/applicant_dev.html:536 templates/people.html:118
#: templates/people.html:125 templates/people.html:132
msgid "Not analyzed"
msgstr "Analiz edilmedi"

#: templates/applicant_dev.html:480 templates/people.html:61
msgid "Application Date"
msgstr "Başvuru Tarihi"

#: templates/applicant_dev.html:486
msgid "Application Portal"
msgstr "Başvuru Portalı"

#: templates/applicant_dev.html:496
msgid "Latest/Current Position"
msgstr "Güncel/En Son Pozisyon"

#: templates/applicant_dev.html:503
msgid "Latest/Current Employer"
msgstr "Güncel/En Son İşveren"

#: templates/applicant_dev.html:510 templates/jobs.html:69
#: templates/manage_permissions.html:117 templates/manage_permissions.html:264
#: templates/people.html:41 templates/people.html:88
#: templates/published_job_details.html:201
msgid "Status"
msgstr "Durum"

#: templates/applicant_dev.html:516
msgid "Application ID"
msgstr "Başvuru ID"

#: templates/applicant_dev.html:526
msgid "Total Experience"
msgstr "Toplam Tecrübe"

#: templates/applicant_dev.html:533
msgid "Education Level"
msgstr "Eğitim Düzeyi"

#: templates/applicant_dev.html:540
msgid "Notice Period"
msgstr "İhbar Süresi"

#: templates/applicant_dev.html:546
msgid "Latest Update"
msgstr "Son Güncelleme"

#: templates/applicant_dev.html:564
msgid "Uploaded on"
msgstr "Yüklenme Tarihi"

#: templates/applicant_dev.html:581
msgid "Your browser does not support PDFs."
msgstr "Tarayıcınız PDF'leri desteklemiyor."

#: templates/applicant_dev.html:585
msgid "Download the CV"
msgstr "CV'yi İndir"

#: templates/applicant_dev.html:601
msgid "Application Stages"
msgstr "Başvuru Aşamaları"

#: templates/applicant_dev.html:618
msgid "Started on:"
msgstr "Başlangıç:"

#: templates/applicant_dev.html:630
msgid "No stages available for this application."
msgstr "Bu aday için hiçbir durum seçilebilir değil."

#: templates/applicant_dev.html:648
msgid "Email History"
msgstr "E-posta Geçmişi"

#: templates/applicant_dev.html:658
msgid "From:"
msgstr "Gönderen:"

#: templates/applicant_dev.html:659
msgid "To:"
msgstr "Alıcı:"

#: templates/applicant_dev.html:662 templates/applicant_dev.html:1091
msgid "Subject:"
msgstr "Konu:"

#: templates/applicant_dev.html:677
msgid "No emails found."
msgstr "E-posta bulunamadı."

#: templates/applicant_dev.html:684 templates/applicant_dev.html:730
msgid "Send Email"
msgstr "E-posta Gönder"

#: templates/applicant_dev.html:699
msgid "Subject"
msgstr "Konu"

#: templates/applicant_dev.html:709 templates/published_job_details.html:364
msgid "Email Body"
msgstr "E-posta İçeriği"

#: templates/applicant_dev.html:752
msgid "Add a comment"
msgstr "Yorum Ekle"

#: templates/applicant_dev.html:758
msgid "Add your comment here..."
msgstr "Yorumunuzu buraya girin..."

#: templates/applicant_dev.html:763
msgid "Post Comment"
msgstr "Yorumu Gönder"

#: templates/applicant_dev.html:796
msgid "No comments yet. Be the first to comment!"
msgstr "Henüz yorum yapılmadı. İlk yorumu siz yapın!"

#: templates/applicant_dev.html:831 templates/feed.html:256
msgid "Event Title"
msgstr "Etkinlik Başlığı"

#: templates/applicant_dev.html:842 templates/feed.html:266
msgid "Event Type"
msgstr "Etkinlik Türü"

#: templates/applicant_dev.html:848 templates/feed.html:272
msgid "Select an event type"
msgstr "Bir etkinlik türü seçin"

#: templates/applicant_dev.html:856 templates/feed.html:280
#: templates/manage_permissions.html:49
msgid "Recruiters"
msgstr "İşe Alım Uzmanları"

#: templates/applicant_dev.html:861 templates/feed.html:285
msgid "Select one or many recruiters"
msgstr "Bir veya birden fazla işe alım uzmanı seçin"

#: templates/applicant_dev.html:871 templates/feed.html:338
#: templates/people.html:31 templates/people.html:87 templates/profile.html:74
#: templates/profile.html:115
msgid "Position"
msgstr "Pozisyon"

#: templates/applicant_dev.html:888 templates/feed.html:360
msgid "Candidate"
msgstr "Aday"

#: templates/applicant_dev.html:904
msgid "Date"
msgstr "Tarih"

#: templates/applicant_dev.html:913 templates/feed.html:374
msgid "Start Time"
msgstr "Başlangıç Zamanı"

#: templates/applicant_dev.html:919 templates/feed.html:379
msgid "End Time"
msgstr "Bitiş Zamanı"

#: templates/applicant_dev.html:926 templates/feed.html:385
msgid "Meeting Link"
msgstr "Toplantı Linki"

#: templates/applicant_dev.html:945 templates/feed.html:403
msgid "Inform invitees by E-mail"
msgstr "Davetlileri E-posta ile bilgilendir"

#: templates/applicant_dev.html:950 templates/feed.html:408
msgid "Color"
msgstr "Renk"

#: templates/applicant_dev.html:952 templates/feed.html:410
msgid "Blue"
msgstr "Mavi"

#: templates/applicant_dev.html:953 templates/feed.html:411
msgid "Light Blue"
msgstr "Açık Mavi"

#: templates/applicant_dev.html:954 templates/feed.html:412
msgid "Purple"
msgstr "Mor"

#: templates/applicant_dev.html:955 templates/feed.html:413
msgid "Pink"
msgstr "Pembe"

#: templates/applicant_dev.html:962 templates/applicant_dev.html:1119
#: templates/create_job_template.html:228 templates/feed.html:419
#: templates/job_details.html:157 templates/manage_permissions.html:413
#: templates/manage_permissions.html:463 templates/profile.html:125
#: templates/profile.html:152 templates/profile.html:184
#: templates/published_job_details.html:301
#: templates/published_job_details.html:386
#: templates/published_job_details.html:446
msgid "Cancel"
msgstr "İptal"

#: templates/applicant_dev.html:965 templates/feed.html:422
msgid "Save Event"
msgstr "Etkinliği Kaydet"

#: templates/applicant_dev.html:1024
msgid "Change Application Status"
msgstr "Başvuru Durumunu Değiştir"

#: templates/applicant_dev.html:1042
msgid "New Status"
msgstr "Yeni Durum"

#: templates/applicant_dev.html:1058 templates/published_job_details.html:353
msgid "Internal Notes"
msgstr "Dahili Notlar"

#: templates/applicant_dev.html:1058 templates/published_job_details.html:353
msgid "(visible only to recruiters)"
msgstr "(sadece işe alım uzmanları tarafından görülebilir)"

#: templates/applicant_dev.html:1072
msgid "Notify candidate about this status change via email"
msgstr "Bu Durum gelişmesi için adayı e-posta ile bilgilendir"

#: templates/applicant_dev.html:1078
msgid "Email Message"
msgstr "E-posta Mesajı"

#: templates/applicant_dev.html:1078
msgid "(will be included in the email to the candidate)"
msgstr "(adaya gönderilen e-postaya dahil edilecek)"

#: templates/applicant_dev.html:1089
msgid "Email Preview"
msgstr "E-posta Önizleme"

#: templates/applicant_dev.html:1091
msgid "Your application status has been updated"
msgstr "Başvuru durumunuz güncellendi"

#: templates/applicant_dev.html:1121
msgid "Save Change"
msgstr "Değişikliği Kaydet"

#: templates/create_job.html:3
msgid "Create Job Position"
msgstr "İş Pozisyonu Oluştur"

#: templates/create_job.html:8
msgid "Basic Information"
msgstr "Temel Bilgiler"

#: templates/create_job.html:10 templates/job_details.html:1151
#: templates/job_preview_publish.html:590
msgid "Role Title"
msgstr "Rol Başlığı"

#: templates/create_job.html:14
msgid "e.g. Senior Software Engineer"
msgstr "örn. Kıdemli Yazılım Mühendisi"

#: templates/create_job.html:18 templates/job_details.html:1155
#: templates/job_preview_publish.html:594
msgid "Office Location"
msgstr "Ofis Konumu"

#: templates/create_job.html:21
msgid "Select office location"
msgstr "Ofis konumu seçin"

#: templates/create_job.html:31
msgid "No office locations found. Please"
msgstr "Ofis konumu bulunamadı. Lütfen"

#: templates/create_job.html:32
msgid "add office locations"
msgstr "ofis konumları ekleyin"

#: templates/create_job.html:32 templates/create_job.html:53
#: templates/create_job.html:74
msgid "in your preferences first."
msgstr "önce tercihlerinizde."

#: templates/create_job.html:36
msgid "No locations available"
msgstr "Mevcut konum yok"

#: templates/create_job.html:41 templates/job_details.html:1159
#: templates/job_preview_publish.html:598
msgid "Work Schedule"
msgstr "Çalışma Programı"

#: templates/create_job.html:44 templates/create_job.html:65
msgid "Select an option"
msgstr "Bir seçenek seçin"

#: templates/create_job.html:52
msgid "No work schedules found. Please"
msgstr "Çalışma programı bulunamadı. Lütfen"

#: templates/create_job.html:53
msgid "add work schedules"
msgstr "çalışma programları ekleyin"

#: templates/create_job.html:57
msgid "No work schedules available"
msgstr "Mevcut çalışma programı yok"

#: templates/create_job.html:62 templates/job_details.html:1163
#: templates/job_preview_publish.html:604
msgid "Office Schedule"
msgstr "Ofis Programı"

#: templates/create_job.html:73
msgid "No office schedules found. Please"
msgstr "Ofis programı bulunamadı. Lütfen"

#: templates/create_job.html:74
msgid "add office schedules"
msgstr "ofis programları ekleyin"

#: templates/create_job.html:78
msgid "No office schedules available"
msgstr "Mevcut ofis programı yok"

#: templates/create_job.html:86
msgid "Skills Requirements"
msgstr "Yetenek Gereksinimleri"

#: templates/create_job.html:88
msgid "Skill"
msgstr "Yetenek"

#: templates/create_job.html:93
msgid "e.g. JavaScript"
msgstr "örn. JavaScript"

#: templates/create_job.html:95 templates/create_job.html:169
msgid "Add"
msgstr "Ekle"

#: templates/create_job.html:98
msgid "Choose Skills"
msgstr "Yetenekleri Seçin"

#: templates/create_job.html:112
msgid "Selected Skills"
msgstr "Seçilen Yetenekler"

#: templates/create_job.html:115
msgid "No skills selected yet"
msgstr "Henüz yetenek seçilmedi"

#: templates/create_job.html:125
msgid "Salary Details (Optional)"
msgstr "Maaş Detayları (İsteğe Bağlı)"

#: templates/create_job.html:128
msgid "Minimum Salary"
msgstr "Minimum Maaş"

#: templates/create_job.html:132
msgid "Enter minimum salary"
msgstr "Minimum maaş girin"

#: templates/create_job.html:136
msgid "Maximum Salary"
msgstr "Maksimum Maaş"

#: templates/create_job.html:140
msgid "Enter maximum salary"
msgstr "Maksimum maaş girin"

#: templates/create_job.html:144
msgid "Currency"
msgstr "Para Birimi"

#: templates/create_job.html:146
msgid "Select currency"
msgstr "Para birimi seçin"

#: templates/create_job.html:162
msgid "Benefits and Highlights (Optional)"
msgstr "Yan Haklar ve Öne Çıkanlar (İsteğe Bağlı)"

#: templates/create_job.html:167
msgid "e.g. Yearly Bonuses"
msgstr "örn. Yıllık Bonuslar"

#: templates/create_job.html:172
msgid "Choose Benefits"
msgstr "Yan Hakları Seçin"

#: templates/create_job.html:175
msgid "Dental Coverage"
msgstr "Diş Sigortası"

#: templates/create_job.html:178
msgid "Private Health Coverage"
msgstr "Özel Sağlık Sigortası"

#: templates/create_job.html:181
msgid "Gym membership"
msgstr "Spor Salonu Üyeliği"

#: templates/create_job.html:184
msgid "Sign-in Bonus"
msgstr "Giriş Bonusu"

#: templates/create_job.html:187
msgid "Relocation Package"
msgstr "Taşınma Paketi"

#: templates/create_job.html:190
msgid "Company Vehicle"
msgstr "Şirket Aracı"

#: templates/create_job.html:192
msgid "Food Card"
msgstr "Yemek Kartı"

#: templates/create_job.html:194
msgid "Snacks & Coffee"
msgstr "Atıştırmalık ve Kahve"

#: templates/create_job.html:197
msgid "Pet Friendly Office"
msgstr "Evcil Hayvan Dostu Ofis"

#: templates/create_job.html:201
msgid "Selected Benefits & Highlights"
msgstr "Seçilen Yan Haklar ve Öne Çıkanlar"

#: templates/create_job.html:204
msgid "No benefits or highlights selected yet"
msgstr "Henüz yan hak veya öne çıkan seçilmedi"

#: templates/create_job.html:211 templates/job_details.html:130
#: templates/job_preview_publish.html:159
msgid "Discard"
msgstr "İptal Et"

#: templates/create_job.html:212
msgid "Next"
msgstr "İleri"

#: templates/create_job_template.html:13 templates/create_job_template.html:19
#: templates/settings.html:41
msgid "Templates"
msgstr "Şablonlar"

#: templates/create_job_template.html:14
msgid "Create and manage reusable job description templates"
msgstr "Yeniden kullanılabilir iş tanımı şablonları oluşturun ve yönetin"

#: templates/create_job_template.html:17 templates/job_preferences.html:15
#: templates/manage_permissions.html:15 templates/navbar.html:137
#: templates/settings.html:9
msgid "Settings"
msgstr "Ayarlar"

#: templates/create_job_template.html:31
msgid "Total Templates"
msgstr "Toplam Şablon"

#: templates/create_job_template.html:41
msgid "Created This Month"
msgstr "Bu Ay Oluşturulan"

#: templates/create_job_template.html:51
msgid "Jobs Created from Templates"
msgstr "Şablonlardan Oluşturulan İşler"

#: templates/create_job_template.html:61
msgid "Time Saved Using Templates"
msgstr "Şablonlar Kullanarak Kazanılan Zaman"

#: templates/create_job_template.html:71
msgid "My Templates"
msgstr "Şablonlarım"

#: templates/create_job_template.html:74 templates/create_job_template.html:105
#: templates/create_job_template.html:118
msgid "New Template"
msgstr "Yeni Şablon"

#: templates/create_job_template.html:80
msgid "Search templates..."
msgstr "Şablon ara..."

#: templates/create_job_template.html:91
msgid "Updated"
msgstr "Güncellendi"

#: templates/create_job_template.html:94
msgid "Used 1 time"
msgstr "1 kez kullanıldı"

#: templates/create_job_template.html:96
msgid "Used"
msgstr "Kullanıldı"

#: templates/create_job_template.html:96
msgid "times"
msgstr "kez"

#: templates/create_job_template.html:107
msgid "Not saved yet"
msgstr "Henüz kaydedilmedi"

#: templates/create_job_template.html:108
msgid "Not used yet"
msgstr "Henüz kullanılmadı"

#: templates/create_job_template.html:118
msgid "Enter template title"
msgstr "Şablon başlığını girin"

#: templates/create_job_template.html:121
#: templates/create_job_template.html:219
msgid "Delete Template"
msgstr "Şablonu Sil"

#: templates/create_job_template.html:126 templates/job_details.html:158
msgid "Save Template"
msgstr "Şablonu Kaydet"

#: templates/create_job_template.html:136
msgid "Heading 1"
msgstr "Başlık 1"

#: templates/create_job_template.html:137
msgid "Heading 2"
msgstr "Başlık 2"

#: templates/create_job_template.html:138
msgid "Heading 3"
msgstr "Başlık 3"

#: templates/create_job_template.html:139
msgid "Paragraph"
msgstr "Paragraf"

#: templates/create_job_template.html:143
msgid "Font Size"
msgstr "Yazı Boyutu"

#: templates/create_job_template.html:144
msgid "Very Small"
msgstr "Çok Küçük"

#: templates/create_job_template.html:145
msgid "Small"
msgstr "Küçük"

#: templates/create_job_template.html:146
msgid "Normal"
msgstr "Normal"

#: templates/create_job_template.html:147
msgid "Medium"
msgstr "Orta"

#: templates/create_job_template.html:148
msgid "Large"
msgstr "Büyük"

#: templates/create_job_template.html:149
msgid "Very Large"
msgstr "Çok Büyük"

#: templates/create_job_template.html:150
msgid "Extra Large"
msgstr "Ekstra Büyük"

#: templates/create_job_template.html:155
msgid "Bold"
msgstr "Kalın"

#: templates/create_job_template.html:156
msgid "Italic"
msgstr "İtalik"

#: templates/create_job_template.html:157
msgid "Underline"
msgstr "Altı Çizili"

#: templates/create_job_template.html:161
msgid "Bullet List"
msgstr "Madde İşaretli Liste"

#: templates/create_job_template.html:162
msgid "Numbered List"
msgstr "Numaralı Liste"

#: templates/create_job_template.html:166
msgid "Align Left"
msgstr "Sola Hizala"

#: templates/create_job_template.html:167
msgid "Align Center"
msgstr "Ortaya Hizala"

#: templates/create_job_template.html:168
msgid "Align Right"
msgstr "Sağa Hizala"

#: templates/create_job_template.html:172
msgid "Insert Link"
msgstr "Link Ekle"

#: templates/create_job_template.html:173
msgid "Remove Link"
msgstr "Link Kaldır"

#: templates/create_job_template.html:177
msgid "Add Emoji"
msgstr "Emoji Ekle"

#: templates/create_job_template.html:202
msgid "Enter your template content here..."
msgstr "Şablon içeriğinizi buraya girin..."

#: templates/create_job_template.html:208 templates/job_details.html:123
msgid "characters"
msgstr "karakter"

#: templates/create_job_template.html:224
msgid "Are you sure you want to delete the"
msgstr "Silmek istediğinize emin misiniz?"

#: templates/create_job_template.html:224
msgid "template? This action cannot be undone."
msgstr "Bu işlem geri alınamaz."

#: templates/create_job_template.html:229 templates/job_preferences.html:70
msgid "Delete"
msgstr "Sil"

#: templates/feed.html:19 templates/feed.html:45
msgid "Loading..."
msgstr "Yükleniyor..."

#: templates/feed.html:31
msgid "Calendar"
msgstr "Takvim"

#: templates/feed.html:33
msgid "Day"
msgstr "Gün"

#: templates/feed.html:34
msgid "Week"
msgstr "Hafta"

#: templates/feed.html:35
msgid "Month"
msgstr "Ay"

#: templates/feed.html:50 templates/jobs.html:92 templates/people.html:64
msgid "Today"
msgstr "Bugün"

#: templates/feed.html:55
msgid "Click on a day with colored dots to view events"
msgstr "Renkli noktalarla tıklayarak etkinlikleri görüntüleyin"

#: templates/feed.html:60 templates/feed.html:73
msgid "Mon"
msgstr "Pzt"

#: templates/feed.html:61 templates/feed.html:74
msgid "Tue"
msgstr "Sal"

#: templates/feed.html:62 templates/feed.html:75
msgid "Wed"
msgstr "Çar"

#: templates/feed.html:63 templates/feed.html:76
msgid "Thu"
msgstr "Per"

#: templates/feed.html:64 templates/feed.html:77
msgid "Fri"
msgstr "Cum"

#: templates/feed.html:65 templates/feed.html:78
msgid "Sat"
msgstr "Cmt"

#: templates/feed.html:66 templates/feed.html:79
msgid "Sun"
msgstr "Paz"

#: templates/feed.html:95
msgid "Activity Feed"
msgstr "Güncel Gelişmeler"

#: templates/feed.html:143
msgid "Hot"
msgstr "Popüler"

#: templates/feed.html:143 templates/navbar.html:28
msgid "Jobs"
msgstr "İlanlar"

#: templates/feed.html:146
msgid "View All Jobs"
msgstr "Tüm İlanları Görüntüle"

#: templates/feed.html:162 templates/feed.html:199 templates/jobs.html:128
#: templates/navbar.html:33
msgid "Applicants"
msgstr "Adaylar"

#: templates/feed.html:182
msgid "Monthly Applicant Overview"
msgstr "Aylık Aday Genel Bakış"

#: templates/feed.html:221
msgid "Events for Date"
msgstr "Bu Tarihteki Etkinlikler"

#: templates/feed.html:243
msgid "Add New Event"
msgstr "Yeni Etkinlik Ekle"

#: templates/feed.html:253
msgid "Create New Event"
msgstr "Yeni Etkinlik Oluştur"

#: templates/feed.html:260
msgid "Enter event title"
msgstr "Etkinlik başlığını girin"

#: templates/feed.html:347
msgid "Select the relevant position"
msgstr "İlgili pozisyonu seçin"

#: templates/feed.html:354
msgid "No vacancies available"
msgstr "Su anda pozisyon yok"

#: templates/feed.html:367
msgid "Pick a Vacancy to see candidates"
msgstr "Adayları görmek için bir pozisyon seçin"

#: templates/feed.html:389
msgid "Enter meeting link"
msgstr "Toplantı linkini girin"

#: templates/job_details.html:7 templates/job_details.html:20
#: templates/job_details.html:51 templates/job_preview_publish.html:28
msgid "Job Description"
msgstr "İş Açıklaması"

#: templates/job_details.html:11 templates/job_preview_publish.html:17
msgid "Job Summary"
msgstr "İş Özeti"

#: templates/job_details.html:14 templates/job_preview_publish.html:21
msgid "Loading job details..."
msgstr "İş detayları yükleniyor..."

#: templates/job_details.html:24
msgid "Create new description"
msgstr "Yeni açıklama oluştur"

#: templates/job_details.html:28
msgid "Use saved template"
msgstr "Kayıtlı şablon kullan"

#: templates/job_details.html:33
msgid "Choose a template:"
msgstr "Bir şablon seçin:"

#: templates/job_details.html:35
msgid "Select a template"
msgstr "Şablon seçin"

#: templates/job_details.html:42
msgid "AI Job Description Generator"
msgstr "AI İş Tanımı Oluşturucu"

#: templates/job_details.html:43
msgid ""
"Let AI create a professional job description based on your job details above."
msgstr ""
"AI'ın yukarıdaki iş detaylarınıza dayanarak profesyonel bir iş tanımı "
"oluşturmasına izin verin."

#: templates/job_details.html:46
msgid "Generate with AI"
msgstr "AI ile Oluştur"

#: templates/job_details.html:52
msgid ""
"Describe the position, responsibilities, qualifications, and any other "
"relevant details."
msgstr ""
"Pozisyonu, sorumlulukları, nitelikleri ve diğer ilgili detayları açıklayın."

#: templates/job_details.html:129 templates/job_preview_publish.html:158
msgid "Back"
msgstr "Geri"

#: templates/job_details.html:134
msgid "Update Template"
msgstr "Şablonu Güncelle"

#: templates/job_details.html:137 templates/job_details.html:147
msgid "Save as Template"
msgstr "Şablon Olarak Kaydet"

#: templates/job_details.html:140
msgid "Save & Continue"
msgstr "Kaydet ve Devam Et"

#: templates/job_details.html:152
msgid "Template Title"
msgstr "Şablon Başlığı"

#: templates/job_details.html:153
msgid "Enter a name for this template"
msgstr "Bu şablon için bir isim girin"

#: templates/job_details.html:1139
msgid ""
"No job information found. Please go back and fill out the job details form."
msgstr ""
"Hiç iş bilgisi bulunamadı. Lütfen geri gidin ve ilan detaylarını doldurun."

#: templates/job_details.html:1168 templates/job_preview_publish.html:610
msgid "Salary Details"
msgstr "Maaş Detayları (İsteğe Bağlı)"

#: templates/job_details.html:1173 templates/job_preview_publish.html:617
msgid "Benefits & Highlights"
msgstr "Seçilen Yan Haklar ve Öne Çıkanlar"

#: templates/job_details.html:1183 templates/job_preview_publish.html:633
msgid "Skills"
msgstr "Yetenek"

#: templates/job_preferences.html:11 templates/job_preferences.html:17
#: templates/settings.html:20
msgid "Preferences"
msgstr "Tercihler"

#: templates/job_preferences.html:12
msgid "Configure standard options to streamline your job creation process"
msgstr ""
"İş oluşturma sürecinizi kolaylaştırmak için standart seçenekleri yapılandırın"

#: templates/job_preferences.html:26 templates/job_preferences.html:51
msgid "Work Schedules"
msgstr "Çalışma Zamanları"

#: templates/job_preferences.html:30 templates/job_preferences.html:84
msgid "Office Schedules"
msgstr "Ofis Ziyaret Sıklığı"

#: templates/job_preferences.html:34
msgid "Locations"
msgstr "Konumlar"

#: templates/job_preferences.html:38
msgid "Departments"
msgstr "Departmanlar"

#: templates/job_preferences.html:42
msgid "Language"
msgstr "Dil"

#: templates/job_preferences.html:52
msgid "Define standard work schedule types for your organization"
msgstr "Organizasyonunuz için standart çalışma zamanı türlerini tanımlayın"

#: templates/job_preferences.html:56
msgid "Add Work Schedule"
msgstr "Çalışma Zamanı Ekle"

#: templates/job_preferences.html:64
msgid "Search work schedules..."
msgstr "Çalışma zamanlarını ara..."

#: templates/job_preferences.html:67
msgid "Select All"
msgstr "Tümünü Seç"

#: templates/job_preferences.html:85
msgid "Define where and how employees work"
msgstr "Çalışanların nerede ve nasıl çalıştığını tanımlayın"

#: templates/job_preferences.html:89
msgid "Add Office Schedule"
msgstr "Ofis Programı Ekle"

#: templates/job_preferences.html:183
msgid "Language Settings"
msgstr "Dil Ayarları"

#: templates/job_preferences.html:184
msgid "Choose your preferred language for the application interface"
msgstr "Uygulama arayüzü için tercih ettiğiniz dili seçin"

#: templates/job_preferences.html:189
msgid "Interface Language"
msgstr "Arayüz Dili"

#: templates/job_preferences.html:190
msgid "Select the language you want to use for the application interface"
msgstr "Uygulama arayüzü için kullanmak istediğiniz dili seçin"

#: templates/job_preferences.html:210
msgid "Current"
msgstr "Anlık"

#: templates/job_preferences.html:223 templates/published_job_details.html:412
msgid "Note:"
msgstr "Not:"

#: templates/job_preferences.html:223
msgid ""
"Changing the language will refresh the page to apply the new language "
"settings."
msgstr ""
"Dili değiştirmek, yeni dil ayarlarını uygulamak için sayfayı yenileyecektir."

#: templates/job_preview_publish.html:7 templates/job_preview_publish.html:162
msgid "Publish Job"
msgstr "İlanı Yayınla"

#: templates/job_preview_publish.html:11
msgid "Final Review"
msgstr "Son İnceleme"

#: templates/job_preview_publish.html:12
msgid "Please review the job details before publishing."
msgstr "Lütfen yayınlamadan önce ilan detaylarını inceleyin."

#: templates/job_preview_publish.html:32
msgid "Loading job description..."
msgstr "İlan detayları yükleniyor..."

#: templates/job_preview_publish.html:39
msgid "Publish To"
msgstr "Yayınlanacak Platformlar"

#: templates/job_preview_publish.html:41
msgid ""
"Select the job portals where you want to publish this job posting. <br> <br> "
"<i> if the portal you want to publish to is grayed out, it means that you "
"have not yet adjusted the related configuration settings. </i>"
msgstr ""
"Aşağıdakı portallardan birine yayınlamak istediğinizi seçin. <br> <br> <i> "
"Eğer bir portal gri renkli ise, bu portal için gerekli ayarları henüz "
"yapmadığınız anlamına gelir. </i>"

#: templates/job_preview_publish.html:61
msgid ""
"Professional networking platform with over 750 million users worldwide. "
"Selecting this option will open a new tab for you to complete the job "
"posting."
msgstr ""
"750 milyon kullanıcıya sahip profesyonel ağ platformu. Bu seçeneği seçmek "
"yeni bir tarayıcı sekmesi açacak ve iş ilanınızı paylaşmanıza destek "
"verecektir."

#: templates/job_preview_publish.html:82
msgid ""
"Job and company review site focusing on workplace transparency. Selecting "
"this option will open a new tab for you to complete the job posting."
msgstr ""
"İş ve şirket inceleme sitesi, işyerinde saydamlığı odaklanmaktadır. Bu "
"seçeneği seçmek yeni bir tarayıcı sekmesi açacak ve iş ilanınızı "
"paylaşmanıza destek verecektir."

#: templates/job_preview_publish.html:103
#, python-format
msgid ""
"Specialized job platform for tech and creative professionals powered by "
"Workloupe. Workloupe is 100%% free to use."
msgstr ""
"Teknoloji profesyonelleri için özel bir iş platformu. Workloupe tamamen "
"ücretsizdir."

#: templates/job_preview_publish.html:125
msgid ""
"One of the biggest remote job focused job platforms in the world. Posting to "
"Himalayas is free. "
msgstr ""
"Dünyadaki en büyük uzaktan iş odaklı iş platformlarından biridir. "
"Himalayalara gönderme ücretsizdir."

#: templates/job_preview_publish.html:147
msgid ""
"PostJobFree has more than 7 million jobs, and it's free to post to. Their "
"job portal is focused on simplicity and ease of use."
msgstr ""
"PostJobFree'da 7 milyondan fazla iş bulunur ve yayınlar ücretsizdir. Bu iş "
"portalı basitlik ve kullanım kolaylığına odaklanmaktadır."

#: templates/job_preview_publish.html:580
msgid ""
"No job information found. Please go back and fill out the job details form. "
msgstr ""
"Hiç iş bilgisi bulunamadı. Lütfen geri gidin ve ilan detaylarını doldurun."

#: templates/job_preview_publish.html:658
msgid "No job description found. Please go back and create a job description."
msgstr ""
"Hiç iş açıklaması bulunamadı. Lütfen geri gidin ve bir iş açıklaması "
"oluşturun."

#: templates/jobs.html:8
msgid "Job Listings"
msgstr "İş İlanları"

#: templates/jobs.html:18
msgid "Active Jobs"
msgstr "Aktif İlanlar"

#: templates/jobs.html:28
msgid "Total Applicants"
msgstr "Toplam Aday"

#: templates/jobs.html:38
msgid "Archived Jobs"
msgstr "Arşivlenmiş İlanlar"

#: templates/jobs.html:48
msgid "On-Hold Jobs"
msgstr "Beklemede olan İlanlar"

#: templates/jobs.html:59 templates/profile.html:119
msgid "Department"
msgstr "Departman"

#: templates/jobs.html:61
msgid "All Departments"
msgstr "Tüm Departmanlar"

#: templates/jobs.html:71 templates/manage_permissions.html:212
#: templates/people.html:43
msgid "All Statuses"
msgstr "Tüm Durumlar"

#: templates/jobs.html:79 templates/people.html:51 templates/people.html:89
msgid "Location"
msgstr "Konum"

#: templates/jobs.html:81 templates/people.html:53
msgid "All Locations"
msgstr "Tüm Konumlar"

#: templates/jobs.html:89
msgid "Posted Date"
msgstr "Yayın Tarihi"

#: templates/jobs.html:91
msgid "All Time"
msgstr "Tüm Zamanlar"

#: templates/jobs.html:93 templates/people.html:65
msgid "This Week"
msgstr "Bu Hafta"

#: templates/jobs.html:94 templates/people.html:66
msgid "This Month"
msgstr "Bu Ay"

#: templates/jobs.html:95
msgid "Last Month"
msgstr "Geçen Ay"

#: templates/jobs.html:106 templates/people.html:76
msgid "Clear all filters"
msgstr "Tüm filtreleri temizle"

#: templates/jobs.html:134
msgid "Interviews"
msgstr "Görüşmeler"

#: templates/jobs.html:145
msgid "Days Open"
msgstr "Açık Gün Sayısı"

#: templates/jobs.html:151
msgid "Closed on:"
msgstr "Kapatıldı:"

#: templates/jobs.html:151
msgid "Posted on:"
msgstr "Yayın Tarihi:"

#: templates/jobs.html:152
msgid "View Details"
msgstr "Detayları Görüntüle"

#: templates/jobs.html:162 templates/people.html:157
msgid "Showing"
msgstr "Gösteriliyor"

#: templates/jobs.html:162 templates/people.html:157
msgid "of"
msgstr "/"

#: templates/jobs.html:162
msgid "jobs"
msgstr "ilan"

#: templates/manage_permissions.html:11
msgid "Team & Invitations"
msgstr "Üyeler ve Davetler"

#: templates/manage_permissions.html:12
msgid "Manage your recruitment team and invite new members"
msgstr "İşe alım ekibinizi yönetin ve yeni üyeler davet edin"

#: templates/manage_permissions.html:17 templates/manage_permissions.html:39
#: templates/manage_permissions.html:73 templates/settings.html:62
msgid "Invitations"
msgstr "Davetler"

#: templates/manage_permissions.html:29 templates/manage_permissions.html:69
msgid "Team Members"
msgstr "Takım Üyeleri"

#: templates/manage_permissions.html:59
msgid "Administrators"
msgstr "Adminler"

#: templates/manage_permissions.html:84
msgid "Search team members..."
msgstr "Takım üyelerini ara..."

#: templates/manage_permissions.html:88 templates/manage_permissions.html:218
msgid "Role:"
msgstr "Rol:"

#: templates/manage_permissions.html:90 templates/manage_permissions.html:220
msgid "All Roles"
msgstr "Tüm Roller"

#: templates/manage_permissions.html:105 templates/manage_permissions.html:244
#: templates/people.html:86 templates/profile.html:49
#: templates/published_job_details.html:198
msgid "Name"
msgstr "İsim"

#: templates/manage_permissions.html:109 templates/manage_permissions.html:248
#: templates/profile.html:57 templates/profile.html:101
#: templates/register.html:24 templates/signin.html:15
msgid "Email"
msgstr "E-posta"

#: templates/manage_permissions.html:113 templates/manage_permissions.html:252
#: templates/manage_permissions.html:381
msgid "Role"
msgstr "Rol"

#: templates/manage_permissions.html:120 templates/manage_permissions.html:267
#: templates/profile.html:80 templates/published_job_details.html:207
msgid "Actions"
msgstr "İşlemler"

#: templates/manage_permissions.html:143
msgid "Deactivate"
msgstr "Devre Dışı Bırak"

#: templates/manage_permissions.html:147
msgid "Activate"
msgstr "Etkinleştir"

#: templates/manage_permissions.html:206
msgid "Search invitations..."
msgstr "Davetleri ara..."

#: templates/manage_permissions.html:210
msgid "Status:"
msgstr "Durum:"

#: templates/manage_permissions.html:231
msgid "Invite New Member"
msgstr "Yeni Üye Davet Et"

#: templates/manage_permissions.html:256
msgid "Sent Date"
msgstr "Gönderilen Tarih"

#: templates/manage_permissions.html:260
msgid "Expiry Date"
msgstr "Son Kullanım Tarihi"

#: templates/manage_permissions.html:305
msgid "No invitations found"
msgstr "Davet bulunamadı."

#: templates/manage_permissions.html:353
msgid "Invite New Team Member"
msgstr "Yeni Takım Üyesi Davet Et"

#: templates/manage_permissions.html:360
msgid "Recipient Information"
msgstr "Alıcı Bilgileri"

#: templates/manage_permissions.html:364 templates/profile.html:93
msgid "First Name"
msgstr "Ad"

#: templates/manage_permissions.html:365
msgid "Enter first name"
msgstr "Adını girin"

#: templates/manage_permissions.html:369 templates/profile.html:97
msgid "Last Name"
msgstr "Soyad"

#: templates/manage_permissions.html:370
msgid "Enter last name"
msgstr "Soyadını girin"

#: templates/manage_permissions.html:375
msgid "Email Address"
msgstr "E-posta Adresi"

#: templates/manage_permissions.html:376
msgid "Enter email address"
msgstr "E-posta adresini girin"

#: templates/manage_permissions.html:383 templates/manage_permissions.html:443
msgid "Select a role"
msgstr "Rol seçin"

#: templates/manage_permissions.html:384 templates/manage_permissions.html:444
msgid "Administrator"
msgstr "Yönetici"

#: templates/manage_permissions.html:385 templates/manage_permissions.html:437
#: templates/manage_permissions.html:445
msgid "Recruiter"
msgstr "İşe Alım Uzmanı"

#: templates/manage_permissions.html:386 templates/manage_permissions.html:446
msgid "Hiring Manager"
msgstr "İşe Alım Yöneticisi"

#: templates/manage_permissions.html:387 templates/manage_permissions.html:447
msgid "Interviewer"
msgstr "Görüşmenin Sorumlusu"

#: templates/manage_permissions.html:388 templates/manage_permissions.html:448
msgid "Read Only"
msgstr "Sadece Okuma"

#: templates/manage_permissions.html:395
msgid "Permissions"
msgstr "Kullanıcı izinlerini ayarla"

#: templates/manage_permissions.html:396
msgid ""
"Permissions are determined by the selected role. You can customize them "
"after the user has accepted the invitation."
msgstr "İzinler, seçilen rol tarafından belirlenir. Kullanıcı davetiyeyi kabul ettikten sonra bunları özelleştirebilirsiniz."

#: templates/manage_permissions.html:400
msgid "Role Descriptions:"
msgstr "Rol Açıklamaları:"

#: templates/manage_permissions.html:402
msgid "Administrator: Full access to all system features and settings."
msgstr "Tüm sistem özelliklerine ve ayarlara tam erişim."

#: templates/manage_permissions.html:403
msgid "Recruiter: Manage job postings, candidates, and interviews."
msgstr "İş ilanlarını, adayları ve görüşmeleri yönetin."

#: templates/manage_permissions.html:404
msgid "Hiring Manager: Review candidates and make hiring decisions."
msgstr "Adayları gözden geçirin ve işe alım kararlarını verin."

#: templates/manage_permissions.html:405
msgid "Interviewer: Conduct interviews and provide feedback."
msgstr "Görüşmeleri yapın ve geri bildirim sağlayın."

#: templates/manage_permissions.html:406
msgid "Read Only: View-only access to recruitment data."
msgstr "Sadece okuma erişimi."

#: templates/manage_permissions.html:414
msgid "Send Invitation"
msgstr "Davet Gönder"

#: templates/manage_permissions.html:425 templates/manage_permissions.html:464
msgid "Change Role"
msgstr "Rol Değiştir"

#: templates/manage_permissions.html:431
msgid "Team Member"
msgstr "Takım Üyesi"

#: templates/manage_permissions.html:436
msgid "Current Role"
msgstr "Mevcut Rol"

#: templates/manage_permissions.html:441
msgid "New Role*"
msgstr "Yeni Rol*"

#: templates/manage_permissions.html:453
msgid "Reason for Change (Optional)"
msgstr "Değişimin Nedeni (Opsiyonel)"

#: templates/manage_permissions.html:454
msgid "Provide a reason for this role change"
msgstr "Bu rol değişimine bir neden girin"

#: templates/manage_permissions.html:459
msgid ""
"Changing roles will update the user's permissions. They will be notified of "
"this change."
msgstr "Rol değiştirme, kullanıcının izinlerini güncelleyecek. Bu değişiklik hakkında bildirimde bulunacaklar."

#: templates/navbar.html:23
msgid "Feed"
msgstr "Ana Sayfa"

#: templates/navbar.html:41
msgid "Create Job"
msgstr "İlan Oluştur"

#: templates/navbar.html:121
msgid "Guest User"
msgstr "Zıyaretçi"

#: templates/navbar.html:128
msgid "Not logged in"
msgstr "Giriş Yapılmadı"

#: templates/navbar.html:134
msgid "Profile"
msgstr "Profil"

#: templates/navbar.html:141
msgid "Logout"
msgstr "Çıkış Yap"

#: templates/people.html:10
msgid "Applicant Tracking"
msgstr "Aday Takibi"

#: templates/people.html:14 templates/people.html:967
msgid "Refresh Applicants"
msgstr "Adayları Yenile"

#: templates/people.html:18
msgid "Search applicants..."
msgstr "Aday ara..."

#: templates/people.html:33
msgid "All Positions"
msgstr "Tüm Pozisyonlar"

#: templates/people.html:63
msgid "All Dates"
msgstr "Tüm Tarihler"

#: templates/people.html:90
msgid "Experience (Years)"
msgstr "Deneyim (Yıl)"

#: templates/people.html:91 templates/published_job_details.html:204
msgid "Score"
msgstr "Puan"

#: templates/people.html:92
msgid "Applied On"
msgstr "Başvuru Tarihi"

#: templates/people.html:93
msgid "Action"
msgstr "İşlem"

#: templates/people.html:140
msgid "View Application"
msgstr "Başvuruyu Görüntüle"

#: templates/people.html:147
msgid "No applicants found matching the current filters."
msgstr "Mevcut filtrelere uygun aday bulunamadı."

#: templates/people.html:157
msgid "applicants"
msgstr "aday"

#: templates/people.html:210
msgid "Show"
msgstr "Göster"

#: templates/people.html:217
msgid "per page"
msgstr "sayfa başına"

#: templates/people.html:902
msgid "Processing..."
msgstr "İşleniyor..."

#: templates/people.html:948
msgid "Success!"
msgstr "Başarılı!"

#: templates/people.html:955
msgid "Error:"
msgstr "Hata:"

#: templates/people.html:960
msgid "An error occurred while processing applications. Please try again."
msgstr "Başvuruları işlerken bir hata oluştu. Lütfen tekrar deneyin."

#: templates/profile.html:10
msgid "Your Profile"
msgstr "Profiliniz"

#: templates/profile.html:29
msgid "Change Photo"
msgstr "Fotoğraf Değiştir"

#: templates/profile.html:32
msgid "Profile Activity"
msgstr "Profil Aktivitesi"

#: templates/profile.html:33
msgid "Last Login:"
msgstr "Son Giriş:"

#: templates/profile.html:36
msgid "Account Created:"
msgstr "Hesap Oluşturulma:"

#: templates/profile.html:46 templates/profile.html:91
msgid "Personal Information"
msgstr "Kişisel Bilgiler"

#: templates/profile.html:63 templates/profile.html:109
msgid "Company Information"
msgstr "Şirket Bilgileri"

#: templates/profile.html:66 templates/profile.html:111
msgid "Company"
msgstr "Şirket"

#: templates/profile.html:83 templates/profile.html:163
#: templates/profile.html:185
msgid "Change Password"
msgstr "Şifre Değiştir"

#: templates/profile.html:105
msgid "Phone"
msgstr "Telefon"

#: templates/profile.html:124
msgid "Save Changes"
msgstr "Değişiklikleri Kaydet"

#: templates/profile.html:139
msgid "Upload Profile Photo"
msgstr "Profil Fotoğrafı Yükle"

#: templates/profile.html:146
msgid "Choose a photo (PNG or JPEG only)"
msgstr "Bir fotoğraf seçin (sadece PNG veya JPEG)"

#: templates/profile.html:153
msgid "Upload"
msgstr "Yükle"

#: templates/profile.html:170
msgid "Current Password"
msgstr "Mevcut Şifre"

#: templates/profile.html:174
msgid "New Password"
msgstr "Yeni Şifre"

#: templates/profile.html:178
msgid "Confirm New Password"
msgstr "Yeni Şifreyi Onayla"

#: templates/published_job_details.html:58
msgid "Notification"
msgstr "Bildirim"

#: templates/published_job_details.html:82
msgid "Total Applicants:"
msgstr "Toplam Aday:"

#: templates/published_job_details.html:87
msgid "Published At:"
msgstr "Yayın Tarihi:"

#: templates/published_job_details.html:111
msgid "Bulk Communication"
msgstr "Toplu İletişim"

#: templates/published_job_details.html:123
msgid "Expert Support Options"
msgstr "Uzman Destek Seçenekleri"

#: templates/published_job_details.html:136
#: templates/published_job_details.html:401
msgid "Change Vacancy Status"
msgstr "İlan Durumunu Değiştir"

#: templates/published_job_details.html:150
msgid "Applicants Over Time"
msgstr "Zaman İçinde Aday Sayısı"

#: templates/published_job_details.html:162
msgid "Number of Applicants by Job Portal"
msgstr "Adayların Başvuru Portalı Dağılımı"

#: templates/published_job_details.html:170
msgid "Distribution of Applicants by Status"
msgstr "Adayların Durumuna Göre Dağılımı"

#: templates/published_job_details.html:183
msgid "Top Applicants"
msgstr "Toplam Aday Sayısı"

#: templates/published_job_details.html:189
msgid "View All Applicants"
msgstr "Tüm Adayları Görüntüle"

#: templates/published_job_details.html:254
msgid "Not Rated"
msgstr "Puanlanmadı"

#: templates/published_job_details.html:265
msgid "View"
msgstr "İncele"

#: templates/published_job_details.html:281
msgid "Request Support From Experts"
msgstr "Uzman Destek İste"

#: templates/published_job_details.html:286
msgid ""
"We can provide vetted candidates from our talent pool, or help you during "
"the technical interviews to pick best fit for your expectations."
msgstr ""
"Talebiniz için teşekkür ederiz. Teknik görüşme sürecinde en iyi eşleşmeyi "
"seçmenize yardımcı olabiliriz."

#: templates/published_job_details.html:291
msgid "Enter Details"
msgstr "Detayları Girin"

#: templates/published_job_details.html:302
msgid "Request Candidates"
msgstr "Aday Talep Et"

#: templates/published_job_details.html:303
msgid "Request Interview Help"
msgstr "Görüşme Desteği Talep Et"

#: templates/published_job_details.html:317
msgid "Send Bulk Mail to Applicants"
msgstr "Toplu E-posta Gönder"

#: templates/published_job_details.html:322
msgid ""
"Use this form to send bulk emails to applicants based on their application "
"statuses."
msgstr ""
"Adayların durumuna göre toplu e-postalar göndermek için bu formu kullanın."

#: templates/published_job_details.html:327
msgid "Select Application Status"
msgstr "Başvuru Durumu Seçin"

#: templates/published_job_details.html:334
msgid "Select a status"
msgstr "Durum Seçin"

#: templates/published_job_details.html:341
msgid "Email Subject"
msgstr "E-posta Konusu"

#: templates/published_job_details.html:360
msgid "Enter internal notes for your team (optional)"
msgstr "Dahili notlarınızı girin (isteğe bağlı)"

#: templates/published_job_details.html:364
msgid "(sent to candidates)"
msgstr "(adaylara gönderilir)"

#: templates/published_job_details.html:371
msgid "Enter your email message"
msgstr "E-posta mesajınızı girin"

#: templates/published_job_details.html:383
msgid "Send notification emails to candidates"
msgstr "Adaylara bildirim e-postaları gönderin"

#: templates/published_job_details.html:387
msgid "Send Emails"
msgstr "E-postaları Gönder"

#: templates/published_job_details.html:406
msgid "Current Status:"
msgstr "Mevcut Durum:"

#: templates/published_job_details.html:412
msgid "Changing the status will affect the visibility of the vacancy."
msgstr "İlan durumundaki değişiklikler ilanın görünürlüğünü etkileyecektir."

#: templates/published_job_details.html:415
msgid ""
"The vacancy will no longer exist on boards and be closed but be accesible "
"internally."
msgstr ""
"İlan panolardan kaldırılacak ve kapatılacak ancak dahili olarak erişilebilir "
"olacak."

#: templates/published_job_details.html:416
msgid "The vacancy will stop accepting new applications until changed."
msgstr "İlan yeniden açılana kadar aday kabul etmeyecek."

#: templates/published_job_details.html:417
msgid "The vacancy will be re-opened for new applications."
msgstr "Yeni adaylar için ilan yeniden açılacak."

#: templates/published_job_details.html:418
msgid "The vacancy will be permanently deleted. This action cannot be undone."
msgstr "İlan kalıcı olarak silinecek. Bu işlem geri alınamaz."

#: templates/published_job_details.html:424
msgid "Select New Status"
msgstr "Yeni Durum Seçin"

#: templates/published_job_details.html:447
msgid "Confirm Status"
msgstr "Durumu Onayla"

#: templates/register.html:11
msgid "Accept Invitation"
msgstr "Daveti Kabul Et"

#: templates/register.html:15
msgid "This invitation has expired or already been used."
msgstr "Bu davet süresi dolmuş veya zaten kullanılmış."

#: templates/register.html:18
msgid "Hello"
msgstr "Merhaba"

#: templates/register.html:18
msgid "you've been invited to join"
msgstr "katılmaya davet edildiniz"

#: templates/register.html:18
msgid "as a"
msgstr "olarak"

#: templates/register.html:29
msgid "Create Password"
msgstr "Şifre Oluştur"

#: templates/register.html:34
msgid "Confirm Password"
msgstr "Şifreyi Onayla"

#: templates/register.html:39
msgid "Complete Registration"
msgstr "Kaydı Tamamla"

#: templates/register.html:58
msgid "Passwords do not match"
msgstr "Şifreler eşleşmiyor"

#: templates/registration_complete.html:14
msgid "Registration Complete!"
msgstr "Kayıt Tamamlandı!"

#: templates/registration_complete.html:15
msgid ""
"Your account has been created successfully. You can now log in to access the "
"system."
msgstr ""
"Hesabınız başarıyla oluşturuldu. Artık sisteme erişmek için giriş "
"yapabilirsiniz."

#: templates/registration_complete.html:17
msgid "Go to Login"
msgstr "Giriş Sayfasına Git"

#: templates/settings.html:10
msgid "Configure your recruitment workflow and manage your ATS settings"
msgstr "İşe alım iş akışınızı yapılandırın ve ATS ayarlarınızı yönetin"

#: templates/settings.html:21
msgid ""
"Configure default options for job creation including work schedules, office "
"locations, and role titles."
msgstr ""
"Çalışma zamanları, ofis konumları ve rol başlıkları dahil olmak üzere iş "
"oluşturma için varsayılan seçenekleri yapılandırın."

#: templates/settings.html:23
msgid "Define company work schedules"
msgstr "Şirket çalışma zamanlarını tanımlayın"

#: templates/settings.html:24
msgid "Set up office locations"
msgstr "Ofis konumlarını ayarlayın"

#: templates/settings.html:25
msgid "Standardize role titles"
msgstr "Rol başlıklarını standartlaştırın"

#: templates/settings.html:26
msgid "Configure office schedule options"
msgstr "Ofis programı seçeneklerini yapılandırın"

#: templates/settings.html:29
msgid "Manage Preferences"
msgstr "Tercihleri Yönet"

#: templates/settings.html:42
msgid ""
"Create, edit, and manage job description templates to streamline your job "
"posting process."
msgstr ""
"İş ilan sürecinizi kolaylaştırmak için iş tanımı şablonları oluşturun, "
"düzenleyin ve yönetin."

#: templates/settings.html:44
msgid "Build reusable job templates"
msgstr "Yeniden kullanılabilir iş şablonları oluşturun"

#: templates/settings.html:45
msgid "Save time on repetitive descriptions"
msgstr "Tekrarlayan açıklamalarda zaman kazanın"

#: templates/settings.html:46
msgid "Maintain consistent job postings"
msgstr "Tutarlı iş ilanları sürdürün"

#: templates/settings.html:47
msgid "Organize templates by department"
msgstr "Şablonları departmana göre düzenleyin"

#: templates/settings.html:50
msgid "Manage Templates"
msgstr "Şablonları Yönet"

#: templates/settings.html:63
msgid ""
"Invite team members to collaborate on your recruitment process and manage "
"user access."
msgstr ""
"Ekip üyelerini işe alım sürecinizde işbirliği yapmaya davet edin ve "
"kullanıcı erişimini yönetin."

#: templates/settings.html:65
msgid "Add colleagues to your ATS"
msgstr "ATS'nize meslektaşlar ekleyin"

#: templates/settings.html:66
msgid "Set user permissions"
msgstr "Kullanıcı izinlerini ayarlayın"

#: templates/settings.html:67
msgid "Track invitation status"
msgstr "Davet durumunu takip edin"

#: templates/settings.html:68
msgid "Manage team collaboration"
msgstr "Ekip işbirliğini yönetin"

#: templates/settings.html:71
msgid "Manage Invitations"
msgstr "Davetleri Yönet"

#: templates/settings.html:84
msgid "Job Portals"
msgstr "İş Portalları"

#: templates/settings.html:85
msgid ""
"Configure connections to external job boards and manage API credentials for "
"job publishing."
msgstr ""
"Harici iş panolarına bağlantıları yapılandırın ve iş yayınlama için API "
"kimlik bilgilerini yönetin."

#: templates/settings.html:87
msgid "Connect to major job boards"
msgstr "Büyük iş panolarına bağlanın"

#: templates/settings.html:88
msgid "Manage API tokens securely"
msgstr "API tokenlarını güvenli bir şekilde yönetin"

#: templates/settings.html:89
msgid "Customize portal preferences"
msgstr "Portal tercihlerini özelleştirin"

#: templates/settings.html:90
msgid "Track portal integration status"
msgstr "Portal entegrasyon durumunu takip edin"

#: templates/settings.html:93
msgid "(Coming Soon!)"
msgstr "(Yakında!)"

#: templates/settings.html:103
msgid "Need Help?"
msgstr "Yardıma İhtiyacınız Var mı?"

#: templates/settings.html:104
msgid ""
"Our support team is ready to assist you with any questions about configuring "
"your ATS."
msgstr ""
"Destek ekibimiz ATS'nizi yapılandırma konusundaki tüm sorularınızda size "
"yardımcı olmaya hazır."

#: templates/settings.html:105
msgid "Contact Support"
msgstr "Destek ile İletişime Geçin"

#: templates/signin.html:16
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: templates/signin.html:20
msgid "Password"
msgstr "Şifre"

#: templates/signin.html:48
msgid "Signin"
msgstr "Giriş Yap"

#, fuzzy
#~| msgid "Loading job details..."
#~ msgid "Loading job details...</div>"
#~ msgstr "İş detayları yükleniyor..."

#~ msgid "Decision"
#~ msgstr "Karar"

#~ msgid "Candidates Hired"
#~ msgstr "İşe Alınan Adaylar"

#~ msgid "A new vacancy"
#~ msgstr "Yeni iş ilanı"

#~ msgid "moved to"
#~ msgstr "taşıdı"

#~ msgid "for"
#~ msgstr "için"

#~ msgid "Hot Jobs"
#~ msgstr "Popüler İlanlar"
