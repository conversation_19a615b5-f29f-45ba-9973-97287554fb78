{% extends 'main.html' %}
{% load static %}
{% load initials_avatar %}
{% load i18n %}
{% block content %}

<div class="container py-5">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center dashboard-header">
            <h3 class="page-heading">{% trans "Your Profile" %}</h3>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        {% if recruiter.profile_photo %}
                        <img src="{{ recruiter.profile_photo }}" alt="{{ user.first_name }}'s profile" id="profile-photo" class="d-flex justify-content-center align-items-center mx-auto rounded-circle img-fluid shadow" style="width: 150px; height: 150px; object-fit: cover;">
                        {% else %}
                        <div id="profile-initials" class="rounded-circle d-flex justify-content-center align-items-center mx-auto bg-primary text-white shadow" style="width: 150px; height: 150px; font-size: 48px;">
                            {{ user.first_name|first }}{{ user.last_name|first }}
                        </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#photo-upload-modal">
                            <i class="fas fa-camera"></i> {% trans "Change Photo" %}
                        </button>
                    </div>
                    <h5 class="mb-3">{% trans "Profile Activity" %}</h5>
                    <p class="mb-1"><strong>{% trans "Last Login:" %}</strong></p>
                    <p class="text-muted">{{ user.last_login|date:"F j, Y, g:i a" }}</p>
                    <hr>
                    <p class="mb-1"><strong>{% trans "Account Created:" %}</strong></p>
                    <p class="text-muted">{{ user.date_joined|date:"F j, Y" }}</p>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card h-100">
                <div class="card-body">
                    <div id="view-mode">
                        <h5 class="card-title mb-4">{% trans "Personal Information" %}</h5>
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <h6 class="mb-0">{% trans "Name" %}</h6>
                            </div>
                            <div class="col-sm-9 text-secondary">
                                {{ user.first_name }} {{ user.last_name }}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <h6 class="mb-0">{% trans "Email" %}</h6>
                            </div>
                            <div class="col-sm-9 text-secondary">
                                {{ user.email }}
                            </div>
                        </div>
                        <h5 class="card-title mb-4 mt-5">{% trans "Company Information" %}</h5>
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <h6 class="mb-0">{% trans "Company" %}</h6>
                            </div>
                            <div class="col-sm-9 text-secondary">
                                {{ recruiter.employer_id.employer_name|default:"Not provided" }}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <h6 class="mb-0">{% trans "Position" %}</h6>
                            </div>
                            <div class="col-sm-9 text-secondary">
                                {{ recruiter.role|default:"ATS User" }}
                            </div>
                        </div>
                        <h5 class="card-title mb-4 mt-5">{% trans "Actions" %}</h5>
                        <div class="row mt-5">
                            <div class="col-sm-12">
                                <button type="button" class="btn btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#change-password-modal"> <i class="fas fa-key"></i> {% trans "Change Password" %}</button>
                            </div>
                        </div>
                    </div>

                    <div id="edit-mode" class="d-none">
                        <form id="profile-edit-form" method="POST" action="#">
                            {% csrf_token %}
                            <h5 class="card-title mb-4">{% trans "Personal Information" %}</h5>
                            <div class="mb-3">
                                <label for="first_name" class="form-label">{% trans "First Name" %}</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" value="{{ recruiter.first_name }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="last_name" class="form-label">{% trans "Last Name" %}</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" value="{{ recruiter.last_name }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">{% trans "Email" %}</label>
                                <input type="email" class="form-control" id="email" name="email" value="{{ recruiter.email }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="phone" class="form-label">{% trans "Phone" %}</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="{{ recruiter.phone|default:'' }}">
                            </div>

                            <h5 class="card-title mb-4 mt-5">{% trans "Company Information" %}</h5>
                            <div class="mb-3">
                                <label for="company_name" class="form-label">{% trans "Company" %}</label>
                                <input type="text" class="form-control" id="company_name" name="company_name" value="{{ recruiter.company_name|default:'' }}">
                            </div>
                            <div class="mb-3">
                                <label for="position" class="form-label">{% trans "Position" %}</label>
                                <input type="text" class="form-control" id="position" name="position" value="{{ recruiter.position|default:'' }}">
                            </div>
                            <div class="mb-3">
                                <label for="department" class="form-label">{% trans "Department" %}</label>
                                <input type="text" class="form-control" id="department" name="department" value="{{ recruiter.department|default:'' }}">
                            </div>

                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary me-2">{% trans "Save Changes" %}</button>
                                <button type="button" id="cancel-edit-btn" class="btn btn-outline-secondary">{% trans "Cancel" %}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="photo-upload-modal" tabindex="-1" aria-labelledby="photoUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="photoUploadModalLabel">{% trans "Upload Profile Photo" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="photo-upload-form" enctype="multipart/form-data" method="POST" action="{% url 'change_photo' %}">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="photo-file" class="form-label">{% trans "Choose a photo (PNG or JPEG only)" %}</label>
                        <input type="file" class="form-control" id="photo-file" name="photo" accept=".png,.jpg,.jpeg" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="submit" form="photo-upload-form" class="btn btn-primary">{% trans "Upload" %}</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="change-password-modal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePasswordModalLabel">{% trans "Change Password" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="change-password-form" method="POST" action="{% url 'change_password' %}">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="current-password" class="form-label">{% trans "Current Password" %}</label>
                        <input type="password" class="form-control" id="current-password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new-password" class="form-label">{% trans "New Password" %}</label>
                        <input type="password" class="form-control" id="new-password" name="new_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirm-password" class="form-label">{% trans "Confirm New Password" %}</label>
                        <input type="password" class="form-control" id="confirm-password" name="confirm_password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="submit" form="change-password-form" class="btn btn-primary">{% trans "Change Password" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Add JavaScript for image preview -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Image preview functionality
        const photoInput = document.getElementById('photo-file');
        const profilePhoto = document.getElementById('profile-photo');
        const profileInitials = document.getElementById('profile-initials');

        if (photoInput) {
            photoInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        // If there's already a profile photo element
                        if (profilePhoto) {
                            profilePhoto.src = e.target.result;
                            if (profileInitials) {
                                profileInitials.style.display = 'none';
                            }
                        }
                        // If there's only initials showing
                        else if (profileInitials) {
                            // Create a new image element
                            const newImg = document.createElement('img');
                            newImg.src = e.target.result;
                            newImg.id = 'profile-photo';
                            newImg.alt = 'Profile Preview';
                            newImg.className = 'rounded-circle img-fluid';
                            newImg.style = 'width: 150px; height: 150px; object-fit: cover;';

                            // Replace initials with the new image
                            profileInitials.parentNode.replaceChild(newImg, profileInitials);
                        }
                    };

                    reader.readAsDataURL(this.files[0]);
                }
            });
        }
    });
</script>
{% endblock %}