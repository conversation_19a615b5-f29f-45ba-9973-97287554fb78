<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Careers at Ergunek Inc.</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --gray-0: #ffffff;
            --gray-1: #f8f9fa;
            --gray-2: #e9ecef;
            --gray-3: #dee2e6;
            --gray-4: #ced4da;
            --gray-5: #adb5bd;
            --gray-6: #6c757d;
            --gray-7: #495057;
            --gray-8: #343a40;
            --gray-9: #212529;
            --primary: var(--gray-8);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--gray-8);
            background-color: var(--gray-1);
        }

        header {
            background: linear-gradient(135deg, var(--gray-8), var(--gray-9));
            color: var(--gray-0);
            padding: 4rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 10% 20%, rgba(255,255,255,0.05) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255,255,255,0.05) 0%, transparent 20%);
            pointer-events: none;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
            letter-spacing: -0.5px;
            animation: fadeInDown 1s ease-out;
        }

        .tagline {
            font-size: 1.5rem;
            max-width: 800px;
            margin: 0 auto 2rem;
            font-weight: 300;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.2s forwards;
            opacity: 0;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 2rem;
            flex-wrap: wrap;
            animation: fadeIn 1s ease-out 0.4s forwards;
            opacity: 0;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.2rem;
            border-radius: 8px;
            min-width: 150px;
            backdrop-filter: blur(5px);
            transition: var(--transition);
        }

        .stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.3rem;
        }

        .stat-label {
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.8;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .section-title {
            text-align: center;
            margin: 3rem 0 2rem;
            font-size: 2.2rem;
            position: relative;
            display: inline-block;
            left: 50%;
            transform: translateX(-50%);
        }

        .section-title::after {
            content: "";
            display: block;
            width: 60px;
            height: 4px;
            background: var(--gray-8);
            margin: 0.8rem auto;
            border-radius: 2px;
        }

        .search-section {
            background: var(--gray-0);
            padding: 2.5rem;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            margin: -3rem auto 3rem;
            max-width: 1000px;
            position: relative;
            z-index: 10;
            animation: fadeInUp 0.8s ease-out 0.4s forwards;
            opacity: 0;
        }

        .search-title {
            text-align: center;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            color: var(--gray-7);
        }

        .search-controls {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .search-input {
            flex: 1;
            padding: 1rem 1.5rem;
            border: 2px solid var(--gray-3);
            border-radius: 8px;
            font-size: 1rem;
            transition: var(--transition);
            background: var(--gray-1);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--gray-6);
            box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.2);
        }

        .filter-section {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.6rem 1.2rem;
            background: var(--gray-2);
            border: none;
            border-radius: 50px;
            cursor: pointer;
            transition: var(--transition);
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filter-btn:hover {
            background: var(--gray-3);
        }

        .filter-btn.active {
            background: var(--gray-8);
            color: var(--gray-0);
        }

        .jobs-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .job-card {
            background: var(--gray-0);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: var(--transition);
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .job-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .job-header {
            padding: 1.8rem 1.8rem 1.2rem;
            border-bottom: 1px solid var(--gray-2);
        }

        .job-title {
            font-size: 1.4rem;
            margin-bottom: 0.8rem;
            color: var(--gray-9);
        }

        .job-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1.2rem;
            margin-bottom: 1rem;
        }

        .job-meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: var(--gray-6);
        }

        .job-remote {
            background: var(--gray-1);
            color: var(--gray-7);
            padding: 0.3rem 0.8rem;
            border-radius: 50px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .job-remote.true {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .job-body {
            padding: 1.2rem 1.8rem;
            flex-grow: 1;
        }

        .job-description {
            color: var(--gray-7);
            margin-bottom: 1.5rem;
            line-height: 1.7;
        }

        .job-footer {
            padding: 1rem 1.8rem;
            background: var(--gray-1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .job-salary {
            font-weight: 600;
            color: var(--gray-8);
            font-size: 1.1rem;
        }

        .apply-btn {
            padding: 0.7rem 1.5rem;
            background: var(--gray-8);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .apply-btn:hover {
            background: var(--gray-9);
            transform: translateY(-2px);
        }

        .no-jobs {
            grid-column: 1 / -1;
            text-align: center;
            padding: 4rem;
            font-size: 1.2rem;
            color: var(--gray-6);
        }

        .values-section {
            background: var(--gray-2);
            padding: 4rem 2rem;
            margin: 4rem 0;
        }

        .values-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .value-card {
            background: var(--gray-0);
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: var(--transition);
        }

        .value-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .value-icon {
            font-size: 2.5rem;
            margin-bottom: 1.5rem;
            color: var(--gray-7);
        }

        .value-title {
            font-size: 1.4rem;
            margin-bottom: 1rem;
            color: var(--gray-9);
        }

        .value-description {
            color: var(--gray-7);
            line-height: 1.7;
        }

        footer {
            background: var(--gray-9);
            color: var(--gray-3);
            padding: 3rem 2rem;
            text-align: center;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-logo {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--gray-0);
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin: 1.5rem 0;
            flex-wrap: wrap;
        }

        .footer-link {
            color: var(--gray-4);
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-link:hover {
            color: var(--gray-0);
        }

        .copyright {
            margin-top: 2rem;
            font-size: 0.9rem;
            color: var(--gray-5);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .delay-1 { animation-delay: 0.1s; }
        .delay-2 { animation-delay: 0.2s; }
        .delay-3 { animation-delay: 0.3s; }
        .delay-4 { animation-delay: 0.4s; }
        .delay-5 { animation-delay: 0.5s; }

        /* Responsive design */
        @media (max-width: 768px) {
            .search-controls {
                flex-direction: column;
            }
            
            .stats {
                gap: 1rem;
            }
            
            .stat-item {
                min-width: 120px;
                padding: 1rem;
            }
            
            .stat-number {
                font-size: 1.5rem;
            }
            
            h1 {
                font-size: 2.5rem;
            }
            
            .tagline {
                font-size: 1.2rem;
            }
        }

        @media (max-width: 480px) {
            .jobs-container {
                grid-template-columns: 1fr;
            }
            
            .search-section {
                padding: 1.5rem;
            }
            
            .value-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <h1>Careers at Ergunek Inc.</h1>
            <p class="tagline">Build what matters. Grow your career. Shape the future.</p>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">250+</div>
                    <div class="stat-label">Team Members</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">Countries</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">Employee Satisfaction</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">25+</div>
                    <div class="stat-label">Open Positions</div>
                </div>
            </div>
        </div>
    </header>

    <div class="search-section">
        <h2 class="search-title">Find Your Perfect Role</h2>
        <div class="search-controls">
            <input type="text" id="keywordSearch" class="search-input" placeholder="Search by job title, keywords, or skills">
            <input type="text" id="locationSearch" class="search-input" placeholder="Search by city, country, or remote">
        </div>
        <div class="filter-section">
            <button class="filter-btn active"><i class="fas fa-globe-americas"></i> All</button>
            <button class="filter-btn"><i class="fas fa-laptop"></i> Remote</button>
            <button class="filter-btn"><i class="fas fa-building"></i> Full-time</button>
            <button class="filter-btn"><i class="fas fa-clock"></i> Part-time</button>
            <button class="filter-btn"><i class="fas fa-graduation-cap"></i> Internship</button>
        </div>
    </div>

    <div class="container">
        <h2 class="section-title">Current Opportunities</h2>
        <div class="jobs-container" id="jobsContainer">
            <!-- Job cards will be dynamically inserted here -->
            <div class="no-jobs">
                <i class="fas fa-spinner fa-spin"></i> Loading opportunities...
            </div>
        </div>
    </div>

    <div class="values-section">
        <div class="container">
            <h2 class="section-title">Our Values</h2>
            <div class="values-container">
                <div class="value-card">
                    <div class="value-icon"><i class="fas fa-lightbulb"></i></div>
                    <h3 class="value-title">Innovation</h3>
                    <p class="value-description">We embrace new ideas and approaches to solve complex challenges and drive industry transformation.</p>
                </div>
                <div class="value-card">
                    <div class="value-icon"><i class="fas fa-users"></i></div>
                    <h3 class="value-title">Collaboration</h3>
                    <p class="value-description">We believe that diverse perspectives create better solutions through teamwork and open communication.</p>
                </div>
                <div class="value-card">
                    <div class="value-icon"><i class="fas fa-shield-alt"></i></div>
                    <h3 class="value-title">Integrity</h3>
                    <p class="value-description">We uphold the highest ethical standards in all our actions and business relationships.</p>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">ERGUNEK INC.</div>
            <p>Building the future through innovation and exceptional talent.</p>
            
            <div class="footer-links">
                <a href="#" class="footer-link">About Us</a>
                <a href="#" class="footer-link">Benefits</a>
                <a href="#" class="footer-link">Diversity & Inclusion</a>
                <a href="#" class="footer-link">Employee Stories</a>
                <a href="#" class="footer-link">Contact</a>
            </div>
            
            <p class="copyright">© 2025 Ergunek Inc. All rights reserved.</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const jobsContainer = document.getElementById('jobsContainer');
            const keywordSearch = document.getElementById('keywordSearch');
            const locationSearch = document.getElementById('locationSearch');
            let jobsData = [];
            
            // Sample job data from the XML feed provided
            const sampleJobs = [
                {
                    title: "Senior UI Designer",
                    date: "2025-06-08 15:33:51.460635+00:00",
                    referencenumber: "44",
                    url: "https://workloupe.com/apply/44?source=feed",
                    company: "Ergunek Inc.",
                    city: "Krakow",
                    country: "Poland",
                    remote: "true",
                    description: "Position: Senior UI Designer. We are seeking a highly skilled Senior UI Designer to join our dynamic team. The ideal candidate will have a strong background in creating intuitive and engaging user interfaces, with a keen eye for detail and a passion for design excellence.",
                    website: "https://workloupe.com/employers/1",
                    schedule: "fully remote",
                    email: "<EMAIL>",
                    salarymin: "6000.0",
                    salarymax: "10000.0",
                    salarycurrency: "EUR",
                    salary: "6000.0 - 10000.0 EUR"
                },
                {
                    title: "Senior SQL Developer",
                    date: "2025-06-06 22:03:36.316942+00:00",
                    referencenumber: "43",
                    url: "https://workloupe.com/apply/43?source=feed",
                    company: "Ergunek Inc.",
                    city: "Krakow",
                    country: "Poland",
                    remote: "true",
                    description: "Position: Senior SQL Developer. We are seeking a highly skilled Senior SQL Developer to join our dynamic team. The ideal candidate will have a strong background in database development and management, with expertise in SQL and related technologies.",
                    website: "https://workloupe.com/employers/1",
                    schedule: "fully remote",
                    email: "<EMAIL>",
                    salarymin: "10000.0",
                    salarymax: "12000.0",
                    salarycurrency: "PLN",
                    salary: "10000.0 - 12000.0 PLN"
                },
                {
                    title: "Data Engineer",
                    date: "2025-06-04 07:46:47.693404+00:00",
                    referencenumber: "42",
                    url: "https://workloupe.com/apply/42?source=feed",
                    company: "Ergunek Inc.",
                    city: "Krakow",
                    country: "Poland",
                    remote: "true",
                    description: "Position: Data Engineer. We are seeking a skilled Data Engineer to join our dynamic team. The ideal candidate will have a strong background in software development, data management, and cloud services.",
                    website: "https://workloupe.com/employers/1",
                    schedule: "fully remote",
                    email: "<EMAIL>",
                    salarymin: "1231.0",
                    salarymax: "124241.0",
                    salarycurrency: "USD",
                    salary: "1231.0 - 124241.0 USD"
                },
                {
                    title: "Junior Python Developer",
                    date: "2025-06-02 16:43:54.050781+00:00",
                    referencenumber: "41",
                    url: "https://workloupe.com/apply/41?source=feed",
                    company: "Ergunek Inc.",
                    city: "Warsaw",
                    country: "Poland",
                    remote: "true",
                    description: "Position: Junior Python Developer. We are seeking a motivated Junior Python Developer to join our dynamic team. This part-time, fully remote position is perfect for someone looking to grow their skills in Python development while working with cutting-edge technologies.",
                    website: "https://workloupe.com/employers/1",
                    schedule: "fully remote",
                    email: "<EMAIL>",
                    salarymin: "12000.0",
                    salarymax: "0.0",
                    salarycurrency: "USD",
                    salary: "12000.0 - 0.0 USD"
                },
                {
                    title: "Junior Data Scientist",
                    date: "2025-05-23 21:58:18.006691+00:00",
                    referencenumber: "37",
                    url: "https://workloupe.com/apply/37?source=feed",
                    company: "Ergunek Inc.",
                    city: "Warsaw",
                    country: "Poland",
                    remote: "false",
                    description: "Position: Junior Data Scientist. We are seeking a highly motivated Junior Data Scientist to join our dynamic team in Warsaw. The ideal candidate will have a strong foundation in data science and analytics.",
                    website: "https://workloupe.com/employers/1",
                    schedule: "hybrid",
                    email: "<EMAIL>",
                    salarymin: "7000.0",
                    salarymax: "11000.0",
                    salarycurrency: "PLN",
                    salary: "7000.0 - 11000.0 PLN"
                },
                {
                    title: "Web Development Intern",
                    date: "2025-05-22 17:54:22.377701+00:00",
                    referencenumber: "36",
                    url: "https://workloupe.com/apply/36?source=feed",
                    company: "Ergunek Inc.",
                    city: "Warsaw",
                    country: "Poland",
                    remote: "true",
                    description: "Position: Web Development Intern. We're looking for a motivated Web Development Intern with foundational skills in Python and React to join our team.",
                    website: "https://workloupe.com/employers/1",
                    schedule: "fully remote",
                    email: "<EMAIL>",
                    salarymin: "5000.0",
                    salarymax: "7000.0",
                    salarycurrency: "PLN",
                    salary: "5000.0 - 7000.0 PLN"
                }
            ];
            
            // Use sample data initially
            jobsData = sampleJobs;
            renderJobs(jobsData);
            
            // Function to render job cards
            function renderJobs(jobs) {
                if (jobs.length === 0) {
                    jobsContainer.innerHTML = `<div class="no-jobs">
                        <i class="fas fa-search"></i> No jobs match your search criteria. Try different keywords.
                    </div>`;
                    return;
                }
                
                jobsContainer.innerHTML = '';
                
                jobs.forEach((job, index) => {
                    const jobCard = document.createElement('div');
                    jobCard.className = `job-card delay-${index % 5}`;
                    
                    // Format salary information
                    let salaryText = job.salary;
                    if (job.salarymin && job.salarymax && job.salarycurrency) {
                        salaryText = `${job.salarymin} - ${job.salarymax} ${job.salarycurrency}`;
                    }
                    
                    // Format location information
                    const locationParts = [];
                    if (job.city) locationParts.push(job.city);
                    if (job.country) locationParts.push(job.country);
                    const locationText = locationParts.join(', ');
                    
                    jobCard.innerHTML = `
                        <div class="job-header">
                            <h3 class="job-title">${job.title}</h3>
                            <div class="job-meta">
                                <span class="job-meta-item">
                                    <i class="fas fa-map-marker-alt"></i> ${locationText || 'Location not specified'}
                                </span>
                                <span class="job-meta-item">
                                    <i class="fas fa-briefcase"></i> ${job.schedule}
                                </span>
                                <span class="job-remote ${job.remote.toLowerCase()}">
                                    <i class="fas ${job.remote.toLowerCase() === 'true' ? 'fa-wifi' : 'fa-building'}"></i>
                                    ${job.remote.toLowerCase() === 'true' ? 'Remote' : 'On-site'}
                                </span>
                            </div>
                        </div>
                        <div class="job-body">
                            <p class="job-description">${job.description}</p>
                        </div>
                        <div class="job-footer">
                            <div class="job-salary">${salaryText}</div>
                            <a href="${job.url}" class="apply-btn" target="_blank">
                                Apply Now <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    `;
                    
                    jobsContainer.appendChild(jobCard);
                });
            }
            
            // Filter jobs based on search criteria
            function filterJobs() {
                const keyword = keywordSearch.value.toLowerCase().trim();
                const location = locationSearch.value.toLowerCase().trim();
                
                const filteredJobs = jobsData.filter(job => {
                    const titleMatch = job.title.toLowerCase().includes(keyword);
                    const descMatch = job.description.toLowerCase().includes(keyword);
                    const cityMatch = job.city.toLowerCase().includes(location);
                    const countryMatch = job.country.toLowerCase().includes(location);
                    const remoteMatch = job.remote.toLowerCase().includes(location);
                    
                    const keywordMatch = keyword === '' || titleMatch || descMatch;
                    const locationMatch = location === '' || cityMatch || countryMatch || remoteMatch;
                    
                    return keywordMatch && locationMatch;
                });
                
                renderJobs(filteredJobs);
            }
            
            // Add event listeners for search
            keywordSearch.addEventListener('input', filterJobs);
            locationSearch.addEventListener('input', filterJobs);
            
            // Add animation to filter buttons
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');
                    
                    // Filter jobs based on button type
                    const filterType = button.textContent.trim().toLowerCase();
                    let filteredJobs = jobsData;
                    
                    if (filterType.includes('remote')) {
                        filteredJobs = jobsData.filter(job => job.remote.toLowerCase() === 'true');
                    } else if (filterType.includes('full-time')) {
                        filteredJobs = jobsData.filter(job => job.schedule.toLowerCase().includes('full'));
                    } else if (filterType.includes('part-time')) {
                        filteredJobs = jobsData.filter(job => job.schedule.toLowerCase().includes('part'));
                    } else if (filterType.includes('internship')) {
                        filteredJobs = jobsData.filter(job => job.title.toLowerCase().includes('intern'));
                    }
                    
                    renderJobs(filteredJobs);
                });
            });
        });
    </script>
</body>
</html>