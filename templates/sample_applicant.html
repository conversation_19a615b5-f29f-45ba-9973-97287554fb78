{% extends 'main.html'%} 
{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Applicant Header with Profile -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="p-6">
            <div class="flex flex-col md:flex-row items-start md:items-center">
                <!-- Profile Photo/Avatar -->
                {% if candidate_info.avatar_url %}
                    <img src="{{ candidate_info.avatar_url }}" alt="{{ candidate_info.full_name }}" class="w-20 h-20 rounded-full mr-6 object-cover">
                {% else %}
                    <div class="w-20 h-20 rounded-full mr-6 flex items-center justify-center text-white text-2xl font-semibold" style="background-color: {{ candidate_info.avatar_bg_color }}">
                        {{ candidate_info.candidate_firstname|first }}{{ candidate_info.candidate_lastname|first }}
                    </div>
                {% endif %}
                
                <div class="mt-4 md:mt-0">
                    <!-- Name and Position -->
                    <div class="flex flex-col md:flex-row md:items-center">
                        <h1 class="text-2xl font-bold text-gray-900">{{ candidate_info.full_name }}</h1>
                        <span class="md:ml-3 px-3 py-1 text-sm rounded-full bg-blue-100 text-blue-800 font-medium">
                            {{ application.application_state }}
                        </span>
                    </div>
                    
                    <!-- Applied position -->
                    <p class="text-lg text-gray-700 mt-1">Applied for: <span class="font-medium">{{ application.vacancy_id.vacancy_title }}</span></p>
                    
                    <!-- Contact details -->
                    <div class="mt-3 flex flex-col sm:flex-row sm:items-center">
                        <div class="flex items-center mr-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <a href="mailto:{{ candidate_info.candidate_email }}" class="text-gray-600 hover:text-blue-600">{{ candidate_info.candidate_email }}</a>
                        </div>
                        <div class="flex items-center mt-2 sm:mt-0">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            <a href="tel:{{ candidate_info.candidate_phone }}" class="text-gray-600 hover:text-blue-600">{{ candidate_info.candidate_phone }}</a>
                        </div>
                    </div>
                </div>
                
                <!-- Action buttons -->
                <div class="mt-4 md:mt-0 md:ml-auto flex flex-wrap gap-2">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        Schedule Interview
                    </button>
                    <div class="relative inline-block text-left">
                        <button type="button" class="bg-gray-100 text-gray-700 hover:bg-gray-200 py-2 px-4 rounded-lg flex items-center" id="options-menu" aria-expanded="true" aria-haspopup="true">
                            More Actions
                            <svg class="-mr-1 ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Background Summary -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Background Summary</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Column 1 -->
                <div>
                    <div class="mb-4">
                        <p class="text-sm text-gray-500">Applied Position</p>
                        <p class="text-base font-medium text-gray-800">{{ application.vacancy_id.vacancy_title }}</p>
                    </div>
                    
                    <div class="mb-4">
                        <p class="text-sm text-gray-500">Location</p>
                        <p class="text-base font-medium text-gray-800">{{ application.vacancy_id.vacancy_city }}, {{ application.vacancy_id.vacancy_country }}</p>
                    </div>
                    
                    <div class="mb-4">
                        <p class="text-sm text-gray-500">Application Date</p>
                        <p class="text-base font-medium text-gray-800">{{ application.application_date|date:"F j, Y" }}</p>
                    </div>
                    
                    <div>
                        <p class="text-sm text-gray-500">Application Portal</p>
                        <p class="text-base font-medium text-gray-800">{{ application.application_source }}</p>
                    </div>
                </div>
                
                <!-- Column 2 -->
                <div>
                    <div class="mb-4">
                        <p class="text-sm text-gray-500">Current Position</p>
                        <p class="text-base font-medium text-gray-800">{{ application.current_position }}</p>
                    </div>
                    
                    <div class="mb-4">
                        <p class="text-sm text-gray-500">Current Employer</p>
                        <p class="text-base font-medium text-gray-800">{{ application.current_employer }}</p>
                    </div>
                    
                    <div class="mb-4">
                      <p class="text-sm text-gray-500">Status</p>
                      <div class="flex items-center">
                          {% if application.application_state == 'New' %}
                              <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 font-medium">
                                  {{ application.application_state }}
                              </span>
                          {% elif application.application_state == 'Interview' %}
                              <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 font-medium">
                                  {{ application.application_state }}
                              </span>
                          {% else %}
                              <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800 font-medium">
                                  {{ application.application_state }}
                              </span>
                          {% endif %}
                      </div>
                  </div>
                    
                    <div>
                        <p class="text-sm text-gray-500">Application ID</p>
                        <p class="text-base font-medium text-gray-800">#{{ application.application_id }}</p>
                    </div>
                </div>
                
                <!-- Column 3 -->
                <div>
                    <div class="mb-4">
                        <p class="text-sm text-gray-500">Total Experience</p>
                        <p class="text-base font-medium text-gray-800">{{ application.total_exp_years }} years</p>
                    </div>
                    
                    <div class="mb-4">
                        <p class="text-sm text-gray-500">Education Level</p>
                        <p class="text-base font-medium text-gray-800">{{ application.education_level }}</p>
                    </div>
                    
                    <div class="mb-4">
                        <p class="text-sm text-gray-500">Notice Period</p>
                        <p class="text-base font-medium text-gray-800">{{ application.notice_period }}</p>
                    </div>
                    
                    <div>
                        <p class="text-sm text-gray-500">Latest Update</p>
                        <p class="text-base font-medium text-gray-800">
          <!--                  {% if journey_data %}
                                {{ journey_data.0.state_started_at|date:"F j, Y" }}
                            {% else %}
                                {{ application.application_date|date:"F j, Y" }}
                            {% endif %}
                    -->    </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs for different sections -->
    <div class="mb-6">
        <ul class="flex flex-wrap border-b border-gray-200">
            <li class="mr-2">
                <a href="#dashboard" class="tab-link inline-block py-3 px-4 text-sm font-medium text-blue-600 border-b-2 border-blue-600 active" data-tab="dashboard">Dashboard</a>
            </li>
            <li class="mr-2">
                <a href="#resume" class="tab-link inline-block py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300" data-tab="resume">Resume</a>
            </li>
    <!--        <li class="mr-2">
                <a href="#journey" class="tab-link inline-block py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300" data-tab="journey">Journey</a>
            </li> -->
            <li class="mr-2">
              <a href="#ai" class="tab-link inline-block py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300" data-tab="ai">AI</a>
            </li>
            <li class="mr-2">
                <a href="#comments" class="tab-link inline-block py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300" data-tab="comments">Comments</a>
            </li>
        </ul>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-pane active">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Overall Rank -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800">Overall Rank</h3>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-center items-center mb-6">
                            <div class="relative w-48 h-48">
                                <svg class="w-full h-full" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
                                    <!-- Background circle -->
                                    <circle cx="18" cy="18" r="16" fill="none" stroke="#e5e7eb" stroke-width="2"></circle>
                                    <!-- Progress circle (87% filled) -->
                                    <circle cx="18" cy="18" r="16" fill="none" stroke="#3b82f6" stroke-width="2" stroke-dasharray="100.53 100.53" stroke-dashoffset="13.07"
                                            transform="rotate(-90 18 18)"></circle>
                                    <!-- Text in the middle -->
                                    <text x="18" y="18" text-anchor="middle" dominant-baseline="middle" font-size="8" font-weight="bold" fill="#111827">87%</text>
                                    <text x="18" y="24" text-anchor="middle" dominant-baseline="middle" font-size="3" fill="#6b7280">Match Score</text>
                                </svg>
                            </div>
                        </div>
                        <div class="text-center">
                            <p class="text-gray-700 mb-2">This candidate ranks <span class="font-bold">3rd</span> among 25 applicants</p>
                            <p class="text-sm text-gray-500">Top 12% of all candidates</p>
                        </div>
                    </div>
                </div>
                
                <!-- Profile Match Analysis -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800">Profile Match Analysis</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <!-- Experience Match -->
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">Experience</span>
                                    <span class="text-sm font-medium text-gray-700">92%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 92%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Required: 5 years | Candidate: {{ application.total_exp_years }} years</p>
                            </div>
                            
                            <!-- Skills Match -->
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">Skills</span>
                                    <span class="text-sm font-medium text-gray-700">85%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 85%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">17 of 20 required skills matched</p>
                            </div>
                            
                            <!-- Education Match -->
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">Education</span>
                                    <span class="text-sm font-medium text-gray-700">100%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Required: Bachelor's | Candidate: {{ application.education_level }}</p>
                            </div>
                            
                            <!-- Location Match -->
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">Location</span>
                                    <span class="text-sm font-medium text-gray-700">70%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-yellow-500 h-2 rounded-full" style="width: 70%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Same country, different city</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Resume Tab -->
        <div id="resume" class="tab-pane hidden">
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">Resume</h3>
                </div>
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <p class="text-gray-600">Uploaded on {{ application.application_date|date:"F j, Y" }}</p>
                        <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            Download PDF
                        </button>
                    </div>
                    
                    <!-- Resume Preview (Placeholder) -->
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <div class="p-8 bg-gray-50 min-h-[600px] flex flex-col items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <p class="mt-4 text-gray-500">Resume preview will be displayed here</p>
                            <button class="mt-4 text-blue-600 hover:text-blue-800 font-medium">
                                Click to expand
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Tab -->
        <div id="ai" class="tab-pane hidden">
          <div class="bg-white rounded-xl shadow-md overflow-hidden">
              <div class="px-6 py-4 border-b border-gray-200">
                  <h3 class="text-lg font-semibold text-gray-800">AI Analysis</h3>
              </div>
              <div class="p-6">
                  <div class="mb-8">
                      <h4 class="text-md font-medium text-gray-800 mb-3">Candidate Match Score</h4>
                      
                      <!-- AI Score Gauge -->
                      <div class="flex items-center mb-6">
                          <div class="relative w-32 h-32">
                              <svg class="w-full h-full" viewBox="0 0 36 36">
                                  <!-- Background circle -->
                                  <circle cx="18" cy="18" r="16" fill="none" stroke="#e5e7eb" stroke-width="2"></circle>
                                  
                                  <!-- Progress circle (dynamically set based on score) -->
                                  {% with circle_circumference=100.53 %}
                                      {% with dash_offset=circle_circumference|floatformat:2 %}
                                          <circle cx="18" cy="18" r="16" fill="none"
                                              {% if ai_data.score >= 80 %}
                                                  stroke="#10b981" 
                                              {% elif ai_data.score >= 60 %}
                                                  stroke="#3b82f6"
                                              {% elif ai_data.score >= 40 %}
                                                  stroke="#f59e0b"
                                              {% else %}
                                                  stroke="#ef4444"
                                              {% endif %}
                                              stroke-width="2" 
                                              stroke-dasharray="{{ circle_circumference }} {{ circle_circumference }}" 
                                              stroke-dashoffset="{{ dash_offset|sub:ai_data.score|mul:0.01|mul:circle_circumference }}"
                                              transform="rotate(-90 18 18)">
                                          </circle>
                                      {% endwith %}
                                  {% endwith %}
                                  
                                  <!-- Score text in the middle -->
                                  <text x="18" y="18" text-anchor="middle" dominant-baseline="middle" font-size="8" font-weight="bold" fill="#111827">{{ ai_data.score }}%</text>
                                  <text x="18" y="24" text-anchor="middle" dominant-baseline="middle" font-size="3" fill="#6b7280">AI Match</text>
                              </svg>
                          </div>
                          
                          <!-- Score interpretation -->
                          <div class="ml-6">
                              <h5 class="font-semibold text-gray-800 mb-1">Match Interpretation</h5>
                              {% if ai_data.score >= 80 %}
                                  <p class="text-sm text-gray-600">This candidate is an <span class="font-medium text-green-600">excellent match</span> for the position based on AI analysis.</p>
                              {% elif ai_data.score >= 60 %}
                                  <p class="text-sm text-gray-600">This candidate is a <span class="font-medium text-blue-600">good match</span> for the position based on AI analysis.</p>
                              {% elif ai_data.score >= 40 %}
                                  <p class="text-sm text-gray-600">This candidate is a <span class="font-medium text-yellow-600">fair match</span> for the position based on AI analysis.</p>
                              {% else %}
                                  <p class="text-sm text-gray-600">This candidate is a <span class="font-medium text-red-600">weak match</span> for the position based on AI analysis.</p>
                              {% endif %}
                          </div>
                      </div>
                  </div>
                  
                  <!-- AI Highlights Section -->
                  <div>
                      <h4 class="text-md font-medium text-gray-800 mb-3">Key Highlights</h4>
                      
                      <div class="space-y-3">
                          {% for highlight in ai_data.highlights %}
                          <div class="flex items-start">
                              <div class="flex-shrink-0 h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                  </svg>
                              </div>
                              <p class="text-sm text-gray-700">{{ highlight }}</p>
                          </div>
                          {% empty %}
                          <div class="text-sm text-gray-500 italic">
                              No highlights available for this candidate.
                          </div>
                          {% endfor %}
                      </div>
                      
                      <!-- AI Analysis Additional Information -->
                      <div class="mt-6 bg-blue-50 rounded-lg p-4">
                          <div class="flex">
                              <div class="flex-shrink-0">
                                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                  </svg>
                              </div>
                              <div class="ml-3">
                                  <h5 class="text-sm font-medium text-blue-800">About AI Analysis</h5>
                                  <p class="mt-1 text-xs text-blue-700">This analysis is based on the candidate's resume, experience, and skills matched against the job requirements. The AI score is a measure of the overall match quality, while highlights represent key strengths identified.</p>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
        </div>

        <!-- Comments Tab -->
        <div id="comments" class="tab-pane hidden">
          <div class="bg-white rounded-xl shadow-md overflow-hidden">
              <div class="px-6 py-4 border-b border-gray-200">
                  <h3 class="text-lg font-semibold text-gray-800">Comments</h3>
              </div>
              <div class="p-6">
                  <!-- Comment form -->
                  <form action="{% url 'insert_comment' %}" method="POST" class="mb-6">
                      {% csrf_token %}
                      <input type="hidden" name="application_id" value="{{ application.application_id }}">
                      <div class="mb-3">
                          <label for="comment_body" class="block text-sm font-medium text-gray-700 mb-1">Add a comment</label>
                          <textarea id="comment_body" name="comment_body" rows="3" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Add your comment here..."></textarea>
                      </div>
                      <div class="flex justify-end">
                          <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                              Post Comment
                          </button>
                      </div>
                  </form>
                  
                  <!-- Comment list -->
                  <div class="space-y-6">
                      {% for comment in comments_data %}
                      <div class="bg-gray-50 rounded-lg p-4">
                          <div class="flex justify-between items-start">
                              <div class="flex items-start">
                                  <!-- User avatar -->
                                  <div class="w-10 h-10 rounded-full bg-blue-100 mr-3 flex items-center justify-center text-blue-700 font-semibold">
                                      {{ comment.commented_by.first_name|first }}{{ comment.commented_by.last_name|first }}
                                  </div>
                                  <div>
                                      <h4 class="font-medium text-gray-900">{{ comment.commented_by.first_name }} {{ comment.commented_by.last_name }}</h4>
                                      <p class="text-xs text-gray-500">{{ comment.comment_date|date:"F j, Y, g:i a" }}</p>
                                  </div>
                              </div>
                              <!-- Comment actions -->
                              <div class="relative">
                                  <button class="text-gray-400 hover:text-gray-600">
                                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                          <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z" />
                                      </svg>
                                  </button>
                              </div>
                          </div>
                          <div class="mt-3 text-gray-700">
                              {{ comment.comment_body }}
                          </div>
                      </div>
                      {% empty %}
                      <div class="text-center py-6">
                          <p class="text-gray-500">No comments yet. Be the first to comment!</p>
                      </div>
                      {% endfor %}
                  </div>
              </div>
          </div>
      </div>
  </div>
</div>

<!-- More Actions Dropdown Menu -->
<div id="more-actions-menu" class="hidden absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50" style="top: 200px; right: 20px;">
  <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
      <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
          <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Send Email
          </div>
      </a>
      <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
          <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Request More Info
          </div>
      </a>
      <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
          <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              Add to Shortlist
          </div>
      </a>
      <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
          <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Reject Application
          </div>
      </a>
  </div>
</div>

<!-- Status Change Modal -->
<div id="status-change-modal" class="hidden fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
  <div class="bg-white rounded-lg max-w-md w-full p-6">
      <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-900">Change Application Status</h3>
          <button type="button" id="close-modal" class="text-gray-400 hover:text-gray-500">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
          </button>
      </div>
      <form>
          <div class="mb-4">
              <label for="new-status" class="block text-sm font-medium text-gray-700 mb-1">New Status</label>
              <select id="new-status" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                  <option value="New">New</option>
                  <option value="Screening">Screening</option>
                  <option value="Interview">Interview</option>
                  <option value="Technical">Technical Assessment</option>
                  <option value="Final">Final Interview</option>
                  <option value="Offer">Offer</option>
                  <option value="Hired">Hired</option>
                  <option value="Rejected">Rejected</option>
              </select>
          </div>
          <div class="mb-4">
              <label for="status-note" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
              <textarea id="status-note" rows="3" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Add notes about this status change..."></textarea>
          </div>
          <div class="flex justify-end">
              <button type="button" id="cancel-status-change" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 mr-3">
                  Cancel
              </button>
              <button type="button" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                  Save Change
              </button>
          </div>
      </form>
  </div>
</div>

<!-- JavaScript for tab functionality and interactive elements -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
      // Tab functionality
      const tabLinks = document.querySelectorAll('.tab-link');
      const tabPanes = document.querySelectorAll('.tab-pane');
      
      tabLinks.forEach(link => {
          link.addEventListener('click', function(e) {
              e.preventDefault();
              
              // Remove active class from all tabs
              tabLinks.forEach(link => {
                  link.classList.remove('text-blue-600', 'border-blue-600');
                  link.classList.add('text-gray-500', 'border-transparent');
              });
              
              // Add active class to clicked tab
              this.classList.remove('text-gray-500', 'border-transparent');
              this.classList.add('text-blue-600', 'border-blue-600');
              
              // Hide all tab panes
              tabPanes.forEach(pane => {
                  pane.classList.add('hidden');
                  pane.classList.remove('active');
              });
              
              // Show the corresponding tab pane
              const tabId = this.getAttribute('data-tab');
              const tabPane = document.getElementById(tabId);
              tabPane.classList.remove('hidden');
              tabPane.classList.add('active');
          });
      });
      
      // More Actions Dropdown
      const moreActionsButton = document.getElementById('options-menu');
      const moreActionsMenu = document.getElementById('more-actions-menu');
      
      if (moreActionsButton && moreActionsMenu) {
          moreActionsButton.addEventListener('click', function(e) {
              e.preventDefault();
              moreActionsMenu.classList.toggle('hidden');
          });
          
          // Close dropdown when clicking outside
          document.addEventListener('click', function(e) {
              if (!moreActionsButton.contains(e.target) && !moreActionsMenu.contains(e.target)) {
                  moreActionsMenu.classList.add('hidden');
              }
          });
      }
      
      // Status change modal functionality
      const statusElements = document.querySelectorAll('.application_state');
      const statusModal = document.getElementById('status-change-modal');
      const closeModalButton = document.getElementById('close-modal');
      const cancelButton = document.getElementById('cancel-status-change');
      
      if (statusElements.length && statusModal) {
          statusElements.forEach(element => {
              element.addEventListener('click', function() {
                  statusModal.classList.remove('hidden');
              });
          });
          
          if (closeModalButton) {
              closeModalButton.addEventListener('click', function() {
                  statusModal.classList.add('hidden');
              });
          }
          
          if (cancelButton) {
              cancelButton.addEventListener('click', function() {
                  statusModal.classList.add('hidden');
              });
          }
          
          // Close modal when clicking outside
          statusModal.addEventListener('click', function(e) {
              if (e.target === statusModal) {
                  statusModal.classList.add('hidden');
              }
          });
      }
  });
</script>
{% endblock content %}