{% extends 'main.html' %}
{% load static %}
{% load i18n %}
{% block title %}Custom Careers Page Builder{% endblock %}
{% block content %}

<div class="container-fluid p-0">
    <div class="row g-0 min-vh-100">
        <!-- Left Panel - Controls -->
        <div class="col-lg-4 bg-light border-end">
            <div class="p-4 h-100 overflow-auto">
                <div class="mb-4">
                    <h4 class="fw-bold text-dark mb-2">
                        <i class="fas fa-cogs me-2"></i> Custom Careers Page Builder
                    </h4>
                </div>

                <!-- Output Type Selection -->
                <div class="mb-4">
                    <label for="outputType" class="form-label fw-semibold text-dark">
                        <i class="fas fa-download me-4"></i> Output Type
                    </label>
                    <select class="form-select bg-white border-dark text-dark" id="outputType" onchange="updateOutputType()">
                        <option value="full">Full HTML Page</option>
                        <option value="widget">Widget Code</option>
                        <option value="wordpress">WordPress Package</option>
                    </select>
                    <br>
                    <div class="form-text text-dark" id="outputTypeHelp">
                        Choose how you want to use your careers page. <br> <br> <br> For most users, the Full HTML Page option is recommended.
                        <br> <br>
                        The Widget Code option can be helpful for embedding the careers page in an existing website. However, it may require additional customization.
                        <br> <br>
                        The WordPress Package option produces a Full HTML Page that can be uploaded to a folder named careers on your WordPress site via any file manager tool you are using.
                        <br>
                    </div>
                </div>

                <!-- Company Information Panel -->
                <div class="card mb-4" style="border: 1px solid lightgrey; color: black !important;">
                    <div class="card-header bg-white">
                        <h6 class="mb-0 fw-semibold">
                            <i class="fas fa-building text-dark me-2"></i> Company Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="companyName" class="form-label small fw-semibold">Company Name</label>
                            <input type="text" class="form-control form-control-sm" id="companyName"
                                   placeholder="Enter company name" value="{{ current_employer.employer_name|default:'Your Company' }}">
                        </div>

                        <div class="mb-3">
                            <label for="companyTagline" class="form-label small fw-semibold">Tagline</label>
                            <input type="text" class="form-control form-control-sm" id="companyTagline"
                                   placeholder="Enter company tagline" value="Build what matters. Grow your career.">
                        </div>

                        <div class="mb-3">
                            <label for="logoUrl" class="form-label small fw-semibold">Logo URL (square)</label>
                            <input type="url" class="form-control form-control-sm" id="logoUrl"
                                   placeholder="https://example.com/logo.png"
                                   value="https://placehold.co/200">
                        </div>

                        <div class="mb-3">
                            <label for="bannerUrl" class="form-label small fw-semibold">Banner URL (landscape format)</label>
                            <input type="url" class="form-control form-control-sm" id="bannerUrl"
                                   placeholder="https://example.com/banner.jpg"
                                   value="https://placehold.co/1000x300">
                        </div>

                        <div class="mb-3">
                            <label for="rssFeedUrl" class="form-label small fw-semibold">RSS Feed URL</label>
                            <input type="url" class="form-control form-control-sm" id="rssFeedUrl"
                                   placeholder="https://example.com/feed.xml" value="https://workloupe.com/{{ current_employer.employer_name }}/jobs.rss">
                        </div>

                        <div class="mb-3">
                            <label for="secondTitle" class="form-label small fw-semibold">Second Title (optional)</label>
                            <input type="text" class="form-control form-control-sm" id="secondTitle"
                                   placeholder="Enter second title" value="Open Positions">
                        </div>

                        <div class="mb-3">
                            <label for="secondtagline" class="form-label small fw-semibold">Second Title Description (optional)</label>
                            <input type="text" class="form-control form-control-sm" id="secondtagline"
                                   placeholder="Enter second title description" value="Join our team and help us build the future">
                        </div>

                        <div class="mb-3" style="display: none">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enableSearch" checked disabled>
                                <label class="form-check-label small fw-semibold" for="enableSearch">
                                    Enable job search functionality
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Design Customization Panel -->
                <div class="card mb-4" style="border: 1px solid lightgrey; color: black !important;">
                    <div class="card-header bg-white">
                        <h6 class="mb-0 fw-semibold">
                            <i class="fas fa-palette text-dark me-2"></i> Design Customization
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <label for="primaryColor" class="form-label small fw-semibold">Primary Color (Default)</label>
                                <input type="color" class="form-control form-control-color form-control-sm"
                                       id="primaryColor" value="#777777">
                            </div>
                            <div class="col-6">
                                <label for="secondaryColor" class="form-label small fw-semibold">Secondary Color (Effect)</label>
                                <input type="color" class="form-control form-control-color form-control-sm"
                                       id="secondaryColor" value="#027777">
                            </div>
                        </div>
                    </div>
                    <!-- critical Info about the dark/light mode and how it should effect the color decisions -->
                    <div class="form-text text-dark m-3" id="outputTypeHelp">
                        The dark/light mode of the page will be determined by the user's browser settings. The colors you choose should work well in both dark and light mode.
                        <br>
                        If you are not sure what colors to choose, you try different colors while changing your device's dark/light mode.
                        <br>
                    </div>

                </div>

                <!-- Widget-specific Options -->
                <div class="card mb-4" id="widgetOptions" style="display: none; border: 1px solid lightgrey; color: black !important;">
                    <div class="card-header bg-white">
                        <h6 class="mb-0 fw-semibold">
                            <i class="fas fa-puzzle-piece text-dark me-2"></i> Widget Options
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="maxJobs" class="form-label small fw-semibold">Maximum Jobs to Display</label>
                            <select class="form-select form-select-sm" id="maxJobs">
                                <option value="3">3 jobs</option>
                                <option value="5" selected>5 jobs</option>
                                <option value="10">10 jobs</option>
                                <option value="15">15 jobs</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showSalary" checked>
                                <label class="form-check-label small fw-semibold" for="showSalary">
                                    Show salary information
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showLocation" checked>
                                <label class="form-check-label small fw-semibold" for="showLocation">
                                    Show location information
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showDate" checked>
                                <label class="form-check-label small fw-semibold" for="showDate">
                                    Show posting date
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Button -->
                <div class="card">
                    <div class="card-body text-center">
                        <button class="btn btn-primary w-100" id="actionBtn" onclick="handleAction()">
                            <i class="fas fa-download me-2"></i><span id="actionBtnText"> Download HTML Page</span>
                        </button>
                        <small class="text-muted d-block mt-2" id="actionBtnHelp">Downloads complete HTML with RSS integration</small>

                        <!-- Widget Code Display Area -->
                        <div id="widgetCodeArea" style="display: none;" class="mt-3">
                            <label class="form-label small fw-semibold">Widget Code (Copy & Paste):</label>
                            <textarea class="form-control" id="widgetCodeTextarea" rows="8" readonly></textarea>
                            <button class="btn btn-outline-primary btn-sm mt-2 w-100" onclick="copyWidgetCode()">
                                <i class="fas fa-copy me-2"></i> Copy Code
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel - Preview -->
        <div class="col-lg-8">
            <div class="p-3 border-bottom bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 fw-semibold">
                        <i class="fas fa-eye text-primary me-2"></i> Live Preview
                    </h6>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-primary" id="desktopBtn" style="border-color: #020202 !important;">
                            <i class="fas fa-desktop"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="tabletBtn" style="border-color: #020202 !important;">
                            <i class="fas fa-tablet-alt"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="mobileBtn" style="border-color: #020202 !important;">
                            <i class="fas fa-mobile-alt"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="preview-container h-100 overflow-auto bg-light p-3">
                <div class="preview-frame bg-white rounded shadow-sm h-100 container" id="previewFrame">
                    <!-- Preview content will be generated here -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
:root {
    --primary-color: #007bff;
    --secondary-color: #4500a2;
    --body-bg: rgb(238, 238, 238);
    --text-color: #111111;
    --text-secondary: #474747;
    --body-bg-light: rgb(249, 249, 249);
}

@media (prefers-color-scheme: dark) {
    :root {
    --primary-color: #007bff;
    --secondary-color: #4500a2;
    --body-bg: #121212;
    --text-color: #ffffff;
    --text-secondary: #a0a0a0;
    --body-bg-light: #1e1e1e;
    }
}

.min-vh-100 {
    min-height: 100vh;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: var(--body-bg);
}

.header {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)),
    url("https://picsum.photos/1200/400");
    background-size: cover;
    background-position: center;
    color: white;
    padding: 80px 20px;
    text-align: center;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}
.header p {
    font-size: 1.25rem;
    max-width: 700px;
    margin: 0 auto;
    opacity: 0.9;
}
.header img {
    max-height: 80px;
    margin-bottom: 20px;
}

.jobs-section {
    padding: 60px 20px;
    background: var(--body-bg) !important;
}
.jobs-container {
    max-width: 1200px;
    margin: 0 auto;
}
.jobs-header {
    text-align: center;
    margin-bottom: 40px;
}
.jobs-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 15px;
}
.jobs-header p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto 30px;
}

.search-container {
    max-width: 600px;
    margin: 0 auto 40px;
    position: relative;
}
.search-input {
    width: 100%;
    padding: 14px 20px;
    border: 2px solid var(--text-secondary) !important;
    border-radius: 50px;
    font-size: 1rem;
    background: var(--body-bg-light);
    color: var(--text-color);
}
.search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color);
    color: var(--body-bg-light);
    border: none;
    padding: 8px 20px;
    border-radius: 50px;
    cursor: pointer;
}

.job-card {
    background: var(--body-bg-light);
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 4px solid transparent !important;
    transition: 0.3s ease-in;
    margin-bottom: 10px;
    margin-top: 10px;
}
.job-card:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    border: 4px solid var(--secondary-color) !important;
    margin-top: -10px;
}
.job-card:hover .apply-btn {
    background: var(--secondary-color) !important;
}
.job-card:hover .job-title {
    color: var(--secondary-color) !important;
}
.job-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}
.job-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
}
.job-type {
    background: rgba(108, 117, 125, 0.1) !important;
    color: var(--text-color) !important;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
}
.job-location,
.job-salary,
.job-date {
    color: var(--text-secondary);
    margin: 0 0 10px 0;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
}
.job-location i,
.job-salary i,
.job-date i {
    margin-right: 8px;
    width: 20px;
}
.job-description {
    color: var(--text-secondary);
    margin: 0 0 20px 0;
    line-height: 1.6;
    font-size: 0.95rem;
}
.apply-btn {
    background: var(--primary-color);
    color: var(--body-bg-light);
    text-decoration: none;
    padding: 10px 24px;
    border-radius: 6px;
    font-weight: 600;
    display: inline-block;
    transition: 0.3s ease;
}
.apply-btn:hover {
    background: var(--secondary-color);
    color: var(--body-bg-light);
}

.job-body {
    max-height: 100px !important;
    overflow-y: hidden !important;
}

@media (max-width: 768px) {
    .header h1 {
    font-size: 2rem;
    }
    .jobs-header h2 {
    font-size: 1.75rem;
    }
    .job-header {
    flex-direction: column;
    align-items: flex-start;
    }
    .job-type {
    margin-top: 10px;
    }
}
</style>

<script>
// Global variables
let currentOutputType = 'full';

document.addEventListener('DOMContentLoaded', function() {
    // Extract RGB values from hex color
    function hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    // Update CSS variables with RGB values for transparency
    function updateCssVariables() {
        const primaryColor = document.getElementById('primaryColor').value || '#020202';
        const secondaryColor = document.getElementById('secondaryColor').value || '#025503';

        // Set CSS variables
        document.documentElement.style.setProperty('--primary-color', primaryColor);
        document.documentElement.style.setProperty('--secondary-color', secondaryColor);

        // Set RGB values for transparency
        const primaryRgb = hexToRgb(primaryColor);
        if (primaryRgb) {
            document.documentElement.style.setProperty('--primary-color-rgb', `${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}`);
        }

        const secondaryRgb = hexToRgb(secondaryColor);
        if (secondaryRgb) {
            document.documentElement.style.setProperty('--secondary-color-rgb', `${secondaryRgb.r}, ${secondaryRgb.g}, ${secondaryRgb.b}`);
        }
    }

    // Set up event listeners for all inputs
    const inputIds = [
        'companyName', 'companyTagline', 'logoUrl', 'bannerUrl',
        'rssFeedUrl', 'primaryColor', 'secondaryColor', 'secondTitle', 'secondtagline',
        'maxJobs', 'showSalary', 'showLocation', 'showDate'
    ];

    inputIds.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', function() {
                updateCssVariables();
                updatePreview();
            });
            element.addEventListener('change', function() {
                updateCssVariables();
                updatePreview();
            });
        }
    });

    // Special handling for checkbox
    const enableSearch = document.getElementById('enableSearch');
    if (enableSearch) {
        enableSearch.addEventListener('change', updatePreview);
    }

    // Set up other event listeners
    document.getElementById('desktopBtn').addEventListener('click', () => togglePreviewMode('desktop'));
    document.getElementById('tabletBtn').addEventListener('click', () => togglePreviewMode('tablet'));
    document.getElementById('mobileBtn').addEventListener('click', () => togglePreviewMode('mobile'));

    // Initialize preview
    updateCssVariables();
    updatePreview();
});

// Handle output type change
function updateOutputType() {
    const outputType = document.getElementById('outputType').value;
    currentOutputType = outputType;

    const widgetOptions = document.getElementById('widgetOptions');
    const widgetCodeArea = document.getElementById('widgetCodeArea');
    const actionBtnText = document.getElementById('actionBtnText');
    const actionBtnHelp = document.getElementById('actionBtnHelp');
    const actionBtn = document.getElementById('actionBtn');

    // Hide widget code area when switching types
    widgetCodeArea.style.display = 'none';

    switch(outputType) {
        case 'full':
            widgetOptions.style.display = 'none';
            actionBtnText.textContent = 'Download HTML Page';
            actionBtnHelp.textContent = 'Downloads complete HTML with RSS integration';
            actionBtn.innerHTML = '<i class="fas fa-download me-2"></i><span id="actionBtnText"> Download HTML Page</span>';
            break;
        case 'widget':
            widgetOptions.style.display = 'none';
            actionBtnText.textContent = 'Generate Widget Code';
            actionBtnHelp.textContent = 'Generates embeddable widget code';
            actionBtn.innerHTML = '<i class="fas fa-code me-2"></i><span id="actionBtnText"> Generate Widget Code</span>';
            break;
        case 'wordpress':
            widgetOptions.style.display = 'none';
            actionBtnText.textContent = 'Download WordPress Package';
            actionBtnHelp.textContent = 'Downloads HTML file in careers folder for WordPress';
            actionBtn.innerHTML = '<i class="fas fa-download me-2"></i><span id="actionBtnText"> Download WordPress Package</span>';
            break;
    }

    updatePreview();
}

// Update preview with current settings
function updatePreview() {
    // Get current form values
    const companyName = document.getElementById('companyName').value || 'Your Company';
    const tagline = document.getElementById('companyTagline').value || 'Build what matters. Grow your career.';
    const logoUrl = document.getElementById('logoUrl').value;
    const bannerUrl = document.getElementById('bannerUrl').value;
    const rssFeedUrl = document.getElementById('rssFeedUrl').value;
    const secondTitle = document.getElementById('secondTitle').value || 'Open Positions';
    const secondtagline = document.getElementById('secondtagline').value || 'Join our team and help us build the future';
    const primaryColor = document.getElementById('primaryColor').value || '#020202';
    const secondaryColor = document.getElementById('secondaryColor').value || '#025503';
    const enableSearch = document.getElementById('enableSearch').checked;

    // Get widget-specific options
    const maxJobs = parseInt(document.getElementById('maxJobs')?.value || 5);
    const showSalary = document.getElementById('showSalary')?.checked || true;
    const showLocation = document.getElementById('showLocation')?.checked || true;
    const showDate = document.getElementById('showDate')?.checked || true;

    // Generate job cards HTML based on maxJobs for widget preview
    const jobsToShow = currentOutputType === 'widget' ? maxJobs : 5;
    let jobCardsHTML = '';

    for (let i = 0; i < jobsToShow; i++) {
        const jobTitles = ['Senior UI Designer', 'Frontend Developer', 'Product Manager', 'Data Scientist', 'DevOps Engineer'];
        const locations = ['San Francisco, CA', 'New York, NY', 'Austin, TX', 'Seattle, WA', 'Remote'];
        const salaries = ['$120k - $150k', '$100k - $130k', '$140k - $170k', '$110k - $140k', '$90k - $120k'];

        const title = jobTitles[i % jobTitles.length];
        const location = locations[i % locations.length];
        const salary = salaries[i % salaries.length];

        const salaryHTML = showSalary ? `<p class="job-salary"><i class="fas fa-dollar-sign"></i> ${salary}</p>` : '';
        const locationHTML = showLocation ? `<p class="job-location"><i class="fas fa-map-marker-alt"></i> ${location}</p>` : '';
        const dateHTML = showDate ? `<p class="job-date"><i class="fas fa-calendar"></i> Posted on Placeholder</p>` : '';

        jobCardsHTML += `
            <div class="col-12 col-sm-12 col-lg-4 col-md-6 p-2">
                <div class="job-card h-100">
                    <div class="job-header">
                        <h3 class="job-title">${title}</h3>
                        <div class="job-type">Full-time</div>
                    </div>
                    ${locationHTML}
                    ${salaryHTML}
                    ${dateHTML}
                    <div class="job-footer">
                        <a href="#" class="btn apply-btn w-100 mt-2">Apply Now</a>
                    </div>
                </div>
            </div>
        `;
    }

    // Generate search bar HTML
    const searchBarHTML = enableSearch ? `
        <div class="search-container">
            <input
              type="text"
              class="search-input"
              placeholder="Search by Keywords or Locations..."
            />
        </div>
    ` : '';

    // Generate header with optional banner and logo
    const headerStyle = bannerUrl ?
        `background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url('${bannerUrl}');
         background-size: cover;
         box-shadow: 0px 0px 0px grey !important;
         background-position: center;` :
        `background: linear-gradient(135deg, ${primaryColor}, ${secondaryColor});`;

    const logoHTML = logoUrl ? `<img src="${logoUrl}" alt="${companyName}" class="mx-auto d-block">` : '';

    // Generate RSS feed indicator
    const rssIndicator = rssFeedUrl ?
        `` :
        ``;

    // if the RSS feed is not configured, show a message
    const rssMessage = !rssFeedUrl ?
        `` : '';

    // if the rss feed returns jobs then display them.
    const rssJobs = rssFeedUrl ?
        `<div class="container"><div class="row g-4">
            ${jobCardsHTML}
        </div></div>` : rssMessage;

    // Generate complete HTML preview
    const previewHTML = `
        <div class="header" style="${headerStyle}">
            ${logoHTML}
            <h1>${companyName}</h1>
            <p>${tagline}</p>
        </div>

        <div class="jobs-section">
            <div class="jobs-container">
                <div class="jobs-header">
                    <h2>${secondTitle}</h2>
                    <p>${secondtagline}</p>
                    ${searchBarHTML}
                    ${rssIndicator}
                </div>
                ${rssJobs}
            </div>
        </div>

        <div class="footer text-center p-3 bg-light">
            <p class="text-dark">Powered by Canvider</p>
        </div>
    `;

    // Update the preview
    const previewFrame = document.getElementById('previewFrame');
    if (previewFrame) {
        previewFrame.innerHTML = previewHTML;
    }
}

// Toggle preview mode (desktop/tablet/mobile)
function togglePreviewMode(mode) {
    const previewFrame = document.getElementById('previewFrame');
    if (!previewFrame) return;

    // Update button states
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-primary');
    });

    const activeBtn = document.getElementById(mode + 'Btn');
    if (activeBtn) {
        activeBtn.classList.remove('btn-outline-primary');
        activeBtn.classList.add('btn-primary');
    }

    // Update preview frame width based on mode
    switch(mode) {
        case 'mobile':
            previewFrame.style.maxWidth = '375px';
            break;
        case 'tablet':
            previewFrame.style.maxWidth = '768px';
            break;
        case 'desktop':
            previewFrame.style.maxWidth = '100%';
            break;
    }
}

// Handle main action button click
function handleAction() {
    const outputType = document.getElementById('outputType').value;
    const actionBtn = document.getElementById('actionBtn');

    // Disable button during processing
    actionBtn.disabled = true;
    const originalHTML = actionBtn.innerHTML;
    actionBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Processing...';

    const enableButton = () => {
        actionBtn.disabled = false;
        actionBtn.innerHTML = originalHTML;
    };

    switch(outputType) {
        case 'full':
            downloadFullPage().finally(enableButton);
            break;
        case 'widget':
            generateWidgetCode().finally(enableButton);
            break;
        case 'wordpress':
            downloadWordPressPackage().finally(enableButton);
            break;
        default:
            enableButton();
    }
}

// Generate and display widget code
function generateWidgetCode() {
    const config = getFormConfig();
    config.isWidget = true;

    // Call backend to generate widget HTML
    return fetch('/generate_careers_page_html/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const widgetCodeArea = document.getElementById('widgetCodeArea');
            const widgetCodeTextarea = document.getElementById('widgetCodeTextarea');

            widgetCodeTextarea.value = data.html;
            widgetCodeArea.style.display = 'block';
        } else {
            alert('Error generating widget code: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error generating widget code');
    });
}

// Download full HTML page
function downloadFullPage() {
    const config = getFormConfig();
    config.isWidget = false;
    config.isFullPage = true;

    // Call backend to generate full page HTML
    return fetch('/generate_careers_page_html/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            downloadFile(data.html, 'careers-page.html', 'text/html');
        } else {
            alert('Error generating HTML: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error generating HTML');
    });
}

// Download WordPress package
function downloadWordPressPackage() {
    const config = getFormConfig();
    config.isWidget = false;
    config.isFullPage = true;

    // Call backend to generate WordPress package
    return fetch('/generate_wordpress_package/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Download the HTML file with WordPress-friendly structure
            downloadFile(data.html, 'careers-index.html', 'text/html');
        } else {
            alert('Error generating WordPress package: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error generating WordPress package');
    });
}

// Copy widget code to clipboard
function copyWidgetCode() {
    const textarea = document.getElementById('widgetCodeTextarea');
    textarea.select();
    textarea.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');

        // Show feedback
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check me-2"></i> Copied!';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-primary');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-primary');
        }, 2000);
    } catch (err) {
        alert('Failed to copy code. Please select and copy manually.');
    }
}

// Helper function to get form configuration
function getFormConfig() {
    return {
        companyName: document.getElementById('companyName').value || 'Your Company',
        tagline: document.getElementById('companyTagline').value || 'Build what matters. Grow your career.',
        logoUrl: document.getElementById('logoUrl').value || '',
        bannerUrl: document.getElementById('bannerUrl').value || '',
        rssFeedUrl: document.getElementById('rssFeedUrl').value || '',
        secondTitle: document.getElementById('secondTitle').value || 'Open Positions',
        secondtagline: document.getElementById('secondtagline').value || 'Join our team and help us build the future',
        primaryColor: document.getElementById('primaryColor').value || '#020202',
        secondaryColor: document.getElementById('secondaryColor').value || '#025503',
        enableSearch: document.getElementById('enableSearch').checked,
        maxJobs: parseInt(document.getElementById('maxJobs')?.value || 5),
        showSalary: document.getElementById('showSalary')?.checked || true,
        showLocation: document.getElementById('showLocation')?.checked || true,
        showDate: document.getElementById('showDate')?.checked || true
    };
}

// Helper function to get CSRF token
function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
}

// Helper function to download file
function downloadFile(content, filename, contentType) {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}

// Helper function to create WordPress zip package
function createWordPressZip(htmlContent) {
    // For now, just download the HTML file with WordPress-friendly name
    // In the future, this could create a proper zip with folder structure
    downloadFile(htmlContent, 'careers/index.html', 'text/html');
}

</script>

{% endblock %}