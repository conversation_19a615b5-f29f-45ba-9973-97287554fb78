{% extends 'main.html' %}
{% block content %}
{% load static %}
{% load i18n %}

<div class="container mx-auto px-4 py-6">
    <div class="row d-flex justify-content-center align-items-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-body p-5">
                    <h2 class="text-center mb-4">{% trans "Accept Invitation" %}</h2>

                    {% if invitation.is_expired %}
                        <div class="alert alert-danger">
                            {% trans "This invitation has expired or already been used." %}
                        </div>
                    {% else %}
                        <p class="mb-4">{% trans "Hello" %} {{ invitation.first_name }}, {% trans "you've been invited to join" %} {{ employer_name }} {% trans "as a" %} {{ invitation.role }}.</p>

                        <form method="POST">
                            {% csrf_token %}

                            <div class="form-group mb-4">
                                <label for="email">{% trans "Email" %}</label>
                                <input type="email" class="form-control" id="email" value="{{ invitation.email }}" readonly>
                            </div>

                            <div class="form-group mb-4">
                                <label for="password">{% trans "Create Password" %}</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>

                            <div class="form-group mb-4">
                                <label for="confirm_password">{% trans "Confirm Password" %}</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">{% trans "Complete Registration" %}</button>
                            </div>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('confirm_password');

        form.addEventListener('submit', function(e) {
            if (password.value !== confirmPassword.value) {
                e.preventDefault();
                alert('{% trans "Passwords do not match" %}');
                confirmPassword.focus();
            }
        });
    });
</script>
{% endblock %}