{% extends 'main.html' %}

{% load static %}
{% block content %}
<!-- Include Material Icons -->
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

<div class="container">
    <div class="portals-header">
        <h1>Job Portals</h1>
        <p class="portals-subtitle">Configure integrations with external job boards for seamless publishing</p>
        
        <nav class="breadcrumb">
            <a href="{% url 'settings' %}">Settings</a>
            <span class="material-icons">chevron_right</span>
            <span>Job Portals</span>
        </nav>
    </div>
    
    <!-- Portal Status Overview -->
    <div class="portal-status-container">
        <h2>Portal Status Overview</h2>
        <p class="portal-status-subtitle">Manage your connections to external job portals</p>
        
        <div class="portal-status-grid">
            <!-- LinkedIn -->
            <div class="portal-status-card">
                <div class="portal-logo">
                    <img src="{% static 'images/linkedin-logo.png' %}" alt="LinkedIn" onerror="this.src='https://cdn.icon-icons.com/icons2/2428/PNG/512/linkedin_black_logo_icon_147114.png'; this.onerror=null;">
                </div>
                <div class="portal-info">
                    <h3>LinkedIn</h3>
                    <span class="portal-status connected">
                        <span class="material-icons">check_circle</span> Connected
                    </span>
                </div>
                <div class="portal-actions">
                    <button class="portal-action-btn configure-btn" data-portal="linkedin">
                        <span class="material-icons">settings</span>
                        Configure
                    </button>
                </div>
            </div>
            
            <!-- Indeed -->
            <div class="portal-status-card">
                <div class="portal-logo">
                    <img src="{% static 'images/indeed-logo.png' %}" alt="Indeed" onerror="this.src='https://cdn.icon-icons.com/icons2/2699/PNG/512/indeed_logo_icon_169025.png'; this.onerror=null;">
                </div>
                <div class="portal-info">
                    <h3>Indeed</h3>
                    <span class="portal-status not-connected">
                        <span class="material-icons">cancel</span> Not Connected
                    </span>
                </div>
                <div class="portal-actions">
                    <button class="portal-action-btn connect-btn" data-portal="indeed">
                        <span class="material-icons">add_circle</span>
                        Connect
                    </button>
                </div>
            </div>
            
            <!-- Glassdoor -->
            <div class="portal-status-card">
                <div class="portal-logo">
                    <img src="{% static 'images/glassdoor-logo.png' %}" alt="Glassdoor" onerror="this.src='https://cdn.icon-icons.com/icons2/2407/PNG/512/glassdoor_icon_146136.png'; this.onerror=null;">
                </div>
                <div class="portal-info">
                    <h3>Glassdoor</h3>
                    <span class="portal-status connected">
                        <span class="material-icons">check_circle</span> Connected
                    </span>
                </div>
                <div class="portal-actions">
                    <button class="portal-action-btn configure-btn" data-portal="glassdoor">
                        <span class="material-icons">settings</span>
                        Configure
                    </button>
                </div>
            </div>
            
            <!-- ZipRecruiter -->
            <div class="portal-status-card">
                <div class="portal-logo">
                    <img src="{% static 'images/ziprecruiter-logo.png' %}" alt="ZipRecruiter" onerror="this.src='https://via.placeholder.com/150?text=ZipRecruiter'; this.onerror=null;">
                </div>
                <div class="portal-info">
                    <h3>ZipRecruiter</h3>
                    <span class="portal-status not-connected">
                        <span class="material-icons">cancel</span> Not Connected
                    </span>
                </div>
                <div class="portal-actions">
                    <button class="portal-action-btn connect-btn" data-portal="ziprecruiter">
                        <span class="material-icons">add_circle</span>
                        Connect
                    </button>
                </div>
            </div>
            
            <!-- Monster -->
            <div class="portal-status-card">
                <div class="portal-logo">
                    <img src="{% static 'images/monster-logo.png' %}" alt="Monster" onerror="this.src='https://via.placeholder.com/150?text=Monster'; this.onerror=null;">
                </div>
                <div class="portal-info">
                    <h3>Monster</h3>
                    <span class="portal-status not-connected">
                        <span class="material-icons">cancel</span> Not Connected
                    </span>
                </div>
                <div class="portal-actions">
                    <button class="portal-action-btn connect-btn" data-portal="monster">
                        <span class="material-icons">add_circle</span>
                        Connect
                    </button>
                </div>
            </div>
            
            <!-- Canvider -->
            <div class="portal-status-card">
                <div class="portal-logo">
                    <img src="{% static 'images/canvider-logo.png' %}" alt="Canvider" onerror="this.src='https://via.placeholder.com/150?text=Canvider'; this.onerror=null;">
                </div>
                <div class="portal-info">
                    <h3>Canvider</h3>
                    <span class="portal-status connected">
                        <span class="material-icons">check_circle</span> Connected
                    </span>
                </div>
                <div class="portal-actions">
                    <button class="portal-action-btn configure-btn" data-portal="canvider">
                        <span class="material-icons">settings</span>
                        Configure
                    </button>
                </div>
            </div>
            
            <!-- Praca.pl -->
            <div class="portal-status-card">
                <div class="portal-logo">
                    <img src="{% static 'images/praca-logo.png' %}" alt="Praca.pl" onerror="this.src='https://via.placeholder.com/150?text=Praca.pl'; this.onerror=null;">
                </div>
                <div class="portal-info">
                    <h3>Praca.pl</h3>
                    <span class="portal-status connected">
                        <span class="material-icons">check_circle</span> Connected
                    </span>
                </div>
                <div class="portal-actions">
                    <button class="portal-action-btn configure-btn" data-portal="praca">
                        <span class="material-icons">settings</span>
                        Configure
                    </button>
                </div>
            </div>
            
            <!-- Add New Portal Button -->
            <div class="add-portal-card">
                <div class="add-portal-content">
                    <span class="material-icons">add_circle_outline</span>
                    <span>Add Custom Job Portal</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Portal Configuration Details -->
    <div class="portal-config-container">
        <h2>Portal Configurations</h2>
        <p class="portal-config-subtitle">Manage detailed settings for each connected job portal</p>
        
        <!-- Tabs for connected portals -->
        <div class="tabs-container">
            <div class="tabs">
                <button class="tab-btn active" data-tab="linkedin">
                    LinkedIn
                </button>
                <button class="tab-btn" data-tab="glassdoor">
                    Glassdoor
                </button>
                <button class="tab-btn" data-tab="canvider">
                    Canvider
                </button>
                <button class="tab-btn" data-tab="praca">
                    Praca.pl
                </button>
            </div>
            
            <div class="tab-content">
                <!-- LinkedIn Configuration Tab -->
                <div class="tab-pane active" id="linkedin">
                    <div class="portal-detail-header">
                        <div class="portal-detail-logo">
                            <img src="{% static 'images/linkedin-logo.png' %}" alt="LinkedIn" onerror="this.src='https://cdn.icon-icons.com/icons2/2428/PNG/512/linkedin_black_logo_icon_147114.png'; this.onerror=null;">
                        </div>
                        <div class="portal-detail-info">
                            <h3>LinkedIn Integration</h3>
                            <p>Manage your LinkedIn job posting API integration</p>
                        </div>
                        <div class="portal-detail-actions">
                            <button class="test-connection-btn">
                                <span class="material-icons">sync</span>
                                Test Connection
                            </button>
                            <button class="disconnect-btn">
                                <span class="material-icons">link_off</span>
                                Disconnect
                            </button>
                        </div>
                    </div>
                    
                    <div class="portal-detail-body">
                        <div class="portal-form">
                            <div class="form-section">
                                <h4>API Configuration</h4>
                                
                                <div class="form-group">
                                    <label for="linkedin-client-id">Client ID</label>
                                    <div class="input-with-copy">
                                        <input type="text" id="linkedin-client-id" value="12a3b456c7d8ef90" readonly>
                                        <button class="copy-btn" data-clipboard="linkedin-client-id">
                                            <span class="material-icons">content_copy</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="linkedin-client-secret">Client Secret</label>
                                    <div class="input-with-copy">
                                        <input type="password" id="linkedin-client-secret" value="••••••••••••••••" readonly>
                                        <button class="toggle-password-btn" data-target="linkedin-client-secret">
                                            <span class="material-icons">visibility</span>
                                        </button>
                                        <button class="copy-btn" data-clipboard="linkedin-client-secret">
                                            <span class="material-icons">content_copy</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="linkedin-redirect-uri">Redirect URI</label>
                                    <div class="input-with-copy">
                                        <input type="text" id="linkedin-redirect-uri" value="https://yourapp.com/callback/linkedin" readonly>
                                        <button class="copy-btn" data-clipboard="linkedin-redirect-uri">
                                            <span class="material-icons">content_copy</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="linkedin-access-token">Access Token</label>
                                    <div class="input-with-copy">
                                        <input type="password" id="linkedin-access-token" value="••••••••••••••••••••••••••••••••••••••" readonly>
                                        <button class="toggle-password-btn" data-target="linkedin-access-token">
                                            <span class="material-icons">visibility</span>
                                        </button>
                                        <button class="copy-btn" data-clipboard="linkedin-access-token">
                                            <span class="material-icons">content_copy</span>
                                        </button>
                                    </div>
                                    <div class="token-expires">
                                        Expires: <span>January 15, 2026</span>
                                        <button class="refresh-token-btn">Refresh Token</button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h4>Company Information</h4>
                                
                                <div class="form-group">
                                    <label for="linkedin-company-id">LinkedIn Company ID</label>
                                    <input type="text" id="linkedin-company-id" value="1234567">
                                </div>
                                
                                <div class="form-group">
                                    <label for="linkedin-company-page">LinkedIn Company Page URL</label>
                                    <input type="text" id="linkedin-company-page" value="https://www.linkedin.com/company/your-company">
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h4>Posting Preferences</h4>
                                
                                <div class="form-group">
                                    <label for="linkedin-default-language">Default Language</label>
                                    <select id="linkedin-default-language">
                                        <option value="en_US" selected>English (US)</option>
                                        <option value="en_UK">English (UK)</option>
                                        <option value="fr_FR">French</option>
                                        <option value="de_DE">German</option>
                                        <option value="es_ES">Spanish</option>
                                        <option value="pl_PL">Polish</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label>Auto-publish Settings</label>
                                    <div class="checkbox-group">
                                        <input type="checkbox" id="linkedin-auto-publish" checked>
                                        <label for="linkedin-auto-publish">Auto-publish jobs to LinkedIn</label>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label>Application Form</label>
                                    <div class="radio-group">
                                        <div class="radio-item">
                                            <input type="radio" id="linkedin-company-application" name="linkedin-application-type" checked>
                                            <label for="linkedin-company-application">Use LinkedIn's application form</label>
                                        </div>
                                        <div class="radio-item">
                                            <input type="radio" id="linkedin-external-application" name="linkedin-application-type">
                                            <label for="linkedin-external-application">Redirect to your careers site</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button class="save-btn">Save Configuration</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Glassdoor Configuration Tab -->
                <div class="tab-pane" id="glassdoor">
                    <div class="portal-detail-header">
                        <div class="portal-detail-logo">
                            <img src="{% static 'images/glassdoor-logo.png' %}" alt="Glassdoor" onerror="this.src='https://cdn.icon-icons.com/icons2/2407/PNG/512/glassdoor_icon_146136.png'; this.onerror=null;">
                        </div>
                        <div class="portal-detail-info">
                            <h3>Glassdoor Integration</h3>
                            <p>Manage your Glassdoor job posting API integration</p>
                        </div>
                        <div class="portal-detail-actions">
                            <button class="test-connection-btn">
                                <span class="material-icons">sync</span>
                                Test Connection
                            </button>
                            <button class="disconnect-btn">
                                <span class="material-icons">link_off</span>
                                Disconnect
                            </button>
                        </div>
                    </div>
                    
                    <div class="portal-detail-body">
                        <div class="portal-form">
                            <div class="form-section">
                                <h4>API Configuration</h4>
                                
                                <div class="form-group">
                                    <label for="glassdoor-partner-id">Partner ID</label>
                                    <input type="text" id="glassdoor-partner-id" value="GD12345">
                                </div>
                                
                                <div class="form-group">
                                    <label for="glassdoor-api-key">API Key</label>
                                    <div class="input-with-copy">
                                        <input type="password" id="glassdoor-api-key" value="••••••••••••••••" readonly>
                                        <button class="toggle-password-btn" data-target="glassdoor-api-key">
                                            <span class="material-icons">visibility</span>
                                        </button>
                                        <button class="copy-btn" data-clipboard="glassdoor-api-key">
                                            <span class="material-icons">content_copy</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h4>Company Information</h4>
                                
                                <div class="form-group">
                                    <label for="glassdoor-employer-id">Glassdoor Employer ID</label>
                                    <input type="text" id="glassdoor-employer-id" value="123456">
                                </div>
                                
                                <div class="form-group">
                                    <label for="glassdoor-company-page">Glassdoor Company Profile URL</label>
                                    <input type="text" id="glassdoor-company-page" value="https://www.glassdoor.com/Overview/Working-at-Your-Company-EI_IE123456.11,23.htm">
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h4>Posting Preferences</h4>
                                
                                <div class="form-group">
                                    <label>Auto-publish Settings</label>
                                    <div class="checkbox-group">
                                        <input type="checkbox" id="glassdoor-auto-publish" checked>
                                        <label for="glassdoor-auto-publish">Auto-publish jobs to Glassdoor</label>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label>Job Application Type</label>
                                    <div class="radio-group">
                                        <div class="radio-item">
                                            <input type="radio" id="glassdoor-quick-apply" name="glassdoor-application-type">
                                            <label for="glassdoor-quick-apply">Enable Glassdoor Quick Apply</label>
                                        </div>
                                        <div class="radio-item">
                                            <input type="radio" id="glassdoor-external-apply" name="glassdoor-application-type" checked>
                                            <label for="glassdoor-external-apply">Use external application URL</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="glassdoor-expiration">Default Job Expiration</label>
                                    <select id="glassdoor-expiration">
                                        <option value="30" selected>30 days</option>
                                        <option value="60">60 days</option>
                                        <option value="90">90 days</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button class="save-btn">Save Configuration</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Canvider Configuration Tab -->
                <div class="tab-pane" id="canvider">
                    <div class="portal-detail-header">
                        <div class="portal-detail-logo">
                            <img src="{% static 'images/canvider-logo.png' %}" alt="Canvider" onerror="this.src='https://via.placeholder.com/150?text=Canvider'; this.onerror=null;">
                        </div>
                        <div class="portal-detail-info">
                            <h3>Canvider Integration</h3>
                            <p>Manage your Canvider job posting API integration</p>
                        </div>
                        <div class="portal-detail-actions">
                            <button class="test-connection-btn">
                                <span class="material-icons">sync</span>
                                Test Connection
                            </button>
                            <button class="disconnect-btn">
                                <span class="material-icons">link_off</span>
                                Disconnect
                            </button>
                        </div>
                    </div>
                    
                    <div class="portal-detail-body">
                        <div class="portal-form">
                            <div class="form-section">
                                <h4>API Configuration</h4>
                                
                                <div class="form-group">
                                    <label for="canvider-api-key">API Key</label>
                                    <div class="input-with-copy">
                                        <input type="password" id="canvider-api-key" value="••••••••••••••••" readonly>
                                        <button class="toggle-password-btn" data-target="canvider-api-key">
                                            <span class="material-icons">visibility</span>
                                        </button>
                                        <button class="copy-btn" data-clipboard="canvider-api-key">
                                            <span class="material-icons">content_copy</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="canvider-endpoint">API Endpoint</label>
                                    <input type="text" id="canvider-endpoint" value="https://api.canvider.com/v1/jobs">
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h4>Company Information</h4>
                                
                                <div class="form-group">
                                    <label for="canvider-company-id">Canvider Company ID</label>
                                    <input type="text" id="canvider-company-id" value="COMPANY12345">
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h4>Posting Preferences</h4>
                                
                                <div class="form-group">
                                    <label>Auto-publish Settings</label>
                                    <div class="checkbox-group">
                                        <input type="checkbox" id="canvider-auto-publish" checked>
                                        <label for="canvider-auto-publish">Auto-publish jobs to Canvider</label>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="canvider-categories">Default Job Categories</label>
                                    <select id="canvider-categories" multiple>
                                        <option value="tech" selected>Technology</option>
                                        <option value="engineering" selected>Engineering</option>
                                        <option value="design">Design</option>
                                        <option value="marketing">Marketing</option>
                                        <option value="sales">Sales</option>
                                        <option value="hr">Human Resources</option>
                                    </select>
                                    <small>Hold Ctrl/Cmd to select multiple categories</small>
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button class="save-btn">Save Configuration</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Praca.pl Configuration Tab -->
                <div class="tab-pane" id="praca">
                    <div class="portal-detail-header">
                        <div class="portal-detail-logo">
                            <img src="{% static 'images/praca-logo.png' %}" alt="Praca.pl" onerror="this.src='https://via.placeholder.com/150?text=Praca.pl'; this.onerror=null;">
                        </div>
                        <div class="portal-detail-info">
                            <h3>Praca.pl Integration</h3>
                            <p>Manage your Praca.pl job posting API integration</p>
                        </div>
                        <div class="portal-detail-actions">
                            <button class="test-connection-btn">
                                <span class="material-icons">sync</span>
                                Test Connection
                            </button>
                            <button class="disconnect-btn">
                                <span class="material-icons">link_off</span>
                                Disconnect
                            </button>
                        </div>
                    </div>
                    
                    <div class="portal-detail-body">
                        <div class="portal-form">
                            <div class="form-section">
                                <h4>API Configuration</h4>
                                
                                <div class="form-group">
                                    <label for="praca-username">API Username</label>
                                    <input type="text" id="praca-username" value="api_yourcompany">
                                </div>
                                
                                <div class="form-group">
                                    <label for="praca-api-key">API Key</label>
                                    <div class="input-with-copy">
                                        <input type="password" id="praca-api-key" value="••••••••••••••••" readonly>
                                        <button class="toggle-password-btn" data-target="praca-api-key">
                                            <span class="material-icons">visibility</span>
                                        </button>
                                        <button class="copy-btn" data-clipboard="praca-api-key">
                                            <span class="material-icons">content_copy</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="praca-endpoint">API Endpoint</label>
                                    <input type="text" id="praca-endpoint" value="https://api.praca.pl/partners/v1/jobs">
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h4>Company Information</h4>
                                
                                <div class="form-group">
                                    <label for="praca-company-id">Praca.pl Company ID</label>
                                    <input type="text" id="praca-company-id" value="12345">
                                </div>
                                
                                <div class="form-group">
                                    <label for="praca-company-page">Praca.pl Company Profile URL</label>
                                    <input type="text" id="praca-company-page" value="https://www.praca.pl/your-company_12345.html">
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h4>Posting Preferences</h4>
                                
                                <div class="form-group">
                                    <label>Auto-publish Settings</label>
                                    <div class="checkbox-group">
                                        <input type="checkbox" id="praca-auto-publish" checked>
                                        <label for="praca-auto-publish">Auto-publish jobs to Praca.pl</label>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="praca-default-language">Default Language</label>
                                    <select id="praca-default-language">
                                        <option value="pl_PL" selected>Polish</option>
                                        <option value="en_US">English (US)</option>
                                        <option value="en_UK">English (UK)</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="praca-category">Default Job Category</label>
                                    <select id="praca-category">
                                        <option value="it" selected>IT & Development</option>
                                        <option value="sales">Sales</option>
                                        <option value="marketing">Marketing</option>
                                        <option value="finance">Finance</option>
                                        <option value="hr">Human Resources</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button class="save-btn">Save Configuration</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Global Portal Settings -->
    <div class="global-settings-container">
        <h2>Global Portal Settings</h2>
        <p class="global-settings-subtitle">Configure settings that apply to all job portal integrations</p>
        
        <div class="settings-form">
            <div class="form-section">
                <h4>Default Job Publication</h4>
                
                <div class="form-group">
                    <label>Default Portals for New Jobs</label>
                    <p class="field-description">Select which portals should be enabled by default when creating new job postings</p>
                    <div class="checkbox-group-list">
                        <div class="checkbox-item">
                            <input type="checkbox" id="default-linkedin" checked>
                            <label for="default-linkedin">LinkedIn</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="default-glassdoor" checked>
                            <label for="default-glassdoor">Glassdoor</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="default-indeed">
                            <label for="default-indeed">Indeed</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="default-canvider" checked>
                            <label for="default-canvider">Canvider</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="default-praca" checked>
                            <label for="default-praca">Praca.pl</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="default-expiration">Default Job Expiration</label>
                    <select id="default-expiration">
                        <option value="30" selected>30 days</option>
                        <option value="60">60 days</option>
                        <option value="90">90 days</option>
                        <option value="0">No expiration</option>
                    </select>
                </div>
            </div>
            
            <div class="form-section">
                <h4>Application Handling</h4>
                
                <div class="form-group">
                    <label for="redirect-url">Default Redirect URL</label>
                    <input type="text" id="redirect-url" value="https://careers.yourcompany.com/apply">
                    <p class="field-description">URL where applicants will be redirected when applying through job portals</p>
                </div>
                
                <div class="form-group">
                    <label>Application Tracking</label>
                    <div class="checkbox-group">
                        <input type="checkbox" id="track-source" checked>
                        <label for="track-source">Track application source portal</label>
                    </div>
                    <p class="field-description">Automatically append UTM parameters to track which portal applications come from</p>
                </div>
            </div>
            
            <div class="form-section">
                <h4>Notifications</h4>
                
                <div class="form-group">
                    <label>Email Notifications</label>
                    <div class="checkbox-group">
                        <input type="checkbox" id="notify-publish" checked>
                        <label for="notify-publish">Notify when jobs are published to portals</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="notify-errors" checked>
                        <label for="notify-errors">Notify on publication errors</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="notify-expiring">
                        <label for="notify-expiring">Notify when job postings are about to expire</label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="notification-email">Notification Recipients</label>
                    <input type="text" id="notification-email" value="<EMAIL>">
                    <p class="field-description">Comma-separated list of email addresses to receive notifications</p>
                </div>
            </div>
            
            <div class="form-actions">
                <button class="save-btn">Save Global Settings</button>
            </div>
        </div>
    </div>
</div>

<!-- Connection Modal -->
<div class="modal" id="connect-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="connect-modal-title">Connect to <span id="portal-name">Job Portal</span></h2>
            <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
            <div class="portal-connection-logo" id="portal-logo-container">
                <!-- Portal logo will be dynamically inserted here -->
            </div>
            
            <div class="connection-instructions">
                <h3>Setup Instructions</h3>
                <p id="connection-description">Follow these steps to connect your account to this job portal.</p>
                
                <ol class="steps-list" id="connection-steps">
                    <!-- Steps will be dynamically inserted here -->
                </ol>
            </div>
            
            <form id="connect-form">
                <div class="form-section">
                    <h4>API Credentials</h4>
                    
                    <div class="form-group">
                        <label for="new-api-key">API Key</label>
                        <input type="text" id="new-api-key" placeholder="Enter API key" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="new-api-secret">API Secret</label>
                        <input type="password" id="new-api-secret" placeholder="Enter API secret" required>
                    </div>
                    
                    <div class="form-group" id="additional-fields">
                        <!-- Additional fields will be added dynamically based on the portal -->
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="cancel-btn" id="cancel-connect">Cancel</button>
                    <button type="submit" class="confirm-btn" id="save-connection">Connect Portal</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    :root {
        --primary: #4a6cf7;
        --primary-hover: #3859e9;
        --secondary: #f5f8ff;
        --success: #28a745;
        --warning: #ffc107;
        --danger: #dc3545;
        --text-color: #333;
        --border-color: #ddd;
        --hover-bg: #f9fafb;
        --shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }
    
    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    body {
        background-color: #f9fafc;
        color: var(--text-color);
        line-height: 1.6;
    }
    
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;
    }
    
    /* Portals Header */
    .portals-header {
        margin-bottom: 30px;
    }
    
    .portals-header h1 {
        font-size: 28px;
        color: #252b42;
        margin-bottom: 10px;
    }
    
    .portals-subtitle, 
    .portal-status-subtitle,
    .portal-config-subtitle,
    .global-settings-subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 20px;
    }
    
    /* Breadcrumb */
    .breadcrumb {
        display: flex;
        align-items: center;
        font-size: 14px;
    }
    
    .breadcrumb a {
        color: var(--primary);
        text-decoration: none;
    }
    
    .breadcrumb a:hover {
        text-decoration: underline;
    }
    
    .breadcrumb .material-icons {
        font-size: 16px;
        margin: 0 8px;
        color: #999;
    }
    
    /* Portal Status Grid */
    .portal-status-container {
        background-color: white;
        border-radius: 8px;
        padding: 30px;
        box-shadow: var(--shadow);
        margin-bottom: 30px;
    }
    
    .portal-status-container h2 {
        font-size: 20px;
        margin-bottom: 8px;
        color: #252b42;
    }
    
    .portal-status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }
    
    .portal-status-card {
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 20px;
        display: flex;
        align-items: center;
        transition: all 0.3s;
    }
    
    .portal-status-card:hover {
        border-color: var(--primary);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }
    
    .portal-logo {
        flex: 0 0 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .portal-logo img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }
    
    .portal-info {
        margin-left: 15px;
        flex: 1;
    }
    
    .portal-info h3 {
        font-size: 16px;
        margin-bottom: 5px;
    }
    
    .portal-status {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
    }
    
    .portal-status .material-icons {
        font-size: 16px;
        margin-right: 4px;
    }
    
    .portal-status.connected {
        color: var(--success);
    }
    
    .portal-status.not-connected {
        color: var(--danger);
    }
    
    .portal-actions {
        margin-left: auto;
    }
    
    .portal-action-btn {
        background-color: white;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        padding: 6px 10px;
        font-size: 13px;
        display: flex;
        align-items: center;
        gap: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }
    
    .portal-action-btn .material-icons {
        font-size: 16px;
    }
    
    .portal-action-btn:hover {
        background-color: var(--hover-bg);
    }
    
    .configure-btn {
        color: var(--primary);
    }
    
    .configure-btn:hover {
        border-color: var(--primary);
        background-color: var(--secondary);
    }
    
    .connect-btn {
        color: var(--success);
    }
    
    .connect-btn:hover {
        border-color: var(--success);
        background-color: #e6f7ea;
    }
    
    /* Add Portal Card */
    .add-portal-card {
        border: 2px dashed var(--border-color);
        border-radius: 6px;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
    }
    
    .add-portal-card:hover {
        border-color: var(--primary);
        background-color: var(--secondary);
    }
    
    .add-portal-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        color: #666;
    }
    
    .add-portal-content .material-icons {
        font-size: 30px;
    }
    
    /* Portal Configuration */
    .portal-config-container {
        background-color: white;
        border-radius: 8px;
        padding: 30px;
        box-shadow: var(--shadow);
        margin-bottom: 30px;
    }
    
    .portal-config-container h2 {
        font-size: 20px;
        margin-bottom: 8px;
        color: #252b42;
    }
    
    /* Tabs Container */
    .tabs-container {
        margin-top: 20px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        overflow: hidden;
    }
    
    /* Tabs Navigation */
    .tabs {
        display: flex;
        overflow-x: auto;
        border-bottom: 1px solid var(--border-color);
        background-color: #f9fafb;
    }
    
    .tab-btn {
        padding: 12px 20px;
        background: none;
        border: none;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        color: #666;
        border-bottom: 3px solid transparent;
        white-space: nowrap;
        transition: all 0.3s;
    }
    
    .tab-btn:hover {
        background-color: #f0f4ff;
        color: var(--primary);
    }
    
    .tab-btn.active {
        color: var(--primary);
        border-bottom-color: var(--primary);
        background-color: white;
    }
    
    /* Tab Content */
    .tab-content {
        min-height: 400px;
    }
    
    .tab-pane {
        display: none;
    }
    
    .tab-pane.active {
        display: block;
        animation: fadeIn 0.3s;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    /* Portal Detail Header */
    .portal-detail-header {
        padding: 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        align-items: center;
    }
    
    .portal-detail-logo {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
    }
    
    .portal-detail-logo img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }
    
    .portal-detail-info {
        flex: 1;
    }
    
    .portal-detail-info h3 {
        font-size: 18px;
        margin-bottom: 5px;
    }
    
    .portal-detail-info p {
        color: #666;
        font-size: 14px;
    }
    
    .portal-detail-actions {
        display: flex;
        gap: 10px;
    }
    
    .test-connection-btn,
    .disconnect-btn {
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;
    }
    
    .test-connection-btn {
        background-color: var(--secondary);
        color: var(--primary);
        border: 1px solid var(--primary);
    }
    
    .test-connection-btn:hover {
        background-color: var(--primary);
        color: white;
    }
    
    .disconnect-btn {
        background-color: white;
        color: var(--danger);
        border: 1px solid var(--danger);
    }
    
    .disconnect-btn:hover {
        background-color: var(--danger);
        color: white;
    }
    
    /* Portal Detail Body */
    .portal-detail-body {
        padding: 20px;
    }
    
    /* Portal Form */
    .portal-form,
    .settings-form {
        max-width: 800px;
        margin: 0 auto;
    }
    
    .form-section {
        margin-bottom: 30px;
    }
    
    .form-section h4 {
        font-size: 16px;
        color: #252b42;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--border-color);
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        font-size: 14px;
    }
    
    .form-group input,
    .form-group textarea,
    .form-group select {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 14px;
    }
    
    .form-group input:focus,
    .form-group textarea:focus,
    .form-group select:focus {
        outline: none;
        border-color: var(--primary);
    }
    
    .form-group select[multiple] {
        height: 120px;
    }
    
    .field-description {
        font-size: 13px;
        color: #666;
        margin-top: 4px;
    }
    
    /* Input with Copy Button */
    .input-with-copy {
        position: relative;
        display: flex;
    }
    
    .input-with-copy input {
        padding-right: 70px;
        flex: 1;
    }
    
    .copy-btn,
    .toggle-password-btn {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        background: none;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        cursor: pointer;
        color: #666;
        transition: color 0.3s;
    }
    
    .toggle-password-btn {
        right: 36px;
    }
    
    .copy-btn:hover,
    .toggle-password-btn:hover {
        color: var(--primary);
    }
    
    /* Token Expires */
    .token-expires {
        font-size: 13px;
        color: #666;
        margin-top: 8px;
        display: flex;
        align-items: center;
    }
    
    .refresh-token-btn {
        margin-left: 10px;
        background: none;
        border: none;
        color: var(--primary);
        font-size: 13px;
        cursor: pointer;
        text-decoration: underline;
    }
    
    /* Checkbox and Radio Styles */
    .checkbox-group {
        display: flex;
        align-items: center;
    }
    
    .checkbox-group input[type="checkbox"] {
        width: auto;
        margin-right: 8px;
    }
    
    .checkbox-group-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .checkbox-item {
        display: flex;
        align-items: center;
    }
    
    .checkbox-item input[type="checkbox"] {
        width: auto;
        margin-right: 8px;
    }
    
    .radio-group {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .radio-item {
        display: flex;
        align-items: center;
    }
    
    .radio-item input[type="radio"] {
        width: auto;
        margin-right: 8px;
    }
    
    /* Form Actions */
    .form-actions {
        margin-top: 30px;
        display: flex;
        justify-content: flex-end;
    }
    
    .save-btn {
        background-color: var(--primary);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s;
    }
    
    .save-btn:hover {
        background-color: var(--primary-hover);
    }
    
    /* Global Settings */
    .global-settings-container {
        background-color: white;
        border-radius: 8px;
        padding: 30px;
        box-shadow: var(--shadow);
        margin-bottom: 30px;
    }
    
    .global-settings-container h2 {
        font-size: 20px;
        margin-bottom: 8px;
        color: #252b42;
    }
    
    /* Connection Modal */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        overflow: auto;
    }
    
    .modal-content {
        background-color: white;
        margin: 10% auto;
        max-width: 600px;
        width: 90%;
        border-radius: 8px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        animation: modalFadeIn 0.3s;
    }
    
    @keyframes modalFadeIn {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .modal-header {
        padding: 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .modal-header h2 {
        margin: 0;
        font-size: 20px;
    }
    
    .close-modal {
        font-size: 24px;
        cursor: pointer;
        color: #999;
    }
    
    .close-modal:hover {
        color: #333;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .portal-connection-logo {
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
    }
    
    .portal-connection-logo img {
        max-width: 100px;
        max-height: 100px;
        object-fit: contain;
    }
    
    .connection-instructions {
        margin-bottom: 30px;
    }
    
    .connection-instructions h3 {
        font-size: 18px;
        margin-bottom: 10px;
    }
    
    .steps-list {
        margin-left: 20px;
        margin-top: 15px;
    }
    
    .steps-list li {
        margin-bottom: 10px;
    }
    
    .modal-footer {
        padding-top: 20px;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
    
    .cancel-btn, .confirm-btn {
        padding: 10px 16px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
    }
    
    .cancel-btn {
        background-color: white;
        color: #666;
        border: 1px solid var(--border-color);
    }
    
    .cancel-btn:hover {
        background-color: #f5f5f5;
    }
    
    .confirm-btn {
        background-color: var(--primary);
        color: white;
        border: none;
    }
    
    .confirm-btn:hover {
        background-color: var(--primary-hover);
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .portal-detail-header {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .portal-detail-logo {
            margin-bottom: 15px;
        }
        
        .portal-detail-actions {
            margin-top: 15px;
            margin-left: 0;
        }
        
        .tabs {
            flex-wrap: wrap;
        }
        
        .tab-btn {
            flex: 1 0 auto;
            text-align: center;
        }
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons and panes
            tabBtns.forEach(b => b.classList.remove('active'));
            tabPanes.forEach(p => p.classList.remove('active'));
            
            // Add active class to clicked button and corresponding pane
            this.classList.add('active');
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });
    
    // Toggle password visibility
    const toggleButtons = document.querySelectorAll('.toggle-password-btn');
    
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const input = document.getElementById(targetId);
            
            if (input.type === 'password') {
                input.type = 'text';
                this.querySelector('.material-icons').textContent = 'visibility_off';
            } else {
                input.type = 'password';
                this.querySelector('.material-icons').textContent = 'visibility';
            }
        });
    });
    
    // Copy to clipboard functionality
    const copyButtons = document.querySelectorAll('.copy-btn');
    
    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-clipboard');
            const input = document.getElementById(targetId);
            
            // Select the text
            input.select();
            input.setSelectionRange(0, 99999); // For mobile devices
            
            // Copy the text
            document.execCommand('copy');
            
            // Visual feedback
            const originalIcon = this.querySelector('.material-icons').textContent;
            this.querySelector('.material-icons').textContent = 'check';
            
            // Reset after a short delay
            setTimeout(() => {
                this.querySelector('.material-icons').textContent = originalIcon;
            }, 2000);
        });
    });
    
    // Test connection button functionality
    const testButtons = document.querySelectorAll('.test-connection-btn');
    
    testButtons.forEach(button => {
        button.addEventListener('click', function() {
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="material-icons">sync</span> Testing...';
            this.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                alert('Connection test successful!');
                this.innerHTML = originalText;
                this.disabled = false;
            }, 1500);
        });
    });
    
    // Disconnect button functionality
    const disconnectButtons = document.querySelectorAll('.disconnect-btn');
    
    disconnectButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (confirm('Are you sure you want to disconnect this integration? All jobs will no longer be published to this portal.')) {
                // Handle disconnect logic here
                alert('Portal disconnected successfully.');
            }
        });
    });
    
    // Connect button functionality
    const connectButtons = document.querySelectorAll('.connect-btn');
    const connectModal = document.getElementById('connect-modal');
    const closeModalBtn = document.querySelector('.close-modal');
    const cancelConnectBtn = document.getElementById('cancel-connect');
    const portalNameSpan = document.getElementById('portal-name');
    const portalLogoContainer = document.getElementById('portal-logo-container');
    const connectionSteps = document.getElementById('connection-steps');
    const additionalFields = document.getElementById('additional-fields');
    
    connectButtons.forEach(button => {
        button.addEventListener('click', function() {
            const portalId = this.getAttribute('data-portal');
            openConnectModal(portalId);
        });
    });
    
    function openConnectModal(portalId) {
        // Set portal name in modal title
        portalNameSpan.textContent = getPortalName(portalId);
        
        // Set portal logo
        portalLogoContainer.innerHTML = `<img src="{% static 'images/${portalId}-logo.png' %}" alt="${getPortalName(portalId)}" onerror="this.src='https://via.placeholder.com/100?text=${getPortalName(portalId)}'; this.onerror=null;">`;
        
        // Set connection steps
        connectionSteps.innerHTML = getConnectionSteps(portalId);
        
        // Set additional fields
        additionalFields.innerHTML = getAdditionalFields(portalId);
        
        // Show modal
        connectModal.style.display = 'block';
    }
    
    function getPortalName(portalId) {
        const portalNames = {
            'linkedin': 'LinkedIn',
            'indeed': 'Indeed',
            'glassdoor': 'Glassdoor',
            'ziprecruiter': 'ZipRecruiter',
            'monster': 'Monster',
            'canvider': 'Canvider',
            'praca': 'Praca.pl'
        };
        
        return portalNames[portalId] || 'Job Portal';
    }
    
    function getConnectionSteps(portalId) {
        // Return different steps based on portal
        switch(portalId) {
            case 'indeed':
                return `
                    <li>Log in to your Indeed Employer account</li>
                    <li>Go to Account Settings > API Access</li>
                    <li>Generate a new API key and copy it</li>
                    <li>Enter the API credentials below</li>
                `;
            case 'ziprecruiter':
                return `
                    <li>Log in to your ZipRecruiter employer account</li>
                    <li>Navigate to Account > Developer Settings</li>
                    <li>Create a new API key</li>
                    <li>Copy your API key and API secret</li>
                    <li>Enter the credentials below</li>
                `;
            case 'monster':
                return `
                    <li>Log in to your Monster employer account</li>
                    <li>Go to Account > Integrations</li>
                    <li>Request API access if you don't already have it</li>
                    <li>Generate a new API key and note your employer ID</li>
                    <li>Enter the credentials below</li>
                `;
            default:
                return `
                    <li>Log in to your ${getPortalName(portalId)} employer account</li>
                    <li>Navigate to your account settings or developer section</li>
                    <li>Generate API credentials</li>
                    <li>Enter the credentials below</li>
                `;
        }
    }
    
    function getAdditionalFields(portalId) {
        // Return different fields based on portal
        switch(portalId) {
            case 'indeed':
                return `
                    <div class="form-group">
                        <label for="indeed-employer-id">Indeed Employer ID</label>
                        <input type="text" id="indeed-employer-id" placeholder="Enter employer ID" required>
                    </div>
                    <div class="form-group">
                        <label for="indeed-token-endpoint">Callback URL</label>
                        <input type="text" id="indeed-token-endpoint" value="https://yourapp.com/callback/indeed" readonly>
                        <p class="field-description">Enter this URL in your Indeed API settings</p>
                    </div>
                `;
            case 'monster':
                return `
                    <div class="form-group">
                        <label for="monster-account-id">Monster Account ID</label>
                        <input type="text" id="monster-account-id" placeholder="Enter account ID" required>
                    </div>
                `;
            case 'ziprecruiter':
                return `
                    <div class="form-group">
                        <label for="ziprecruiter-client-id">Client ID</label>
                        <input type="text" id="ziprecruiter-client-id" placeholder="Enter client ID" required>
                    </div>
                `;
            default:
                return ''; // No additional fields for other portals
        }
    }
    
    // Close modal
    function closeConnectModal() {
        connectModal.style.display = 'none';
    }
    
    closeModalBtn.addEventListener('click', closeConnectModal);
    cancelConnectBtn.addEventListener('click', closeConnectModal);
    
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === connectModal) {
            closeConnectModal();
        }
    });
    
    // Add custom portal card
    const addPortalCard = document.querySelector('.add-portal-card');
    
    addPortalCard.addEventListener('click', function() {
        alert('Custom job portal integration feature coming soon!');
    });
    
    // Form submission for connection
    const connectForm = document.getElementById('connect-form');
    
    connectForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Here you would normally send the data to the server
        // For now, we'll just simulate success and close the modal
        
        const saveButton = document.getElementById('save-connection');
        const originalText = saveButton.textContent;
        saveButton.textContent = 'Connecting...';
        saveButton.disabled = true;
        
        // Simulate API call
        setTimeout(() => {
            alert('Portal connected successfully! Refresh the page to see the updated status.');
            saveButton.textContent = originalText;
            saveButton.disabled = false;
            closeConnectModal();
        }, 2000);
    });
    
    // Save configuration buttons
    const saveButtons = document.querySelectorAll('.save-btn');
    
    saveButtons.forEach(button => {
        button.addEventListener('click', function() {
            const originalText = this.textContent;
            this.textContent = 'Saving...';
            this.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                alert('Settings saved successfully!');
                this.textContent = originalText;
                this.disabled = false;
            }, 1500);
        });
    });
    
    // Refresh token button
    const refreshTokenBtn = document.querySelector('.refresh-token-btn');
    
    refreshTokenBtn.addEventListener('click', function() {
        const originalText = this.textContent;
        this.textContent = 'Refreshing...';
        this.disabled = true;
        
        // Simulate API call
        setTimeout(() => {
            alert('Token refreshed successfully! New expiration: January 15, 2027');
            this.textContent = originalText;
            this.disabled = false;
        }, 1500);
    });
});
</script>
{% endblock %}