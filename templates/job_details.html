{% extends 'main.html' %}

{% load static %}
{% load i18n %}
{% block content %}
<div class="container">
    <h1>{% trans "Job Description" %}</h1>

    <!-- Summary of info from previous page -->
    <div class="summary-container">
        <h2>{% trans "Job Summary" %}</h2>
        <div class="summary-grid" id="job-summary">
            <!-- Data will be loaded here from sessionStorage -->
            <div class="summary-loading">{% trans "Loading job details..." %}</div>
        </div>
    </div>

    <!-- Template selection container -->
    <div class="template-selection-container">
        <h2>{% trans "Job Description" %}</h2>
        <div class="template-options">
            <div class="template-option active" data-option="new">
                <i class="fas fa-file"></i>
                <span>{% trans "Create new description" %}</span>
            </div>
            <div class="template-option" data-option="template">
                <i class="fas fa-copy"></i>
                <span>{% trans "Use saved template" %}</span>
            </div>
        </div>

        <div class="template-selector" style="display: none;">
            <label for="template-select">{% trans "Choose a template:" %}</label>
            <select id="template-select">
                <option value="">{% trans "Select a template" %}</option>
                <!-- Templates will be loaded here -->
            </select>
        </div>
    </div>

    <div class="ai-generation-container">
        <h2>{% trans "AI Job Description Generator" %}</h2>
        <p class="ai-hint">{% trans "Let AI create a professional job description based on your job details above." %}</p>
        <button class="ai-generate-btn" id="ai-generate-btn">
            <i class="fas fa-robot"></i>
            {% trans "Generate with AI" %}
        </button>
    </div>

    <div class="editor-container">
        <h2>{% trans "Job Description" %}</h2>
        <p class="editor-hint">{% trans "Describe the position, responsibilities, qualifications, and any other relevant details." %}</p>

        <!-- Rich Text Editor Toolbar -->
        <div class="editor-toolbar">
            <div class="toolbar-group">
                <button type="button" data-command="formatBlock" data-value="H1" title="Heading 1">H1</button>
                <button type="button" data-command="formatBlock" data-value="H2" title="Heading 2">H2</button>
                <button type="button" data-command="formatBlock" data-value="H3" title="Heading 3">H3</button>
                <button type="button" data-command="formatBlock" data-value="P" title="Paragraph">P</button>
            </div>

            <div class="toolbar-group">
                <select id="font-size" title="Font Size">
                    <option value="1">Very Small</option>
                    <option value="2">Small</option>
                    <option value="3" selected>Normal</option>
                    <option value="4">Medium</option>
                    <option value="5">Large</option>
                    <option value="6">Very Large</option>
                    <option value="7">Extra Large</option>
                </select>
            </div>

            <div class="toolbar-group">
                <button type="button" data-command="bold" title="Bold"><i class="fas fa-bold"></i></button>
                <button type="button" data-command="italic" title="Italic"><i class="fas fa-italic"></i></button>
                <button type="button" data-command="underline" title="Underline"><i class="fas fa-underline"></i></button>
            </div>

            <div class="toolbar-group">
                <button type="button" id="btn-bullet-list" title="Bullet List"><i class="fas fa-list-ul"></i></button>
                <button type="button" id="btn-number-list" title="Numbered List"><i class="fas fa-list-ol"></i></button>
            </div>

            <div class="toolbar-group">
                <button type="button" data-command="justifyLeft" title="Align Left"><i class="fas fa-align-left"></i></button>
                <button type="button" data-command="justifyCenter" title="Align Center"><i class="fas fa-align-center"></i></button>
                <button type="button" data-command="justifyRight" title="Align Right"><i class="fas fa-align-right"></i></button>
            </div>

            <div class="toolbar-group">
                <button type="button" data-command="createLink" title="Insert Link"><i class="fas fa-link"></i></button>
                <button type="button" data-command="unlink" title="Remove Link"><i class="fas fa-unlink"></i></button>
            </div>

            <div class="toolbar-group emoji-group">
                <button type="button" class="emoji-button" title="Add Emoji">😀</button>
                <div class="emoji-picker" id="emoji-picker">
                    <div class="emoji-row">
                        <button class="emoji" data-emoji="😀">😀</button>
                        <button class="emoji" data-emoji="😊">😊</button>
                        <button class="emoji" data-emoji="🙂">🙂</button>
                        <button class="emoji" data-emoji="😄">😄</button>
                        <button class="emoji" data-emoji="👍">👍</button>
                    </div>
                    <div class="emoji-row">
                        <button class="emoji" data-emoji="✅">✅</button>
                        <button class="emoji" data-emoji="⭐">⭐</button>
                        <button class="emoji" data-emoji="🚀">🚀</button>
                        <button class="emoji" data-emoji="💼">💼</button>
                        <button class="emoji" data-emoji="📝">📝</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rich Text Editor Content Area -->
        <div class="editor-content" id="editor" contenteditable="true"></div>

        <!-- Character Counter -->
        <div class="character-counter">
            <span id="char-count">0</span> / 10000 {% trans "characters" %}
        </div>

        <div class="buttons-container">
            <div class="left-buttons">
                <!-- Back button -->
                <button class="back-btn" id="back-btn">{% trans "Back" %}</button>
                <a href="{% url 'feed' %}" class="btn back-btn discard">{% trans "Discard" %}</a>
            </div>
            <div class="right-buttons">
                <!-- Update Template button - hidden by default -->
                <button class="update-template-btn" id="update-template-btn" style="display: none;">{% trans "Update Template" %}</button>

                <!-- Save as Template button -->
                <button class="save-template-btn" id="save-template-btn">{% trans "Save as Template" %}</button>

                <!-- Save & Continue button -->
                <button class="save-btn" id="save-btn">{% trans "Save & Continue" %}</button>
            </div>
        </div>
    </div>
    <div class="modal" id="save-template-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>{% trans "Save as Template" %}</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="template-title">{% trans "Template Title" %}</label>
                    <input type="text" id="template-title" placeholder="{% trans 'Enter a name for this template' %}">
                    <small class="error-message" id="template-title-error"></small>
                </div>
                <div class="modal-footer">
                    <button class="cancel-btn" id="cancel-save-template">{% trans "Cancel" %}</button>
                    <button class="confirm-btn" id="confirm-save-template">{% trans "Save Template" %}</button>
                </div>
            </div>
        </div>
    </div>
    <div class="toast" id="toast">
        <div class="toast-icon">
            <i class="fas fa-check-circle success-icon"></i>
            <i class="fas fa-exclamation-circle error-icon"></i>
        </div>
        <div class="toast-message"></div>
        <div class="toast-close">&times;</div>
    </div>
</div>

<style>
    :root {
        --primary: #4a6cf7;
        --primary-hover: #3859e9;
        --secondary: #f5f8ff;
        --text-color: #333;
        --border-color: #ddd;
        --shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
        background-color: #f9fafc;
        color: var(--text-color);
        line-height: 1.6;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;
    }

    h1, h2 {
        margin-bottom: 20px;
        color: #252b42;
    }

    h1 {
        font-size: 28px;
    }

    h2 {
        font-size: 20px;
        font-weight: 600;
    }

    /* Summary Container */
    .summary-container {
        background-color: white;
        border-radius: 8px;
        padding: 30px;
        box-shadow: var(--shadow);
        margin-bottom: 30px;
    }

    .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .summary-item {
        padding: 15px;
        background-color: var(--secondary);
        border-radius: 6px;
    }

    .summary-label {
        font-weight: 600;
        font-size: 14px;
        color: #555;
        margin-bottom: 5px;
    }

    .summary-value {
        font-size: 16px;
    }

    .summary-skills {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .skill-tag {
        background-color: var(--primary);
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 13px;
    }

    .benefit-tag {
        background-color:rgb(44, 44, 44);
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 13px;
    }

    .summary-loading {
        grid-column: 1 / -1;
        text-align: center;
        padding: 20px;
        color: #888;
        font-style: italic;
    }

    /* Editor Container */
    .editor-container {
        background-color: white;
        border-radius: 8px;
        padding: 30px;
        box-shadow: var(--shadow);
    }

    .editor-hint {
        color: #666;
        margin-bottom: 20px;
        font-size: 15px;
    }

    /* Rich Text Editor */
    .editor-toolbar {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        padding: 10px;
        background-color: #f5f5f5;
        border: 1px solid var(--border-color);
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
    }

    .toolbar-group {
        display: flex;
        border-right: 1px solid #ddd;
        padding-right: 10px;
        margin-right: 10px;
    }

    .toolbar-group:last-child {
        border-right: none;
    }

    .editor-toolbar button {
        background-color: transparent;
        border: none;
        cursor: pointer;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .editor-toolbar button:hover {
        background-color: #e9e9e9;
    }

    .editor-toolbar button.active {
        background-color: #d4e3ff;
        color: var(--primary);
        font-weight: bold;
        box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.1);
    }

    /* Heading button styles */
    .editor-toolbar button[data-command="formatBlock"][data-value="H1"],
    .editor-toolbar button[data-command="formatBlock"][data-value="H2"],
    .editor-toolbar button[data-command="formatBlock"][data-value="H3"] {
        font-weight: bold;
    }

    .editor-toolbar button[data-command="formatBlock"][data-value="H1"] {
        font-size: 16px;
    }

    .editor-toolbar button[data-command="formatBlock"][data-value="H2"] {
        font-size: 15px;
    }

    .editor-toolbar button[data-command="formatBlock"][data-value="H3"] {
        font-size: 14px;
    }

    #font-size {
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ccc;
        background-color: white;
    }

    .editor-content {
        min-height: 300px;
        border: 1px solid var(--border-color);
        border-top: none;
        padding: 20px;
        border-bottom-left-radius: 6px;
        border-bottom-right-radius: 6px;
        outline: none;
        overflow-y: auto;
        line-height: 1.5;
    }

    /* Heading styles within the editor */
    .editor-content h1 {
        font-size: 24px;
        font-weight: bold;
        margin: 20px 0 10px;
        color: #252b42;
    }

    .editor-content h2 {
        font-size: 20px;
        font-weight: bold;
        margin: 18px 0 9px;
        color: #252b42;
    }

    .editor-content h3 {
        font-size: 18px;
        font-weight: bold;
        margin: 16px 0 8px;
        color: #252b42;
    }

    .editor-content p {
        margin: 10px 0;
    }

    /* Ensure proper list display with nesting */
    .editor-content ul {
        list-style-type: disc;
        margin-left: 20px;
    }

    .editor-content ol {
        list-style-type: decimal;
        margin-left: 20px;
    }

    /* Nested list styling */
    .editor-content ul ul {
        list-style-type: circle;
        margin-left: 20px;
    }

    .editor-content ul ul ul {
        list-style-type: square;
        margin-left: 20px;
    }

    .editor-content ol ol {
        list-style-type: lower-alpha;
        margin-left: 20px;
    }

    .editor-content ol ol ol {
        list-style-type: lower-roman;
        margin-left: 20px;
    }

    /* Custom CSS for indented lists */
    .editor-content .custom-indent {
        margin-left: 20px;
    }

    .editor-content:focus {
        border-color: var(--primary);
    }

    /* Character Counter */
    .character-counter {
        margin-top: 10px;
        text-align: right;
        font-size: 14px;
        color: #666;
    }

    .character-counter.limit-exceeded {
        color: #dc3545;
        font-weight: bold;
    }

    /* Emoji Picker */
    .emoji-group {
        position: relative;
    }

    .emoji-button {
        font-size: 16px !important;
    }

    .emoji-picker {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        background-color: white;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 10px;
        box-shadow: var(--shadow);
        z-index: 100;
        width: 200px;
    }

    .emoji-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
    }

    .emoji-row:last-child {
        margin-bottom: 0;
    }

    .emoji {
        cursor: pointer;
        font-size: 18px;
        padding: 5px;
        border-radius: 4px;
        border: none;
        background: transparent;
    }

    .emoji:hover {
        background-color: var(--secondary);
    }

    /* Buttons */
    .buttons-container {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
    }

    .back-btn, .save-btn {
        padding: 12px 30px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .back-btn {
        background-color: white;
        color: var(--text-color);
        border: 1px solid var(--border-color);
    }

    .discard {
        border: 1px solid rgb(100, 24, 34);
    }

    .back-btn:hover {
        background-color: #f5f5f5;
    }

    .save-btn {
        background-color: var(--primary);
        color: white;
        border: none;
    }

    .save-btn:hover {
        background-color: var(--primary-hover);
    }

    /* Media Queries */
    @media (max-width: 768px) {
        .toolbar-group {
            margin-right: 5px;
            padding-right: 5px;
        }

        .editor-toolbar button {
            padding: 5px 8px;
            font-size: 13px;
        }

        .emoji-picker {
            width: 180px;
        }
    }

    /* Template Selection Styling */
    .template-selection-container {
        background-color: white;
        border-radius: 8px;
        padding: 30px;
        box-shadow: var(--shadow);
        margin-bottom: 30px;
    }

    .template-options {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }

    .template-option {
        flex: 1;
        padding: 15px;
        border: 2px solid var(--border-color);
        border-radius: 6px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .template-option i {
        font-size: 24px;
        color: #666;
    }

    .template-option.active {
        border-color: var(--primary);
        background-color: var(--secondary);
    }

    .template-option.active i {
        color: var(--primary);
    }

    .template-option:hover {
        border-color: var(--primary);
    }

    .template-selector {
        margin-top: 20px;
    }

    .template-selector label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
    }

    .template-selector select {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.3s;
    }

    .template-selector select:focus {
        outline: none;
        border-color: var(--primary);
    }

    /* Template loading indicators */
    .loading-template {
        color: #666;
        text-align: center;
        padding: 30px;
        font-style: italic;
    }

    .template-error {
        color: #e63946;
        text-align: center;
        padding: 30px;
        border: 1px dashed #e63946;
        background-color: #fff5f5;
        border-radius: 4px;
    }

    /* Update the buttons container */
    .buttons-container {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
    }

    .right-buttons {
        display: flex;
        gap: 10px;
    }

    .save-template-btn {
        background-color: white;
        color: var(--primary);
        border: 1px solid var(--primary);
        padding: 12px 30px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
    }

    .save-template-btn:hover {
        background-color: var(--secondary);
    }

    /* Modal styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        overflow: auto;
    }

    .modal-content {
        background-color: white;
        margin: 10% auto;
        width: 100%;
        max-width: 500px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        animation: modalFadeIn 0.3s;
    }

    @keyframes modalFadeIn {
        from {
            opacity: 0;
            transform: translateY(-30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-header {
        padding: 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
    }

    .close-modal {
        font-size: 24px;
        cursor: pointer;
        color: #666;
    }

    .close-modal:hover {
        color: #333;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-footer {
        padding: 20px;
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    /* Modal buttons */
    .cancel-btn, .confirm-btn {
        padding: 10px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .cancel-btn {
        background-color: white;
        color: #666;
        border: 1px solid var(--border-color);
    }

    .cancel-btn:hover {
        background-color: #f5f5f5;
    }

    .confirm-btn {
        background-color: var(--primary);
        color: white;
        border: none;
    }

    .confirm-btn:hover {
        background-color: var(--primary-hover);
    }

    /* Toast notification */
    .toast {
        position: fixed;
        bottom: 30px;
        right: 30px;
        background-color: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        padding: 16px;
        display: flex;
        align-items: center;
        min-width: 300px;
        max-width: 400px;
        z-index: 1000;
        transform: translateY(150%);
        transition: transform 0.3s ease-out;
    }

    .toast.show {
        transform: translateY(0);
    }

    .toast-icon {
        margin-right: 12px;
        font-size: 20px;
    }

    .success-icon {
        color: #28a745;
    }

    .error-icon {
        color: #dc3545;
        display: none;
    }

    .toast.error .success-icon {
        display: none;
    }

    .toast.error .error-icon {
        display: block;
    }

    .toast-message {
        flex: 1;
        font-size: 14px;
    }

    .toast-close {
        cursor: pointer;
        color: #999;
        font-size: 18px;
        padding-left: 10px;
    }

    .toast-close:hover {
        color: #666;
    }
    /* Update Template Button */
    .update-template-btn {
        background-color: #28a745; /* Green color for update */
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
    }

    .update-template-btn:hover {
        background-color: #218838; /* Darker green on hover */
    }

    .update-template-btn:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
        opacity: 0.7;
    }

    /* AI Generation Container */
    .ai-generation-container {
        background-color: white;
        border-radius: 8px;
        padding: 30px;
        box-shadow: var(--shadow);
        margin-bottom: 30px;
        border: 2px dashed var(--primary);
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    }

    .ai-hint {
        color: #666;
        margin-bottom: 20px;
        font-size: 15px;
    }

    .ai-generate-btn {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-hover) 100%);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        gap: 10px;
        box-shadow: 0 4px 15px rgba(74, 108, 247, 0.3);
    }

    .ai-generate-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(74, 108, 247, 0.4);
    }

    .ai-generate-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .ai-generate-btn i {
        font-size: 18px;
    }

    /* Loading overlay for AI generation */
    .ai-loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1001;
    }

    .ai-loading-content {
        background-color: white;
        padding: 40px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        max-width: 400px;
    }

    .ai-loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .ai-loading-text {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
    }

    .ai-loading-subtext {
        font-size: 14px;
        color: #666;
    }
</style>

<!-- Include Font Awesome for the toolbar icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<script>
    let currentSelectedTemplate = null;
    document.addEventListener('DOMContentLoaded', function() {
        // Load job summary from sessionStorage
        loadJobSummary();

        // Initialize the rich text editor
        initRichTextEditor();
        initGlobalToast();
        initTemplateSelection();
        initSaveTemplateFeature();
        initUpdateTemplateFeature();

        // Set up editor content area with proper defaults
        setupEditorDefaults();

        // Event listeners for buttons
        document.getElementById('back-btn').addEventListener('click', function() {
            window.history.back();
        });

        document.getElementById('save-btn').addEventListener('click', function() {
            saveJobDescription();
        });

        document.getElementById('ai-generate-btn').addEventListener('click', function() {
            generateJobDescriptionWithAI();
        });

        // Character counter
        const editor = document.getElementById('editor');
        const charCount = document.getElementById('char-count');
        const charCounter = document.querySelector('.character-counter');

        editor.addEventListener('input', function() {
            // Count characters
            const text = editor.textContent || '';
            const count = text.length;
            charCount.textContent = count;

            // Add visual indicator if exceeding limit
            if (count > 10000) {
                charCounter.classList.add('limit-exceeded');
            } else {
                charCounter.classList.remove('limit-exceeded');
            }
        });

        // Function to set up editor defaults and ensure proper behavior
        function setupEditorDefaults() {
            const editor = document.getElementById('editor');

            // Ensure editor always has a valid block element to start with
            // This helps with consistent formatting behavior
            if (!editor.innerHTML.trim()) {
                editor.innerHTML = '<p><br></p>';
            }

            // Fix for Firefox and Safari to ensure proper paragraph handling
            editor.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    // Get current selection
                    const selection = window.getSelection();
                    const range = selection.getRangeAt(0);
                    const startNode = range.startContainer;

                    // Check if we're at the root level with no block element
                    if (startNode === editor && editor.childNodes.length === 0) {
                        // Prevent default behavior
                        e.preventDefault();

                        // Insert a proper paragraph
                        const p = document.createElement('p');
                        p.innerHTML = '<br>';
                        editor.appendChild(p);

                        // Place cursor in the new paragraph
                        range.selectNodeContents(p);
                        range.collapse(true);
                        selection.removeAllRanges();
                        selection.addRange(range);
                    }
                }
            });
        }


        const emojiButton = document.querySelector('.emoji-button');
        const emojiPicker = document.getElementById('emoji-picker');

        emojiButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            emojiPicker.style.display = emojiPicker.style.display === 'block' ? 'none' : 'block';
        });

        // Close emoji picker when clicking outside
        document.addEventListener('click', function(e) {
            if (!emojiButton.contains(e.target) && !emojiPicker.contains(e.target)) {
                emojiPicker.style.display = 'none';
            }
        });

        // Add emojis to the editor
        document.querySelectorAll('.emoji').forEach(function(emoji) {
            emoji.addEventListener('click', function(e) {
                e.preventDefault();
                const emojiChar = this.getAttribute('data-emoji');

                // Get current selection
                const selection = window.getSelection();
                const range = selection.getRangeAt(0);

                // Insert emoji at cursor position
                const textNode = document.createTextNode(emojiChar);
                range.deleteContents();
                range.insertNode(textNode);

                // Move cursor after the inserted emoji
                range.setStartAfter(textNode);
                range.setEndAfter(textNode);
                selection.removeAllRanges();
                selection.addRange(range);

                // Hide emoji picker and focus editor
                emojiPicker.style.display = 'none';
                editor.focus();

                // Update character count
                updateCharCount();
            });
        });

        document.getElementById('btn-bullet-list').addEventListener('click', function() {
            document.execCommand('insertUnorderedList', false, null);
            editor.focus();
        });

        document.getElementById('btn-number-list').addEventListener('click', function() {
            document.execCommand('insertOrderedList', false, null);
            editor.focus();
        });

        // Handle tab key for nested lists
        editor.addEventListener('keydown', function(e) {
            // Check if Tab key is pressed and we're in a list
            if (e.key === 'Tab') {
                const selection = window.getSelection();
                const range = selection.getRangeAt(0);

                // Get the current list item if there is one
                let listItem = getParentListItem(range.startContainer);

                if (listItem) {
                    e.preventDefault(); // Prevent default tab behavior

                    if (e.shiftKey) {
                        // Outdent on Shift+Tab
                        outdentListItem(listItem);
                    } else {
                        // Indent on Tab
                        indentListItem(listItem);
                    }
                }
            }
        });

        // Font size
        document.getElementById('font-size').addEventListener('change', function() {
            document.execCommand('fontSize', false, this.value);
            editor.focus();
        });
    });

    // Function to load job summary from sessionStorage
    function loadJobSummary() {
        const summaryContainer = document.getElementById('job-summary');
        const jobData = JSON.parse(sessionStorage.getItem('jobFormData'));

        if (!jobData) {
            summaryContainer.innerHTML = `
                <div class="summary-item" style="grid-column: 1 / -1;">
                    <p>{% trans "No job information found. Please go back and fill out the job details form." %}</p>
                </div>
            `;
            return;
        }

        // Clear loading message
        summaryContainer.innerHTML = '';

        // Create summary items for each field
        const summaryHTML = `
            <div class="summary-item" style="grid-column: span 2;">
                <div class="summary-label">{% trans "Role Title" %}</div>
                <div class="summary-value">${jobData.roleTitle}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">{% trans "Office Location" %}</div>
                <div class="summary-value">${jobData.officeLocation}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">{% trans "Work Schedule" %}</div>
                <div class="summary-value">${formatSchedule(jobData.workSchedule)}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">{% trans "Office Schedule" %}</div>
                <div class="summary-value">${formatSchedule(jobData.officeSchedule)}</div>
            </div>

            <div class="summary-item">
                <div class="summary-label">{% trans "Salary Details" %}</div>
                <div class="summary-value">From ${jobData.salaryMin} to ${jobData.salaryMax} ${jobData.salaryCurrency}</div>
            </div>

            <div class="summary-item" style="grid-column: span 2;">
                <div class="summary-label">{% trans "Benefits & Highlights" %}</div>
                <div class="summary-skills">
                    ${jobData.benefits && jobData.benefits.length > 0
                        ? jobData.benefits.map(benefit => `<div class="benefit-tag">${benefit}</div>`).join('')
                        : '<span>No benefits specified</span>'
                    }
                </div>
            </div>

            <div class="summary-item" style="grid-column: 1 / -1;">
                <div class="summary-label">{% trans "Skills" %}</div>
                <div class="summary-skills">
                    ${jobData.skills && jobData.skills.length > 0
                        ? jobData.skills.map(skill => `<div class="skill-tag">${skill}</div>`).join('')
                        : '<span>No skills specified</span>'
                    }
                </div>
            </div>

        `;

        summaryContainer.innerHTML = summaryHTML;
    }

    // Format schedule values for display
    function formatSchedule(value) {
        if (!value) return 'Not specified';

        // Convert kebab-case to Title Case with spaces
        return value
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    // Initialize the rich text editor
    function initRichTextEditor() {
        const buttons = document.querySelectorAll('.editor-toolbar button[data-command]');
        const editor = document.getElementById('editor');

        // Function to update active state of formatting buttons based on current selection
        function updateActiveButtons() {
            // Check for active formatting
            buttons.forEach(button => {
                const command = button.dataset.command;
                const value = button.dataset.value || '';

                // Handle different command types
                if (command === 'formatBlock') {
                    // For heading buttons, check if current selection has that heading
                    const formatValue = document.queryCommandValue('formatBlock');
                    if (formatValue.toLowerCase() === value.toLowerCase()) {
                        button.classList.add('active');
                    } else {
                        button.classList.remove('active');
                    }
                } else if (['bold', 'italic', 'underline', 'justifyLeft', 'justifyCenter', 'justifyRight'].includes(command)) {
                    // For other formatting buttons, check if command state is active
                    if (document.queryCommandState(command)) {
                        button.classList.add('active');
                    } else {
                        button.classList.remove('active');
                    }
                }
            });
        }

        // Add selection change event to update button states
        editor.addEventListener('mouseup', updateActiveButtons);
        editor.addEventListener('keyup', updateActiveButtons);

        buttons.forEach(button => {
            button.addEventListener('click', function() {
                const command = this.dataset.command;
                const value = this.dataset.value || '';

                if (command === 'createLink') {
                    const url = prompt('Enter the URL:');
                    if (url) document.execCommand(command, false, url);
                } else if (command === 'formatBlock') {
                    // Special handling for formatBlock to ensure cross-browser compatibility

                    // Check if we're already using this format
                    const currentFormat = document.queryCommandValue('formatBlock');

                    if (currentFormat.toLowerCase() === value.toLowerCase()) {
                        // If already using this format, switch back to paragraph
                        try {
                            document.execCommand('formatBlock', false, 'P');
                        } catch (e) {
                            document.execCommand('formatBlock', false, '<P>');
                        }
                    } else {
                        // Apply the new format
                        try {
                            // Modern browsers
                            document.execCommand('formatBlock', false, value);
                        } catch (e) {
                            // Fallback for older browsers
                            document.execCommand('formatBlock', false, '<' + value + '>');
                        }
                    }

                    // Ensure the heading is properly styled
                    setTimeout(() => {
                        const selection = window.getSelection();
                        if (selection.rangeCount > 0) {
                            const range = selection.getRangeAt(0);
                            const parentElement = range.commonAncestorContainer.parentElement;

                            // If we're in a heading, ensure it has proper styling
                            if (parentElement && /^H[1-6]$/i.test(parentElement.tagName)) {
                                parentElement.style.margin = '';
                                parentElement.style.padding = '';
                            }
                        }
                    }, 0);
                } else {
                    document.execCommand(command, false, value);
                }

                // Update active states after command execution
                updateActiveButtons();

                editor.focus();
                updateCharCount();
            });
        });

        // Initialize button states
        updateActiveButtons();
    }

    // Update character count
    function updateCharCount() {
        const editor = document.getElementById('editor');
        const charCount = document.getElementById('char-count');
        const charCounter = document.querySelector('.character-counter');

        const text = editor.textContent || '';
        const count = text.length;
        charCount.textContent = count;

        if (count > 10000) {
            charCounter.classList.add('limit-exceeded');
        } else {
            charCounter.classList.remove('limit-exceeded');
        }
    }

    // Get parent list item element
    function getParentListItem(node) {
        while (node && node !== editor) {
            if (node.nodeName === 'LI') {
                return node;
            }
            node = node.parentNode;
        }
        return null;
    }

    // Indent a list item to create a nested list
    function indentListItem(listItem) {
        const parentList = listItem.parentNode;
        const prevItem = listItem.previousElementSibling;

        // Can only indent if there's a previous list item
        if (!prevItem) return;

        // Check if the previous list item already has a nested list
        let subList = Array.from(prevItem.children).find(el =>
            el.nodeName === 'UL' || el.nodeName === 'OL'
        );

        // If no sublist exists in the previous item, create one
        // matching the parent list type
        if (!subList) {
            subList = document.createElement(parentList.nodeName);
            prevItem.appendChild(subList);
        }

        // Move the current list item to the sublist
        subList.appendChild(listItem);

        // Place cursor at the end of the list item text
        placeCaretAtEnd(listItem);
    }

    // Outdent a list item
    function outdentListItem(listItem) {
        const parentList = listItem.parentNode;

        // Only outdent if we're in a nested list
        if (parentList.parentNode.nodeName !== 'LI') return;

        // Get the parent list item
        const parentItem = parentList.parentNode;
        const grandparentList = parentItem.parentNode;

        // Move the list item after the parent list item in the grandparent list
        grandparentList.insertBefore(listItem, parentItem.nextSibling);

        // If this was the only item in the sublist, remove the now-empty sublist
        if (parentList.children.length === 0) {
            parentItem.removeChild(parentList);
        }

        // Place cursor at the end of the list item text
        placeCaretAtEnd(listItem);
    }

    // Helper to place cursor at the end of an element
    function placeCaretAtEnd(element) {
        // For text nodes, place at the end of the text
        let lastNode = element.lastChild;

        // Navigate to find the last text node or element with content
        while (lastNode && lastNode.nodeType === Node.ELEMENT_NODE && lastNode.lastChild) {
            if (lastNode.nodeName === 'UL' || lastNode.nodeName === 'OL') {
                break;
            }
            lastNode = lastNode.lastChild;
        }

        const range = document.createRange();
        const selection = window.getSelection();

        // Set the range to the end of the content
        if (lastNode.nodeType === Node.TEXT_NODE) {
            range.setStart(lastNode, lastNode.length);
            range.collapse(true);
        } else {
            // If it's not a text node, append to the element
            range.setStartAfter(lastNode);
            range.collapse(true);
        }

        // Apply the range
        selection.removeAllRanges();
        selection.addRange(range);
    }

    // Save job description to sessionStorage
    function saveJobDescription() {
        const editor = document.getElementById('editor');
        const description = editor.innerHTML;
        const charCount = editor.textContent.length;

        if (charCount > 10000) {
            alert('Your description exceeds the character limit of 10000. Please shorten your text.');
            return;
        }

        // Save to sessionStorage
        sessionStorage.setItem('jobDescription', description);

        showToast('Job description saved successfully!', 'success');

        // Navigate to the next page (uncomment and specify the URL)
        // window.location.href = 'review-job-page.html';
        window.location.href = "{% url 'preview' %}";
    }

    function initGlobalToast() {
        const toast = document.getElementById('toast');
        const toastMessage = document.querySelector('.toast-message');
        const toastClose = document.querySelector('.toast-close');

        // Make showToast globally available
        window.showToast = function(message, type = 'success') {
            toastMessage.textContent = message;

            // Reset classes first
            toast.className = 'toast';

            // Then add the right classes
            if (type === 'error') {
                toast.classList.add('error', 'show');
            } else {
                toast.classList.add('show');
            }

            // Auto hide after 5 seconds
            setTimeout(() => {
                toast.className = 'toast';
            }, 5000);
        };

        // Close toast on X click
        toastClose.addEventListener('click', function() {
            toast.className = 'toast';
        });
    }


    function initUpdateTemplateFeature() {
        const updateTemplateBtn = document.getElementById('update-template-btn');
        const editor = document.getElementById('editor');

        // Handle update template button click
        updateTemplateBtn.addEventListener('click', function() {
            // If no template is selected or button is disabled, do nothing
            if (!currentSelectedTemplate || updateTemplateBtn.disabled) {
                return;
            }

            // Get updated content
            const updatedContent = editor.innerHTML;

            // Show updating state
            const originalButtonText = updateTemplateBtn.textContent;
            updateTemplateBtn.textContent = 'Updating...';
            updateTemplateBtn.disabled = true;

            // Send update to API
            fetch('/api/save-template/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify({
                    title: currentSelectedTemplate.title,
                    description: updatedContent
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Update current template data
                    currentSelectedTemplate.originalContent = updatedContent;

                    // Show success message
                    showToast('Template updated successfully!', 'success');

                    // Refresh templates list
                    window.loadTemplates();
                } else {
                    throw new Error(data.message || 'Failed to update template');
                }
            })
            .catch(error => {
                // Show error message
                showToast('Error: ' + (error.message || 'Failed to update template'), 'error');
                console.error('Update template error:', error);
            })
            .finally(() => {
                // Reset button state
                updateTemplateBtn.textContent = originalButtonText;
                updateTemplateBtn.disabled = false;
            });
        });

        // Helper function to get CSRF token
        function getCSRFToken() {
            const name = 'csrftoken';
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    }

    // Template functionality
    function initTemplateSelection() {
        const templateOptions = document.querySelectorAll('.template-option');
        const templateSelector = document.querySelector('.template-selector');
        const templateSelect = document.getElementById('template-select');
        const editor = document.getElementById('editor');

        // Store all template data
        let templatesData = [];

        // Make loadTemplates function available globally
        window.loadTemplates = loadTemplates;

        // Load available templates from API
        function loadTemplates() {
            fetch('/api/templates/')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to fetch templates');
                    }
                    return response.json();
                })
                .then(data => {
                    // Store the complete templates data
                    templatesData = data.templates || [];

                    // Clear existing options except the first one
                    templateSelect.innerHTML = '<option value="">Select a template</option>';

                    // Add templates to dropdown
                    templatesData.forEach((template, index) => {
                        const option = document.createElement('option');
                        option.value = index; // Use array index as value
                        option.textContent = template.title;
                        templateSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error loading templates:', error);
                    templateSelect.innerHTML = '<option value="">Error loading templates</option>';
                });
        }

        // Initialize by loading templates
        loadTemplates();

        // Add event listener to detect changes in the editor
        editor.addEventListener('input', function() {
            // If a template is selected, enable/disable the update button based on content changes
            if (currentSelectedTemplate) {
                const updateTemplateBtn = document.getElementById('update-template-btn');
                const currentContent = editor.innerHTML;

                // Enable button only if content has changed
                updateTemplateBtn.disabled = (currentContent === currentSelectedTemplate.originalContent);
            }

            // Also handle character count updates
            updateCharCount();
        });

        // Handle option selection
        templateOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Update active state
                templateOptions.forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');

                const selectedOption = this.getAttribute('data-option');
                const updateTemplateBtn = document.getElementById('update-template-btn');

                if (selectedOption === 'template') {
                    // Show template selector
                    templateSelector.style.display = 'block';

                    // Clear editor until a template is selected
                    if (!templateSelect.value) {
                        editor.innerHTML = '';
                        // No template selected yet, hide update button
                        updateTemplateBtn.style.display = 'none';
                        // Reset current template
                        currentSelectedTemplate = null;
                    }
                } else {
                    // Creating a new description
                    // Hide template selector and clear editor
                    templateSelector.style.display = 'none';
                    editor.innerHTML = '';

                    // Hide update button and reset current template
                    updateTemplateBtn.style.display = 'none';
                    currentSelectedTemplate = null;
                }
            });
        });

        // Handle template selection
        templateSelect.addEventListener('change', function() {
            const selectedIndex = this.value;
            const updateTemplateBtn = document.getElementById('update-template-btn');

            // Reset current template selection
            currentSelectedTemplate = null;
            updateTemplateBtn.style.display = 'none';

            if (selectedIndex !== '') {
                // Show loading indicator in editor
                editor.innerHTML = '<div class="loading-template">Loading template content...</div>';

                try {
                    // Get template from our stored data using the index
                    const selectedTemplate = templatesData[parseInt(selectedIndex)];

                    if (selectedTemplate && selectedTemplate.description) {
                        // Set the editor content to the template description
                        // Use a timeout to ensure proper rendering
                        setTimeout(() => {
                            // Set the editor content to the template description
                            editor.innerHTML = selectedTemplate.description;


                            // Ensure proper formatting of headings and other elements
                            normalizeEditorContent();

                            // Force editor to refresh (sometimes needed for proper rendering)
                            editor.blur();
                            editor.focus();

                            // Track current template selection
                            currentSelectedTemplate = {
                                index: selectedIndex,
                                title: selectedTemplate.title,
                                originalContent: selectedTemplate.description
                            };

                            // Show update template button
                            updateTemplateBtn.style.display = 'block';
                            updateTemplateBtn.disabled = true; // Initially disabled until changes are made


                            // Update character count
                            updateCharCount();
                        }, 50);
                    } else {
                        throw new Error('Template data not found');
                    }

                    // Function to ensure proper formatting of editor content
                    function normalizeEditorContent() {
                        // Fix headings that might be improperly formatted
                        ['h1', 'h2', 'h3'].forEach(tag => {
                            const elements = editor.querySelectorAll(tag);
                            elements.forEach(el => {
                                // Ensure proper styling
                                el.style.margin = '';
                                el.style.padding = '';

                                // If heading is empty, add a break to maintain height
                                if (!el.textContent.trim()) {
                                    el.innerHTML = '<br>';
                                }
                            });
                        });

                        // Ensure paragraphs are properly formatted
                        const paragraphs = editor.querySelectorAll('p');
                        paragraphs.forEach(p => {
                            p.style.margin = '';

                            // If paragraph is empty, add a break to maintain height
                            if (!p.textContent.trim() && !p.querySelector('br')) {
                                p.innerHTML = '<br>';
                            }
                        });
                    }
                } catch (error) {
                    console.error('Error setting template content:', error);
                    editor.innerHTML = '<div class="template-error">Error loading template content. Please try again.</div>';
                }

                // Update character count
                updateCharCount();
            } else {
                // Clear editor if no template selected
                editor.innerHTML = '';
                updateCharCount();
            }
        });
    }

    function initSaveTemplateFeature() {
        const saveTemplateBtn = document.getElementById('save-template-btn');
        const saveTemplateModal = document.getElementById('save-template-modal');
        const closeModal = document.querySelector('.close-modal');
        const cancelSaveTemplate = document.getElementById('cancel-save-template');
        const confirmSaveTemplate = document.getElementById('confirm-save-template');
        const templateTitleInput = document.getElementById('template-title');
        const templateTitleError = document.getElementById('template-title-error');
        const editor = document.getElementById('editor');
        const toast = document.getElementById('toast');
        const toastMessage = document.querySelector('.toast-message');
        const toastClose = document.querySelector('.toast-close');

        // Show modal when save template button is clicked
        saveTemplateBtn.addEventListener('click', function() {
            // Only allow saving if editor has content
            if (!editor.textContent.trim()) {
                window.showToast('Please add some content to your job description before saving as template.', 'error');
                return;
            }

            // Clear previous input and errors
            templateTitleInput.value = '';
            templateTitleError.textContent = '';
            saveTemplateModal.style.display = 'block';
            templateTitleInput.focus();
        });

        // Close modal on X click
        closeModal.addEventListener('click', function() {
            saveTemplateModal.style.display = 'none';
        });

        // Close modal on cancel button click
        cancelSaveTemplate.addEventListener('click', function() {
            saveTemplateModal.style.display = 'none';
        });

        // Close modal on clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === saveTemplateModal) {
                saveTemplateModal.style.display = 'none';
            }
        });

        // Handle save template form submission
        confirmSaveTemplate.addEventListener('click', saveTemplate);

        // Also allow Enter key to submit
        templateTitleInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                saveTemplate();
            }
        });

        // Save template function
        function saveTemplate() {
            // Validate title
            const title = templateTitleInput.value.trim();
            if (!title) {
                templateTitleError.textContent = 'Please enter a title for your template';
                return;
            }

            // Get editor content
            const description = editor.innerHTML;

            // Show saving state
            confirmSaveTemplate.textContent = 'Saving...';
            confirmSaveTemplate.disabled = true;

            // Send to API
            fetch('/api/save-template/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken() // Function to get CSRF token
                },
                body: JSON.stringify({
                    title: title,
                    description: description
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Close modal
                    saveTemplateModal.style.display = 'none';

                    // Show success message
                    showToast('Template saved successfully!', 'success');

                    // Reload available templates
                    window.loadTemplates(); // Using the global loadTemplates function
                } else {
                    throw new Error(data.message || 'Failed to save template');
                }
            })
            .catch(error => {
                // Show error message
                templateTitleError.textContent = error.message || 'An error occurred while saving the template';
                console.error('Save template error:', error);
            })
            .finally(() => {
                // Reset button state
                confirmSaveTemplate.textContent = 'Save Template';
                confirmSaveTemplate.disabled = false;
            });
        }

        // Function to get CSRF token from cookies
        function getCSRFToken() {
            const name = 'csrftoken';
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

    }
    function generateJobDescriptionWithAI() {
        // Get job data from sessionStorage
        const jobData = JSON.parse(sessionStorage.getItem('jobFormData'));

        if (!jobData) {
            showToast('No job information found. Please go back and fill out the job details form.', 'error');
            return;
        }

        // Show loading overlay
        showAILoadingOverlay();

        // Disable the AI generate button
        const aiGenerateBtn = document.getElementById('ai-generate-btn');
        const originalText = aiGenerateBtn.innerHTML;
        aiGenerateBtn.disabled = true;
        aiGenerateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';

        // Send request to AI endpoint
        fetch('/ai/generate_job_description/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify(jobData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to generate job description');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Hide loading overlay
                hideAILoadingOverlay();

                // Set the generated job description in the editor
                const editor = document.getElementById('editor');
                setTimeout(() => {
                    editor.innerHTML = data.job_description;

                    // Force editor to refresh (same as templates)
                    editor.blur();
                    editor.focus();
                    // Update character count
                    updateCharCount();
                }, 50);



                // Reset template selection to "new" since we're using AI-generated content
                document.querySelector('.template-option[data-option="new"]').click();

                // Hide update template button since this is new content
                document.getElementById('update-template-btn').style.display = 'none';
                currentSelectedTemplate = null;

                // Show success message
                showToast('AI job description generated successfully!', 'success');

                // Scroll to editor
                editor.scrollIntoView({ behavior: 'smooth', block: 'center' });
                editor.focus();

            } else {
                throw new Error(data.message || 'Failed to generate job description');
            }
        })
        .catch(error => {
            console.error('AI generation error:', error);
            hideAILoadingOverlay();
            showToast('Error: ' + (error.message || 'Failed to generate job description with AI'), 'error');
        })
        .finally(() => {
            // Re-enable the AI generate button
            aiGenerateBtn.disabled = false;
            aiGenerateBtn.innerHTML = originalText;
        });
    }

    function showAILoadingOverlay() {
        // Create loading overlay if it doesn't exist
        let overlay = document.getElementById('ai-loading-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'ai-loading-overlay';
            overlay.className = 'ai-loading-overlay';
            overlay.innerHTML = `
                <div class="ai-loading-content">
                    <div class="ai-loading-spinner"></div>
                    <div class="ai-loading-text">AI is crafting your job description</div>
                    <div class="ai-loading-subtext">This may take a few moments...</div>
                </div>
            `;
            document.body.appendChild(overlay);
        }
        overlay.style.display = 'flex';
    }

    function hideAILoadingOverlay() {
        const overlay = document.getElementById('ai-loading-overlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }

    // Helper function to get CSRF token (if not already present)
    function getCSRFToken() {
        const name = 'csrftoken';
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
{% endblock %}