{% extends 'main.html' %}
{% load static %}
{% load i18n %}

{% block content %}
{% csrf_token %}
<div class="workloupe-platform-container">
    <div class="container">
        <div class="row justify-content-center">
            <!-- Single Column - Company Information Form -->
            <div class="col-lg-10 col-xl-8">
                <div class="form-header text-center">
                    <h2><i class="fas fa-globe me-2"></i>{% trans "Workloupe Platform Setup" %}</h2>
                    <p class="text-muted">{% trans "Create and manage your company profile on workloupe.com" %}</p>
                    {% if employer %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% trans "Update your existing profile information below. All changes will be saved automatically." %}
                        </div>
                    {% endif %}
                </div>

                <form id="workloupePlatformForm" class="platform-form">
                    <!-- Basic Company Information -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-building me-2"></i>
                            {% trans "Company Information" %}
                        </h5>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employerName" class="form-label">{% trans "Company Name" %} *</label>
                                    <input type="text" class="form-control" id="employerName" name="employer_name" required>
                                    <div class="invalid-feedback">{% trans "Company name is required" %}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employerEmail" class="form-label">{% trans "Company Email" %} *</label>
                                    <input type="email" class="form-control" id="employerEmail" name="employer_email" required>
                                    <div class="invalid-feedback">{% trans "Please enter a valid email address" %}</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employerPhone" class="form-label">{% trans "Phone Number" %}</label>
                                    <input type="tel" class="form-control" id="employerPhone" name="employer_phone">
                                    <small class="form-text text-muted">{% trans "Include country code (e.g., ******-123-4567)" %}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employerWebsite" class="form-label">{% trans "Website" %} *</label>
                                    <input type="url" class="form-control" id="employerWebsite" name="employer_website" required>
                                    <div class="invalid-feedback">{% trans "Please enter a valid website URL" %}</div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="employerAddress" class="form-label">{% trans "Company Address" %}</label>
                            <input type="text" class="form-control" id="employerAddress" name="employer_address" placeholder="{% trans 'Full company address including city, state, country' %}">
                        </div>

                        <div class="form-group mb-3">
                            <label for="officeLocations" class="form-label">{% trans "Office Locations" %}</label>
                            <input type="text" class="form-control" id="officeLocations" name="office_locations" placeholder="{% trans 'Separate multiple locations with | (e.g., New York | London | Remote)' %}">
                            <small class="form-text text-muted">{% trans "Use | to separate multiple office locations" %}</small>
                        </div>

                        <div class="form-group mb-3">
                            <label for="employerDescription" class="form-label">{% trans "Company Description" %} *</label>
                            <textarea class="form-control" id="employerDescription" name="employer_description" rows="5" required placeholder="{% trans 'Describe your company, mission, values, and what makes it special. This will be prominently displayed on your profile.' %}"></textarea>
                            <div class="invalid-feedback">{% trans "Company description is required" %}</div>
                            <small class="form-text text-muted">{% trans "Minimum 50 characters recommended for better visibility" %}</small>
                        </div>
                    </div>

                    <!-- Company Details -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-info-circle me-2"></i>
                            {% trans "Company Details" %}
                        </h5>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employerIndustry" class="form-label">{% trans "Industry" %}</label>
                                    <select class="form-select" id="employerIndustry" name="employer_industry">
                                        <option value="">{% trans "Select Industry" %}</option>
                                        <option value="Technology">{% trans "Technology" %}</option>
                                        <option value="Healthcare">{% trans "Healthcare" %}</option>
                                        <option value="Finance">{% trans "Finance" %}</option>
                                        <option value="Education">{% trans "Education" %}</option>
                                        <option value="Manufacturing">{% trans "Manufacturing" %}</option>
                                        <option value="Retail">{% trans "Retail" %}</option>
                                        <option value="Consulting">{% trans "Consulting" %}</option>
                                        <option value="Marketing & Advertising">{% trans "Marketing & Advertising" %}</option>
                                        <option value="Real Estate">{% trans "Real Estate" %}</option>
                                        <option value="Non-profit">{% trans "Non-profit" %}</option>
                                        <option value="Other">{% trans "Other" %}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employerHeadcount" class="form-label">{% trans "Company Size" %} *</label>
                                    <input type="number" class="form-control" id="employerHeadcount" name="employer_headcount" min="1" max="999999" placeholder="{% trans 'Number of employees' %}" required>
                                    <div class="invalid-feedback">{% trans "Please enter a valid number of employees" %}</div>
                                    <small class="form-text text-muted">{% trans "Enter the total number of employees in your company" %}</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label">{% trans "Social Media Links" %} <small class="text-muted">({% trans "Optional" %})</small></label>
                            <div class="social-links-container">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label small"><i class="fab fa-linkedin me-2 text-primary"></i> LinkedIn</label>
                                        <input type="url" class="form-control social-input" id="linkedinUrl" name="linkedin_url" placeholder="https://linkedin.com/company/yourcompany">
                                        <div class="invalid-feedback">{% trans "Please enter a valid LinkedIn URL" %}</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label small"><i class="fab fa-instagram me-2 text-danger"></i> Instagram</label>
                                        <input type="url" class="form-control social-input" id="instagramUrl" name="instagram_url" placeholder="https://instagram.com/yourcompany">
                                        <div class="invalid-feedback">{% trans "Please enter a valid Instagram URL" %}</div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label small"><i class="fab fa-twitter me-2 text-info"></i> Twitter (X)</label>
                                        <input type="url" class="form-control social-input" id="twitterUrl" name="twitter_url" placeholder="https://twitter.com/yourcompany">
                                        <div class="invalid-feedback">{% trans "Please enter a valid Twitter URL" %}</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label small"><i class="fab fa-github me-2 text-dark"></i> Github</label>
                                        <input type="url" class="form-control social-input" id="githubUrl" name="github_url" placeholder="https://github.com/yourcompany">
                                        <div class="invalid-feedback">{% trans "Please enter a valid Github URL" %}</div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label small"><i class="fab fa-facebook me-2 text-primary"></i> Facebook</label>
                                        <input type="url" class="form-control social-input" id="facebookUrl" name="facebook_url" placeholder="https://facebook.com/yourcompany">
                                        <div class="invalid-feedback">{% trans "Please enter a valid Facebook URL" %}</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label small"><i class="fas fa-building me-2 text-success"></i> Glassdoor</label>
                                        <input type="url" class="form-control social-input" id="glassdoorUrl" name="glassdoor_url" placeholder="https://glassdoor.com/company/yourcompany">
                                        <div class="invalid-feedback">{% trans "Please enter a valid Glassdoor URL" %}</div>
                                    </div>
                                </div>
                            </div>
                            <small class="form-text text-muted">{% trans "Add your company's social media profiles to increase visibility. URLs will be validated automatically." %}</small>
                        </div>
                    </div>

                    <!-- Logo and Banner Upload -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-image me-2"></i>
                            {% trans "Branding Assets" %} - {% trans "(Publicly Visible)" %}
                        </h5>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="logoUpload" class="form-label">{% trans "Company Logo" %}</label>
                                    <input type="file" class="form-control" id="logoUpload" name="logo" accept="image/jpeg,image/jpg,image/png,image/webp">
                                    <small class="form-text text-muted">{% trans "Recommended: 300x300px, PNG or JPG. Max 3MB." %}</small>
                                    <div id="logoPreview" class="image-preview mt-2">
                                        {% if employer.employer_logo_url %}
                                            <img src="{{ employer.employer_logo_url }}" alt="Current Logo" class="current-image">
                                            <small class="text-muted d-block mt-1">{% trans "Current logo - upload new file to replace" %}</small>
                                        {% else %}
                                            <div class="upload-placeholder">
                                                <i class="fas fa-image fa-2x text-muted mb-2"></i>
                                                <p class="text-muted">{% trans "No logo uploaded" %}</p>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="bannerUpload" class="form-label">{% trans "Company Banner" %}</label>
                                    <input type="file" class="form-control" id="bannerUpload" name="banner" accept="image/jpeg,image/jpg,image/png,image/webp">
                                    <small class="form-text text-muted">{% trans "Recommended: 1200x400px, PNG or JPG. Max 3MB." %}</small>
                                    <div id="bannerPreview" class="image-preview mt-2">
                                        {% if employer.employer_banner_url %}
                                            <img src="{{ employer.employer_banner_url }}" alt="Current Banner" class="current-image">
                                            <small class="text-muted d-block mt-1">{% trans "Current banner - upload new file to replace" %}</small>
                                        {% else %}
                                            <div class="upload-placeholder">
                                                <i class="fas fa-image fa-2x text-muted mb-2"></i>
                                                <p class="text-muted">{% trans "No banner uploaded" %}</p>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>{% trans "Note:" %}</strong> {% trans "If you don't have online versions of your logo or banner, you can upload them here. They will be stored securely and made publicly accessible for your profile." %}
                        </div>
                    </div>

                    <!-- Company Gallery -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-images me-2"></i>
                            {% trans "Company Gallery" %}
                        </h5>

                        <div class="alert alert-warning mb-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>{% trans "Important:" %}</strong> {% trans "Photos will be public once you publish your profile. Maximum 50 photos allowed. We reserve the right to remove inappropriate content. Only upload professional, work-appropriate images." %}
                        </div>

                        <div id="existingGallery" class="existing-gallery mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-folder-open me-2"></i>
                                {% trans "Current Photos" %} (<span id="galleryCount">0</span>/50)
                            </h6>
                            <div id="existingGalleryGrid" class="gallery-preview">
                                <!-- Existing gallery images will be displayed here -->
                                <div id="galleryLoadingMessage" class="text-center p-4">
                                    <i class="fas fa-spinner fa-spin me-2"></i>
                                    {% trans "Loading existing photos..." %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="galleryUpload" class="form-label">
                                <i class="fas fa-cloud-upload-alt me-2"></i>
                                {% trans "Upload New Photos" %}
                            </label>
                            <input type="file" class="form-control" id="galleryUpload" name="gallery[]" accept="image/jpeg,image/jpg,image/png,image/webp" multiple>
                            <small class="form-text text-muted">{% trans "Select multiple photos (JPG, PNG, WebP). Max 5MB per photo. Showcase your company culture, office, team, events, etc." %}</small>
                        </div>

                        <div id="newGalleryPreview" class="gallery-preview">
                            <!-- New gallery images will be displayed here -->
                        </div>

                        <div id="uploadProgress" class="mt-3" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted mt-1 d-block">{% trans "Uploading photos..." %}</small>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions text-center">
                        <button type="button" class="btn btn-outline-secondary btn-lg me-3" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>
                            {% trans "Reset Form" %}
                        </button>
                        <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                            <i class="fas fa-save me-2"></i>
                            {% trans "Save & Publish Profile" %}
                        </button>
                    </div>

                    <div class="text-center mt-3">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            {% trans "Your data is secure and will only be used for your public company profile" %}
                        </small>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="successModalLabel">
                    <i class="fas fa-check-circle me-2"></i>
                    {% trans "Profile Published Successfully!" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-4">
                    <i class="fas fa-globe fa-4x text-success mb-3"></i>
                    <h4>{% trans "Your company is now live on Workloupe!" %}</h4>
                    <p class="text-muted">{% trans "Your company profile has been created and published on workloupe.com. Candidates can now discover your company and apply to your jobs." %}</p>
                </div>

                <div class="alert alert-info">
                    <strong>{% trans "Your Profile URL:" %}</strong><br>
                    <div class="input-group mt-2">
                        <input type="text" class="form-control" id="profileUrlInput" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyProfileUrl()">
                            <i class="fas fa-copy me-1"></i>{% trans "Copy" %}
                        </button>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-share-alt fa-2x text-primary mb-2"></i>
                                <h6>{% trans "Share Your Profile" %}</h6>
                                <p class="small text-muted">{% trans "Share this link with candidates and on social media" %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-edit fa-2x text-info mb-2"></i>
                                <h6>{% trans "Update Anytime" %}</h6>
                                <p class="small text-muted">{% trans "Return to this page to update your profile information" %}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                <a href="#" id="viewProfileBtn" class="btn btn-primary btn-lg" target="_blank">
                    <i class="fas fa-external-link-alt me-2"></i>
                    {% trans "View Live Profile" %}
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.workloupe-platform-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 0;
}

.form-header {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.form-header h2 {
    color: #343a40;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.platform-form {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.form-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-section:hover {
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transform: translateY(-1px);
}

.section-title {
    color: #343a40;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #dee2e6;
}

.form-actions {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid #dee2e6;
    margin-top: 2rem;
}

.image-preview {
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fafafa;
    transition: all 0.3s ease;
}

.image-preview:hover {
    border-color: #007bff;
    background: #f0f8ff;
}

.image-preview .current-image {
    max-width: 100%;
    max-height: 150px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.upload-placeholder {
    text-align: center;
}

.social-input {
    transition: all 0.3s ease;
}

.social-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.gallery-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.gallery-item {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    aspect-ratio: 1;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-item .remove-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-item .remove-btn:hover {
    background: rgba(220, 53, 69, 1);
    transform: scale(1.1);
}

.existing-gallery h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

.btn-outline-secondary {
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    transform: translateY(-1px);
}

.alert {
    border-radius: 8px;
    border: none;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

#galleryLoadingMessage {
    color: #6c757d;
    font-style: italic;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
}

.progress-bar {
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
    border-radius: 4px;
}

@media (max-width: 768px) {
    .workloupe-platform-container {
        padding: 1rem 0;
    }

    .form-header, .platform-form {
        padding: 1.5rem;
    }

    .form-section {
        padding: 1.5rem;
    }

    .form-actions .btn {
        width: 100%;
        margin-bottom: 1rem;
    }

    .gallery-preview {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 1rem;
    }
}
</style>

<script>
// Workloupe platform functionality
let platformData = {
    employerName: '{{ employer.employer_name|default:""|escapejs }}',
    employerEmail: '{{ employer.employer_email|default:""|escapejs }}',
    employerPhone: '{{ employer.employer_phone|default:""|escapejs }}',
    employerWebsite: '{{ employer.employer_website|default:""|escapejs }}',
    employerAddress: '{{ employer.employer_address|default:""|escapejs }}',
    officeLocations: '{{ employer.office_locations|default:""|escapejs }}',
    employerDescription: '{{ employer.employer_description|default:""|escapejs }}',
    employerIndustry: '{{ employer.employer_industry|default:""|escapejs }}',
    employerHeadcount: '{{ employer.employer_headcount|default:""|escapejs }}',
    logoUrl: {% if employer.employer_logo_url %}'{{ employer.employer_logo_url|escapejs }}'{% else %}null{% endif %},
    bannerUrl: {% if employer.employer_banner_url %}'{{ employer.employer_banner_url|escapejs }}'{% else %}null{% endif %},
    existingGalleryImages: [],
    newGalleryImages: [],
    socialLinks: {
        linkedin: '',
        instagram: '',
        twitter: '',
        github: '',
        facebook: '',
        glassdoor: ''
    }
};

const MAX_GALLERY_PHOTOS = 50;
const MAX_FILE_SIZE = 3 * 1024 * 1024; // 3MB
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
const employerId = '{{ employer.employer_id }}';

// Initialize the platform builder
document.addEventListener('DOMContentLoaded', function() {
    initializePlatformBuilder();
    loadExistingData();
    loadExistingGallery();
    setupFormValidation();
});

function loadExistingData() {
    // Load existing employer data into form fields
    document.getElementById('employerName').value = platformData.employerName;
    document.getElementById('employerEmail').value = platformData.employerEmail;
    document.getElementById('employerPhone').value = platformData.employerPhone;
    document.getElementById('employerWebsite').value = platformData.employerWebsite;
    document.getElementById('employerAddress').value = platformData.employerAddress;
    document.getElementById('officeLocations').value = platformData.officeLocations;
    document.getElementById('employerDescription').value = platformData.employerDescription;
    document.getElementById('employerIndustry').value = platformData.employerIndustry;
    document.getElementById('employerHeadcount').value = platformData.employerHeadcount;

    // Parse and load social media links
    try {
        const socialLinksStr = '{{ employer.employer_social_portals|default:""|escapejs }}';
        if (socialLinksStr) {
            const socialLinks = socialLinksStr.split(',');
            socialLinks.forEach(link => {
                const parts = link.trim().split(';');
                if (parts.length >= 2) {
                    const platform = parts[0].toLowerCase();
                    const url = parts[1];
                    const inputId = platform + 'Url';
                    const input = document.getElementById(inputId);
                    if (input) {
                        input.value = url;
                        platformData.socialLinks[platform] = url;
                    }
                }
            });
        }
    } catch (e) {
        console.log('No existing social data found');
    }
}

function initializePlatformBuilder() {
    // Add event listeners for all form controls
    const formInputs = [
        'employerName', 'employerEmail', 'employerPhone', 'employerWebsite',
        'employerAddress', 'officeLocations', 'employerDescription',
        'employerIndustry', 'employerHeadcount'
    ];

    formInputs.forEach(inputId => {
        const element = document.getElementById(inputId);
        if (element) {
            element.addEventListener('input', validateField);
            element.addEventListener('blur', validateField);
        }
    });

    // Social media input handlers
    const socialInputs = ['linkedin', 'instagram', 'twitter', 'github', 'facebook', 'glassdoor'];
    socialInputs.forEach(portal => {
        const element = document.getElementById(`${portal}Url`);
        if (element) {
            element.addEventListener('input', function(e) {
                validateSocialUrl(e, portal);
            });
            element.addEventListener('blur', function(e) {
                validateSocialUrl(e, portal);
            });
        }
    });

    // Print the outcome of social media inputs
    socialInputs.forEach(portal => {
        const element = document.getElementById(`${portal}Url`);
        if (element) {
            console.log(`[WORKLOUPE-DEBUG] ${portal}Url: ${element.value}`);
        }
    });



    // File upload handlers
    document.getElementById('logoUpload').addEventListener('change', handleLogoUpload);
    document.getElementById('bannerUpload').addEventListener('change', handleBannerUpload);
    document.getElementById('galleryUpload').addEventListener('change', handleGalleryUpload);

    // Form submission
    document.getElementById('workloupePlatformForm').addEventListener('submit', handleFormSubmission);
}

function setupFormValidation() {
    const form = document.getElementById('workloupePlatformForm');
    form.classList.add('needs-validation');
}

function validateField(event) {
    const field = event.target;
    const value = field.value.trim();

    // Remove existing validation classes
    field.classList.remove('is-valid', 'is-invalid');

    let isValid = true;

    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
    }

    // Email validation
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        isValid = emailRegex.test(value);
    }

    // URL validation
    if (field.type === 'url' && value) {
        try {
            new URL(value);
        } catch {
            isValid = false;
        }
    }

    // Number validation
    if (field.type === 'number' && value) {
        const num = parseInt(value);
        isValid = !isNaN(num) && num > 0;
    }

    // Description length validation
    if (field.id === 'employerDescription' && value) {
        isValid = value.length >= 50;
    }

    field.classList.add(isValid ? 'is-valid' : 'is-invalid');
    return isValid;
}

function validateSocialUrl(event) {
    const field = event.target;
    const value = field.value.trim();

    field.classList.remove('is-valid', 'is-invalid');

    if (!value) {
        return true; // Optional field
    }

    let isValid = false;
    const platform = field.id.replace('Url', '').toLowerCase();

    try {
        const url = new URL(value);
        const hostname = url.hostname.toLowerCase();

        switch (platform) {
            case 'linkedin':
                isValid = hostname.includes('linkedin.com');
                break;
            case 'instagram':
                isValid = hostname.includes('instagram.com');
                break;
            case 'twitter':
                isValid = hostname.includes('twitter.com') || hostname.includes('x.com');
                break;
            case 'github':
                isValid = hostname.includes('github.com');
                break;
            case 'facebook':
                isValid = hostname.includes('facebook.com');
                break;
            case 'glassdoor':
                isValid = hostname.includes('glassdoor.com');
                break;
        }
    } catch {
        isValid = false;
    }

    field.classList.add(isValid ? 'is-valid' : 'is-invalid');

    if (isValid) {
        platformData.socialLinks[platform] = value;
    }

    return isValid;
}

function handleLogoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        if (!validateFile(file, 3 * 1024 * 1024)) { // 3MB limit for logo
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('logoPreview').innerHTML =
                `<img src="${e.target.result}" alt="Logo Preview" class="current-image">
                 <small class="text-muted d-block mt-1">{% trans "New logo selected - will be uploaded on save" %}</small>`;
        };
        reader.readAsDataURL(file);
    }
}

function handleBannerUpload(event) {
    const file = event.target.files[0];
    if (file) {
        if (!validateFile(file, 3 * 1024 * 1024)) { // 3MB limit for banner
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('bannerPreview').innerHTML =
                `<img src="${e.target.result}" alt="Banner Preview" class="current-image">
                 <small class="text-muted d-block mt-1">{% trans "New banner selected - will be uploaded on save" %}</small>`;
        };
        reader.readAsDataURL(file);
    }
}

function validateFile(file, maxSize) {
    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
        alert('{% trans "Please select a valid image file (JPG, PNG, WebP)" %}');
        return false;
    }

    if (file.size > maxSize) {
        const maxSizeMB = Math.round(maxSize / (1024 * 1024));
        alert(`{% trans "File size must be less than" %} ${maxSizeMB}MB`);
        return false;
    }

    return true;
}

function loadExistingGallery() {
    const existingGalleryImages = {{ existing_gallery_images|safe }};
    
    if (!existingGalleryImages || existingGalleryImages.length === 0) {
        document.getElementById('galleryLoadingMessage').innerHTML =
            '<p class="text-muted">{% trans "No existing photos found" %}</p>';
        return;
    }

    platformData.existingGalleryImages = existingGalleryImages.map(url => ({
        url: url,
        name: url.split('/').pop()
    }));

    displayExistingGallery();
}

function displayExistingGallery() {
    const existingGalleryGrid = document.getElementById('existingGalleryGrid');
    existingGalleryGrid.innerHTML = '';

    platformData.existingGalleryImages.forEach((image, index) => {
        const imageDiv = document.createElement('div');
        imageDiv.className = 'gallery-item';
        imageDiv.innerHTML = `
            <img src="${image.url}" alt="${image.name || `Gallery Image ${index + 1}`}">
            <button class="remove-btn" type="button" onclick="removeExistingGalleryItem(${index})" aria-label="{% trans 'Remove image' %}">×</button>
        `;
        existingGalleryGrid.appendChild(imageDiv);
    });

    updateGalleryCount();
}

function removeExistingGalleryItem(index) {
    const imageToRemove = platformData.existingGalleryImages[index];
    
    // Send request to backend to remove the image
    fetch('{% url "remove_gallery_image" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            image_url: imageToRemove.url,
            employer_id: employerId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove from platformData only after successful backend removal
            platformData.existingGalleryImages.splice(index, 1);
            displayExistingGallery();
        } else {
            alert('{% trans "Failed to remove image. Please try again." %}');
        }
    })
    .catch(error => {
        console.error('Error removing image:', error);
        alert('{% trans "An error occurred while removing the image." %}');
    });
}

function updateGalleryCount() {
    const totalCount = platformData.existingGalleryImages.length + platformData.newGalleryImages.length;
    document.getElementById('galleryCount').textContent = totalCount;

    // Disable upload if at limit
    const uploadInput = document.getElementById('galleryUpload');
    if (totalCount >= MAX_GALLERY_PHOTOS) {
        uploadInput.disabled = true;
        uploadInput.parentElement.querySelector('.form-text').textContent =
            `Maximum ${MAX_GALLERY_PHOTOS} photos allowed. Please remove some photos to upload new ones.`;
    } else {
        uploadInput.disabled = false;
        uploadInput.parentElement.querySelector('.form-text').textContent =
            'Upload multiple photos to showcase your company culture, office, team, etc.';
    }
}

function handleGalleryUpload(event) {
    const files = event.target.files;

    // Check if adding these files would exceed the limit
    const currentTotal = platformData.existingGalleryImages.length + platformData.newGalleryImages.length;
    const newFilesCount = files.length;

    if (currentTotal + newFilesCount > MAX_GALLERY_PHOTOS) {
        alert(`{% trans "You can only upload" %} ${MAX_GALLERY_PHOTOS - currentTotal} {% trans "more photos. Maximum" %} ${MAX_GALLERY_PHOTOS} {% trans "photos allowed." %}`);
        event.target.value = ''; // Clear the input
        return;
    }

    // Validate each file
    for (let i = 0; i < files.length; i++) {
        if (!validateFile(files[i], 3 * 1024 * 1024)) { // 3MB limit for gallery photos
            event.target.value = '';
            return;
        }
    }

    // Clear previous new previews
    const newGalleryPreview = document.getElementById('newGalleryPreview');
    newGalleryPreview.innerHTML = '';
    platformData.newGalleryImages = [];

    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const reader = new FileReader();

        reader.onload = function(e) {
            platformData.newGalleryImages.push(e.target.result);

            const imageDiv = document.createElement('div');
            imageDiv.className = 'gallery-item';
            imageDiv.innerHTML = `
                <img src="${e.target.result}" alt="New Gallery Image">
                <button class="remove-btn" onclick="removeNewGalleryItem(${i})">×</button>
            `;
            newGalleryPreview.appendChild(imageDiv);

            updateGalleryCount();
        };

        reader.readAsDataURL(file);
    }
}

function removeNewGalleryItem(index) {
    platformData.newGalleryImages.splice(index, 1);

    // Rebuild the preview
    const newGalleryPreview = document.getElementById('newGalleryPreview');
    newGalleryPreview.innerHTML = '';

    platformData.newGalleryImages.forEach((imageData, i) => {
        const imageDiv = document.createElement('div');
        imageDiv.className = 'gallery-item';
        imageDiv.innerHTML = `
            <img src="${imageData}" alt="New Gallery Image">
            <button class="remove-btn" onclick="removeNewGalleryItem(${i})">×</button>
        `;
        newGalleryPreview.appendChild(imageDiv);
    });

    updateGalleryCount();
}

function handleFormSubmission(event) {
    event.preventDefault();

    // Validate all required fields
    const form = event.target;
    let isValid = true;

    // Check all required fields
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        const fieldValid = validateField({ target: field });
        if (!fieldValid) {
            isValid = false;
        }
    });

    // Validate social media URLs
    const socialInputs = ['linkedinUrl', 'instagramUrl', 'twitterUrl', 'githubUrl', 'facebookUrl', 'glassdoorUrl'];
    socialInputs.forEach(inputId => {
        const field = document.getElementById(inputId);
        if (field && field.value.trim()) {
            const fieldValid = validateSocialUrl({ target: field });
            if (!fieldValid) {
                isValid = false;
            }
        }
    });

    if (!isValid) {
        alert('{% trans "Please fix the validation errors before submitting." %}');
        return;
    }

    // Prepare form data
    const formData = new FormData();

    // Add basic employer data
    formData.append('employer_name', document.getElementById('employerName').value);
    formData.append('employer_email', document.getElementById('employerEmail').value);
    formData.append('employer_phone', document.getElementById('employerPhone').value);
    formData.append('employer_website', document.getElementById('employerWebsite').value);
    formData.append('employer_address', document.getElementById('employerAddress').value);
    formData.append('office_locations', document.getElementById('officeLocations').value);
    formData.append('employer_description', document.getElementById('employerDescription').value);
    formData.append('employer_industry', document.getElementById('employerIndustry').value);
    formData.append('employer_headcount', document.getElementById('employerHeadcount').value);

    // Prepare social media data
    const socialData = {
        social_links: []
    };

    Object.keys(platformData.socialLinks).forEach(platform => {
        const url = document.getElementById(platform + 'Url').value.trim();
        if (url) {
            socialData.social_links.push(`${platform};${url}`);
        }
    });

    formData.append('employer_social_portals', JSON.stringify(socialData));


    // Add file uploads
    const logoFile = document.getElementById('logoUpload').files[0];
    const bannerFile = document.getElementById('bannerUpload').files[0];
    const galleryFiles = document.getElementById('galleryUpload').files;

    if (logoFile) {
        formData.append('logo', logoFile);
    }

    if (bannerFile) {
        formData.append('banner', bannerFile);
    }

    // Add gallery files
    for (let i = 0; i < galleryFiles.length; i++) {
        formData.append('gallery[]', galleryFiles[i]);
    }

    // Show loading state
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Publishing..." %}';

    // Submit the form
    fetch('{% url "create_workloupe_profile" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the success modal with the correct URL
            const profileUrl = `https://workloupe.com/employers/${employerId}`;
            document.getElementById('profileUrlInput').value = profileUrl;
            document.getElementById('viewProfileBtn').href = profileUrl;

            // Show success modal
            const successModal = new bootstrap.Modal(document.getElementById('successModal'));
            successModal.show();
        } else {
            alert('{% trans "Error:" %} ' + (data.error || '{% trans "Unknown error occurred" %}'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{% trans "An error occurred while publishing your profile. Please try again." %}');
    })
    .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
}
function copyProfileUrl() {
    const urlInput = document.getElementById('profileUrlInput');
    urlInput.select();
    urlInput.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');
        // Show temporary feedback
        const copyBtn = event.target;
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check me-1"></i>{% trans "Copied!" %}';
        copyBtn.classList.add('btn-success');
        copyBtn.classList.remove('btn-outline-secondary');

        setTimeout(() => {
            copyBtn.innerHTML = originalText;
            copyBtn.classList.remove('btn-success');
            copyBtn.classList.add('btn-outline-secondary');
        }, 2000);
    } catch (err) {
        console.error('Failed to copy URL:', err);
        alert('{% trans "Failed to copy URL. Please copy it manually." %}');
    }
}

function resetForm() {
    if (confirm('{% trans "Are you sure you want to reset the form? All unsaved changes will be lost." %}')) {
        document.getElementById('workloupePlatformForm').reset();

        // Clear previews
        document.getElementById('logoPreview').innerHTML = `
            <div class="upload-placeholder">
                <i class="fas fa-image fa-2x text-muted mb-2"></i>
                <p class="text-muted">{% trans "No logo uploaded" %}</p>
            </div>
        `;

        document.getElementById('bannerPreview').innerHTML = `
            <div class="upload-placeholder">
                <i class="fas fa-image fa-2x text-muted mb-2"></i>
                <p class="text-muted">{% trans "No banner uploaded" %}</p>
            </div>
        `;

        document.getElementById('newGalleryPreview').innerHTML = '';

        // Reset platform data
        platformData.newGalleryImages = [];
        platformData.socialLinks = {
            linkedin: '',
            instagram: '',
            twitter: '',
            github: '',
            facebook: '',
            glassdoor: ''
        };

        // Remove validation classes
        document.querySelectorAll('.is-valid, .is-invalid').forEach(element => {
            element.classList.remove('is-valid', 'is-invalid');
        });

        updateGalleryCount();
    }
}



</script>
{% endblock %}
