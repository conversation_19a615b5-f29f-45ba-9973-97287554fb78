{% extends 'main.html' %}

{% load static %}
{% load i18n %}
{% block content %}
<!-- Include Material Icons -->
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

<div class="container">
    <div class="preferences-header">
        <h1>{% trans "Preferences" %}</h1>
        <p class="preferences-subtitle">{% trans "Configure standard options to streamline your job creation process" %}</p>

        <nav class="breadcrumb">
            <a href="{% url 'settings' %}">{% trans "Settings" %}</a>
            <i class="bi bi-chevron-right"></i>
            <span>{% trans "Preferences" %}</span>
        </nav>
    </div>

    <!-- Tabs Navigation -->
    <div class="tabs-container">
        <div class="tabs">
            <button class="tab-btn active" data-tab="work-schedules">
                <span class="material-icons">schedule</span>
                {% trans "Work Schedules" %}
            </button>
            <button class="tab-btn" data-tab="office-schedules">
                <span class="material-icons">home_work</span>
                {% trans "Office Schedules" %}
            </button>
            <button class="tab-btn" data-tab="locations">
                <span class="material-icons">location_on</span>
                {% trans "Locations" %}
            </button>
            <button class="tab-btn" data-tab="departments">
                <span class="material-icons">people</span>
                {% trans "Departments" %}
            </button>
            <button class="tab-btn" data-tab="language" style="display: none;">
                <span class="material-icons">language</span>
                {% trans "Language" %}
            </button>
        </div>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Work Schedules Tab -->
            <div class="tab-pane active" id="work-schedules">
                <div class="tab-header">
                    <h2>{% trans "Work Schedules" %}</h2>
                    <p>{% trans "Define standard work schedule types for your organization" %}</p>

                    <button class="add-btn" id="add-work-schedule-btn">
                        <span class="material-icons">add</span>
                        {% trans "Add Work Schedule" %}
                    </button>
                </div>

                <div class="list-container">
                    <div class="list-header">
                        <div class="list-search">
                            <span class="material-icons">search</span>
                            <input type="text" placeholder="{% trans 'Search work schedules...' %}" id="search-work-schedules">
                        </div>
                        <div class="list-actions">
                            <button class="secondary-btn" id="select-all-work-schedules" style="display: none;">{% trans "Select All" %}</button>
                            <button class="delete-btn" id="delete-work-schedules" disabled>
                                <span class="material-icons">delete</span>
                                {% trans "Delete" %}
                            </button>
                        </div>
                    </div>

                    <div class="list-body" id="work-schedules-list">

                    </div>
                </div>
            </div>

            <!-- Office Schedules Tab -->
            <div class="tab-pane" id="office-schedules">
                <div class="tab-header">
                    <h2>{% trans "Office Schedules" %}</h2>
                    <p>{% trans "Define where and how employees work" %}</p>

                    <button class="add-btn" id="add-office-schedule-btn">
                        <span class="material-icons">add</span>
                        {% trans "Add Office Schedule" %}
                    </button>
                </div>

                <div class="list-container">
                    <div class="list-header">
                        <div class="list-search">
                            <span class="material-icons">search</span>
                            <input type="text" placeholder="Search office schedules..." id="search-office-schedules">
                        </div>
                        <div class="list-actions">
                            <button class="secondary-btn" id="select-all-office-schedules" style="display: none;">Select All</button>
                            <button class="delete-btn" id="delete-office-schedules" disabled>
                                <span class="material-icons">delete</span>
                                Delete
                            </button>
                        </div>
                    </div>

                    <div class="list-body" id="office-schedules-list">

                    </div>
                </div>
            </div>

            <!-- Locations Tab -->
            <div class="tab-pane" id="locations">
                <div class="tab-header">
                    <h2>Office Locations</h2>
                    <p>Manage your company's office locations</p>

                    <button class="add-btn" id="add-locations-btn">
                        <span class="material-icons">add</span>
                        Add Location
                    </button>
                </div>

                <div class="list-container">
                    <div class="list-header">
                        <div class="list-search">
                            <span class="material-icons">search</span>
                            <input type="text" placeholder="Search locations..." id="search-locations">
                        </div>
                        <div class="list-actions">
                            <button class="secondary-btn" id="select-all-locations" style="display: none;">Select All</button>
                            <button class="delete-btn" id="delete-locations" disabled>
                                <span class="material-icons">delete</span>
                                Delete
                            </button>
                        </div>
                    </div>

                    <div class="list-body" id="locations-list">

                    </div>
                </div>
            </div>

            <!-- Departments Tab -->
            <div class="tab-pane" id="departments">
                <div class="tab-header">
                    <h2>Departments</h2>
                    <p>Organize your company's structure with departments</p>

                    <button class="add-btn" id="add-department-btn">
                        <span class="material-icons">add</span>
                        Add Department
                    </button>
                </div>

                <div class="list-container">
                    <div class="list-header">
                        <div class="list-search">
                            <span class="material-icons">search</span>
                            <input type="text" placeholder="Search departments..." id="search-departments">
                        </div>
                        <div class="list-actions">
                            <button class="secondary-btn" id="select-all-departments" style="display: none;">Select All</button>
                            <button class="delete-btn" id="delete-departments" disabled>
                                <span class="material-icons">delete</span>
                                Delete
                            </button>
                        </div>
                    </div>

                    <div class="list-body" id="departments-list">

                    </div>
                </div>
            </div>

            <!-- Language Tab -->
            <div class="tab-pane" id="language" style="display: none;">
                <div class="tab-header">
                    <h2>{% trans "Language Settings" %}</h2>
                    <p>{% trans "Choose your preferred language for the application interface" %}</p>
                </div>

                <div class="language-container">
                    <div class="language-selection">
                        <h3>{% trans "Interface Language" %}</h3>
                        <p class="language-description">{% trans "Select the language you want to use for the application interface" %}</p>

                        <form method="post" action="{% url 'preferences' %}" id="language-form">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="change_language">
                            <div class="language-options">
                                {% for lang_code, lang_name in AVAILABLE_LANGUAGES %}
                                <div class="language-option">
                                    <input type="radio"
                                           id="lang-{{ lang_code }}"
                                           name="language"
                                           value="{{ lang_code }}"
                                           {% if lang_code == CURRENT_LANGUAGE %}checked{% endif %}
                                           onchange="changeLanguage('{{ lang_code }}')">
                                    <label for="lang-{{ lang_code }}" class="language-label">
                                        <div class="language-info">
                                            <span class="language-name">{{ lang_name }}</span>
                                            <span class="language-code">{{ lang_code|upper }}</span>
                                        </div>
                                        {% if lang_code == CURRENT_LANGUAGE %}
                                        <span class="current-badge">{% trans "Current" %}</span>
                                        {% endif %}
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                        </form>

                        <div class="language-note">
                            <div class="note-icon">
                                <span class="material-icons">info</span>
                            </div>
                            <div class="note-content">
                                <p><strong>{% trans "Note:" %}</strong> {% trans "Changing the language will refresh the page to apply the new language settings." %}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Modal Template -->
<div class="modal" id="preference-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modal-title">Add New Item</h2>
            <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
            <form id="preference-form">
                <div class="form-group">
                    <label for="item-name">Name</label>
                    <input type="text" id="item-name" placeholder="Enter name" required>
                </div>
                <div class="form-group">
                    <label for="item-description">Description</label>
                    <textarea id="item-description" placeholder="Enter description" rows="3"></textarea>
                </div>
                <div class="form-group" id="additional-fields">
                    <!-- Additional fields will be added dynamically based on the tab -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="cancel-btn" id="cancel-preference">Cancel</button>
                    <button type="submit" class="confirm-btn" id="save-preference">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Confirmation Dialog -->
<div class="confirmation-dialog" id="confirm-delete-dialog">
    <div class="dialog-content">
        <div class="dialog-header">
            <h3>Delete Items</h3>
            <span class="close-dialog">&times;</span>
        </div>
        <div class="dialog-body">
            <p>Are you sure you want to delete the selected items? This action cannot be undone.</p>
        </div>
        <div class="dialog-footer">
            <button class="cancel-btn" id="cancel-delete">Cancel</button>
            <button class="danger-btn" id="confirm-delete">Delete</button>
        </div>
    </div>
</div>

<div class="toast-container" id="toast-container"></div>

<style>
    :root {
        --primary: #4a6cf7;
        --primary-hover: #3859e9;
        --secondary: #f5f8ff;
        --success: #28a745;
        --danger: #dc3545;
        --text-color: #333;
        --border-color: #ddd;
        --hover-bg: #f9fafb;
        --success-color: #4CAF50;
        --error-color: #F44336;
        --info-color: #2196F3;
        --warning-color: #FF9800;
        --shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
        background-color: #f9fafc;
        color: var(--text-color);
        line-height: 1.6;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;
    }

    /* Preferences Header */
    .preferences-header {
        margin-bottom: 30px;
    }

    .preferences-header h1 {
        font-size: 28px;
        color: #252b42;
        margin-bottom: 10px;
    }

    .preferences-subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 20px;
    }

    /* Breadcrumb */
    .breadcrumb {
        display: flex;
        align-items: center;
        font-size: 14px;
    }

    .breadcrumb a {
        color: var(--primary);
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .breadcrumb .material-icons {
        font-size: 16px;
        margin: 0 8px;
        color: #999;
    }

    /* Tabs Container */
    .tabs-container {
        background-color: white;
        border-radius: 8px;
        box-shadow: var(--shadow);
        overflow: hidden;
    }

    /* Tabs Navigation */
    .tabs {
        display: flex;
        overflow-x: auto;
        border-bottom: 1px solid var(--border-color);
        background-color: #f9fafb;
    }

    .tab-btn {
        padding: 15px 20px;
        background: none;
        border: none;
        cursor: pointer;
        font-size: 15px;
        font-weight: 500;
        color: #666;
        border-bottom: 3px solid transparent;
        white-space: nowrap;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s;
    }

    .tab-btn:hover {
        background-color: #f0f4ff;
        color: var(--primary);
    }

    .tab-btn.active {
        color: var(--primary);
        border-bottom-color: var(--primary);
        background-color: white;
    }

    .tab-btn .material-icons {
        font-size: 20px;
    }

    /* Tab Content */
    .tab-content {
        min-height: 400px;
    }

    .tab-pane {
        display: none;
        padding: 30px;
    }

    .tab-pane.active {
        display: block;
        animation: fadeIn 0.3s;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Tab Header */
    .tab-header {
        margin-bottom: 20px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: flex-start;
    }

    .tab-header h2 {
        font-size: 20px;
        color: #252b42;
        margin-bottom: 5px;
        flex-basis: 100%;
    }

    .tab-header p {
        color: #666;
        margin-bottom: 20px;
        flex-basis: 100%;
    }

    /* Add Button */
    .add-btn {
        background-color: var(--primary);
        color: white;
        border: none;
        padding: 10px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 6px;
        transition: background-color 0.3s;
    }

    .add-btn:hover {
        background-color: var(--primary-hover);
    }

    .add-btn .material-icons {
        font-size: 18px;
    }

    /* List Container */
    .list-container {
        border: 1px solid var(--border-color);
        border-radius: 6px;
        overflow: hidden;
    }

    /* List Header */
    .list-header {
        padding: 15px;
        background-color: #f9fafb;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .list-search {
        position: relative;
        flex: 1;
        max-width: 300px;
    }

    .list-search .material-icons {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
        font-size: 18px;
    }

    .list-search input {
        width: 100%;
        padding: 8px 10px 8px 35px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 14px;
    }

    .list-search input:focus {
        outline: none;
        border-color: var(--primary);
    }

    .list-actions {
        display: flex;
        gap: 10px;
    }

    .secondary-btn {
        background-color: white;
        color: #666;
        border: 1px solid var(--border-color);
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .secondary-btn:hover {
        background-color: #f5f5f5;
    }

    .delete-btn {
        background-color: white;
        color: var(--danger);
        border: 1px solid var(--danger);
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .delete-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .delete-btn:hover:not(:disabled) {
        background-color: var(--danger);
        color: white;
    }

    .delete-btn .material-icons {
        font-size: 16px;
    }

    /* List Body */
    .list-body {
        max-height: 500px;
        overflow-y: auto;
    }

    .list-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid var(--border-color);
        transition: background-color 0.3s;
    }

    .list-item:last-child {
        border-bottom: none;
    }

    .list-item:hover {
        background-color: var(--hover-bg);
    }

    .list-item-select {
        margin-right: 15px;
        position: relative;
    }

    .item-checkbox {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }

    .item-checkbox + label {
        position: relative;
        display: inline-block;
        width: 18px;
        height: 18px;
        background-color: white;
        border: 1px solid #ccc;
        border-radius: 3px;
        cursor: pointer;
    }

    .item-checkbox:checked + label {
        background-color: var(--primary);
        border-color: var(--primary);
    }

    .item-checkbox:checked + label:after {
        content: '';
        position: absolute;
        left: 6px;
        top: 2px;
        width: 4px;
        height: 9px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }

    .list-item-content {
        flex: 1;
    }

    .list-item-title {
        font-weight: 600;
        margin-bottom: 3px;
    }

    .list-item-description {
        font-size: 13px;
        color: #666;
    }

    .list-item-actions {
        margin-left: 10px;
    }

    .icon-btn {
        background: none;
        border: none;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
        cursor: pointer;
        transition: all 0.3s;
    }

    .icon-btn:hover {
        background-color: #f0f0f0;
        color: var(--primary);
    }

    .edit-btn .material-icons {
        font-size: 18px;
    }

    /* Modal */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        overflow: auto;
    }

    .modal-content {
        background-color: white;
        margin: 10% auto;
        max-width: 500px;
        width: 90%;
        border-radius: 8px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        animation: modalFadeIn 0.3s;
    }

    @keyframes modalFadeIn {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-header {
        padding: 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
    }

    .close-modal {
        font-size: 24px;
        cursor: pointer;
        color: #999;
    }

    .close-modal:hover {
        color: #333;
    }

    .modal-body {
        padding: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        font-size: 14px;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 14px;
    }

    .form-group input:focus,
    .form-group textarea:focus,
    .form-group select:focus {
        outline: none;
        border-color: var(--primary);
    }

    .modal-footer {
        padding-top: 20px;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    .cancel-btn, .confirm-btn, .danger-btn {
        padding: 10px 16px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
    }

    .cancel-btn {
        background-color: white;
        color: #666;
        border: 1px solid var(--border-color);
    }

    .cancel-btn:hover {
        background-color: #f5f5f5;
    }

    .confirm-btn {
        background-color: var(--primary);
        color: white;
        border: none;
    }

    .confirm-btn:hover {
        background-color: var(--primary-hover);
    }

    .danger-btn {
        background-color: var(--danger);
        color: white;
        border: none;
    }

    .danger-btn:hover {
        background-color: #c82333;
    }

    /* Confirmation Dialog */
    .confirmation-dialog {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        overflow: auto;
    }

    .dialog-content {
        background-color: white;
        margin: 15% auto;
        max-width: 400px;
        width: 90%;
        border-radius: 8px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        animation: modalFadeIn 0.3s;
    }

    .dialog-header {
        padding: 15px 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .dialog-header h3 {
        margin: 0;
        font-size: 18px;
    }

    .close-dialog {
        font-size: 24px;
        cursor: pointer;
        color: #999;
    }

    .close-dialog:hover {
        color: #333;
    }

    .dialog-body {
        padding: 20px;
    }

    .dialog-footer {
        padding: 15px 20px;
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .tabs {
            flex-wrap: wrap;
        }

        .tab-btn {
            flex: 1 0 auto;
            text-align: center;
            justify-content: center;
        }

        .tab-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .add-btn {
            margin-bottom: 15px;
        }

        .list-header {
            flex-direction: column;
            gap: 10px;
        }

        .list-search {
            max-width: 100%;
        }
    }
    /* Toast Container */
    .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999 !important;
        display: flex;
        flex-direction: column;
        gap: 10px;
        pointer-events: auto !important;
    }

    /* Toast Notification */
    .toast {
        min-width: 300px;
        max-width: 350px;
        background-color: white;
        color: #333;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        border-radius: 6px;
        padding: 16px;
        margin-bottom: 10px;
        display: flex;
        align-items: flex-start;
        animation: slide-in 0.3s ease forwards;
        position: relative;
        overflow: hidden;
        pointer-events: auto !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .toast.hide {
        animation: slide-out 0.3s ease forwards;
    }

    .toast::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 5px;
        height: 100%;
    }

    .toast.success::before { background-color: #4CAF50; }
    .toast.error::before { background-color: #F44336; }
    .toast.info::before { background-color: #2196F3; }
    .toast.warning::before { background-color: #FF9800; }

    .toast-icon {
        flex-shrink: 0;
        width: 24px;
        height: 24px;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .toast-icon svg {
        width: 20px;
        height: 20px;
    }

    .toast.success .toast-icon svg { fill: #4CAF50; }
    .toast.error .toast-icon svg { fill: #F44336; }
    .toast.info .toast-icon svg { fill: #2196F3; }
    .toast.warning .toast-icon svg { fill: #FF9800; }

    .toast-content {
        flex-grow: 1;
    }

    .toast-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .toast-message {
        font-size: 14px;
        opacity: 0.9;
    }

    .toast-close {
        background: none;
        border: none;
        color: #888;
        cursor: pointer;
        font-size: 16px;
        padding: 0 5px;
        margin-left: 10px;
        opacity: 0.7;
        transition: opacity 0.2s;
    }

    .toast-close:hover {
        opacity: 1;
        transform: none;
        box-shadow: none;
    }

    .toast-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.1);
    }

    .toast-progress-bar {
        height: 100%;
        width: 100%;
    }

    .toast.success .toast-progress-bar { background-color: #4CAF50; }
    .toast.error .toast-progress-bar { background-color: #F44336; }
    .toast.info .toast-progress-bar { background-color: #2196F3; }
    .toast.warning .toast-progress-bar { background-color: #FF9800; }

    /* Toast animations */
    @keyframes slide-in {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slide-out {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    @keyframes progress {
        from { width: 100%; }
        to { width: 0%; }
    }

    @media (max-width: 576px) {
        .toast-container {
            top: 10px;
            right: 10px;
            left: 10px;
        }
        .toast {
            min-width: 0;
            max-width: none;
            width: 100%;
        }
    }

    /* Language Selection Styles */
    .language-container {
        max-width: 600px;
    }

    .language-selection h3 {
        font-size: 18px;
        color: #252b42;
        margin-bottom: 8px;
    }

    .language-description {
        color: #666;
        margin-bottom: 24px;
        font-size: 14px;
    }

    .language-options {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 24px;
    }

    .language-option {
        position: relative;
    }

    .language-option input[type="radio"] {
        position: absolute;
        opacity: 0;
        cursor: pointer;
    }

    .language-label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: white;
    }

    .language-label:hover {
        border-color: var(--primary);
        background-color: rgba(67, 97, 238, 0.02);
    }

    .language-option input[type="radio"]:checked + .language-label {
        border-color: var(--primary);
        background-color: rgba(67, 97, 238, 0.05);
    }

    .language-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .language-name {
        font-weight: 600;
        color: #252b42;
        font-size: 16px;
    }

    .language-code {
        font-size: 12px;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .current-badge {
        background-color: var(--primary);
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
    }

    .language-note {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        background-color: rgba(33, 150, 243, 0.05);
        border: 1px solid rgba(33, 150, 243, 0.2);
        border-radius: 8px;
        margin-top: 20px;
    }

    .note-icon {
        flex-shrink: 0;
        color: #2196F3;
    }

    .note-icon .material-icons {
        font-size: 20px;
    }

    .note-content {
        flex: 1;
    }

    .note-content p {
        margin: 0;
        font-size: 14px;
        color: #666;
        line-height: 1.5;
    }

    .note-content strong {
        color: #2196F3;
    }
</style>

<script>

    const TOAST_ICONS = {
        success: '<svg viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg>',
        error: '<svg viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"></path></svg>',
        warning: '<svg viewBox="0 0 24 24"><path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"></path></svg>',
        info: '<svg viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"></path></svg>'
    };

    window.showToast = function(type, title, message, duration = 5000) {
        console.log('showToast called with', type, title, message);

        // Make sure the container exists
        let container = document.getElementById('toast-container');
        if (!container) {
            console.log('Creating toast container');
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container';
            document.body.appendChild(container);
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        // Toast inner HTML structure
        toast.innerHTML = `
            <div class="toast-icon">${TOAST_ICONS[type]}</div>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close" onclick="closeToast(this.parentElement)">&times;</button>
            <div class="toast-progress">
                <div class="toast-progress-bar"></div>
            </div>
        `;

        // Add toast to container
        container.appendChild(toast);

        // Set progress bar animation
        const progressBar = toast.querySelector('.toast-progress-bar');
        progressBar.style.animation = `progress ${duration / 1000}s linear forwards`;

        // Auto-remove toast after duration
        const timeoutId = setTimeout(() => {
            closeToast(toast);
        }, duration);

        // Store timeout ID to clear if manually closed
        toast.dataset.timeoutId = timeoutId;

        return toast;
    };

    window.closeToast = function(toast) {
        // Clear the timeout if it exists
        if (toast.dataset.timeoutId) {
            clearTimeout(parseInt(toast.dataset.timeoutId));
        }

        // Add hide class for slide-out animation
        toast.classList.add('hide');

        // Remove toast after animation completes
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    };

    // Add this function to reset the form to its default state
    function resetFormToDefault() {
        const form = document.getElementById('preference-form');

        // Reset to the default structure
        form.innerHTML = `
            <div class="form-group">
                <label for="item-name">Name</label>
                <input type="text" id="item-name" placeholder="Enter name" required>
            </div>
            <div class="form-group">
                <label for="item-description">Description</label>
                <textarea id="item-description" placeholder="Enter description" rows="3"></textarea>
            </div>
            <div class="form-group" id="additional-fields">
                <!-- Additional fields will be added dynamically based on the tab -->
            </div>
            <div class="modal-footer">
                <button type="button" class="cancel-btn" id="cancel-preference">Cancel</button>
                <button type="submit" class="confirm-btn" id="save-preference">Save</button>
            </div>
        `;

        // Reattach the cancel event listener
        document.getElementById('cancel-preference').addEventListener('click', closeModal);
    }
    // Add category-specific fields to the form
    function addCategorySpecificFields(category, container) {
        container.innerHTML = ''; // Clear existing content
        console.log(category);
        switch(category) {
            case 'locations':
            container.innerHTML += `
                <div class="form-group">
                    <label for="country">Country</label>
                    <input type="text" id="country" required>
                </div>
                <div class="form-group">
                    <label for="city">City</label>
                    <input type="text" id="city" required>
                </div>
            `;
            break;
        }
    }
    function createDepartmentListItem(department) {
        const listItem = document.createElement('div');
        listItem.className = 'list-item';
        listItem.dataset.id = department.id;

        listItem.innerHTML = `
            <div class="list-item-select">
                <input type="checkbox" id="dep-${department.id}" class="item-checkbox">
                <label for="dep-${department.id}"></label>
            </div>
            <div class="list-item-content">
                <div class="list-item-title">${department.name}</div>
                <div class="list-item-description">${department.description}</div>
            </div>
            <div class="list-item-actions">
                <button class="icon-btn edit-btn" data-id="${department.id}">
                    <span class="material-icons">edit</span>
                </button>
            </div>
        `;

        return listItem;
    }
    function loadDepartments() {
        fetch('/api/departments/')
            .then(handleFetchError)
            .then(response => response.json())
            .then(data => {
                const departmentsList = document.getElementById('departments-list');
                departmentsList.innerHTML = '';

                data.departments.forEach(department => {
                    const listItem = createDepartmentListItem(department);
                    departmentsList.appendChild(listItem);
                });

                // Add event listeners to the edit buttons
                document.querySelectorAll('#departments-list .edit-btn').forEach(btn => {
                    btn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const departmentId = this.getAttribute('data-id');
                        openModalForEdit('departments', departmentId);
                    });
                });

                // Add event listeners to the checkboxes
                document.querySelectorAll('#departments-list .item-checkbox').forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        updateDeleteButtonState('departments');
                    });
                });
            })
            .catch(error => {
                console.error('Error loading departments:', error);
                showToast('error', 'Error!', error.message);
            });
    }

    function validateDepartmentForm() {
        const nameInput = document.getElementById('item-name');
        const name = nameInput.value.trim();

        // Clear any existing error indicators
        nameInput.classList.remove('error');

        // Validate name field
        if (!name) {
            nameInput.classList.add('error');
            showToast('error', 'Error!', 'Department name is required.');
            return false;
        }

        return true;
    }

    function openModalForAdd(category) {
        resetFormToDefault();
        const modal = document.getElementById('preference-modal');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('preference-form');

        // Clear previous form data
        form.reset();

        // Clear previous attributes
        form.removeAttribute('data-id');
        form.removeAttribute('data-type');

        // Set modal title
        modalTitle.textContent = `Add New Department`;

        // Set form type
        form.setAttribute('data-type', 'department');

        // Show modal
        modal.style.display = 'block';
    }

    // Function to open the modal for editing a department
    function openModalForEdit(category, departmentId) {
        const modal = document.getElementById('preference-modal');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('preference-form');
        const nameInput = document.getElementById('item-name');
        const descInput = document.getElementById('item-description');

        // Clear previous attributes
        form.removeAttribute('data-type');

        // Set modal title
        modalTitle.textContent = `Edit Department`;

        // Set the data-id attribute
        form.setAttribute('data-id', departmentId);

        // Set form type
        form.setAttribute('data-type', 'department');

        // Fetch department details
        fetch(`/api/departments/${departmentId}/`)
            .then(response => response.json())
            .then(department => {
                nameInput.value = department.name;
                descInput.value = department.description || '';

                // Show modal
                modal.style.display = 'block';
            })
            .catch(error => {
                console.error('Error fetching department details:', error);
            });
    }
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Function to add a new department
    function addDepartment() {
        if (!validateDepartmentForm()) {
            return;
        }
        const nameInput = document.getElementById('item-name');
        const descInput = document.getElementById('item-description');

        const departmentData = {
            name: nameInput.value.trim(),
            description: descInput.value.trim()
        };

        fetch('/api/departments/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify(departmentData),
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close the modal
                    document.getElementById('preference-modal').style.display = 'none';

                    // Reload departments
                    loadDepartments();

                    // Show success toast
                    showToast('success', 'Success!', 'Department added successfully!');
                } else {
                    showToast('error', 'Error!', data.message);
                }
            })
            .catch(error => {
                console.error('Error adding department:', error);
                showToast('error', 'Error!', 'An error occurred while adding the department.');
            });
    }

    // Function to update a department
    function updateDepartment() {
        if (!validateDepartmentForm()) {
            return;
        }
        const form = document.getElementById('preference-form');
        const departmentId = form.getAttribute('data-id');
        const nameInput = document.getElementById('item-name');
        const descInput = document.getElementById('item-description');

        const departmentData = {
            name: nameInput.value.trim(),
            description: descInput.value.trim()
        };

        fetch(`/api/departments/${departmentId}/`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify(departmentData),
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close the modal
                    document.getElementById('preference-modal').style.display = 'none';

                    // Reload departments
                    loadDepartments();

                    // Show success toast
                    showToast('success', 'Success!', 'Department updated successfully!');

                } else {
                    showToast('error', 'Error!', data.message);
                }
            })
            .catch(error => {
                console.error('Error updating department:', error);
                showToast('error', 'Error!', 'An error occurred while updating the department.');
            });
    }

    // Function to delete selected departments
    function deleteDepartments() {
        const selectedDepartments = document.querySelectorAll('#departments-list .item-checkbox:checked');
        const deletePromises = [];

        selectedDepartments.forEach(checkbox => {
            const departmentId = checkbox.closest('.list-item').dataset.id;

            const deletePromise = fetch(`/api/departments/${departmentId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                },
            })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        throw new Error(data.message);
                    }
                    return data;
                });

            deletePromises.push(deletePromise);
        });

        Promise.all(deletePromises)
            .then(() => {
                // Close the confirmation dialog
                closeDialog();

                // Reload departments
                loadDepartments();

                // Show success toast
                showToast('success', 'Success!', `${selectedDepartments.length} department(s) deleted successfully!`);
            })
            .catch(error => {
                console.error('Error deleting departments:', error);
                showToast('error', 'Error!', 'An error occurred while deleting the departments.');
                closeDialog();
            });
    }

    function handleFetchError(response) {
        if (!response.ok) {
            // Create error message based on status
            let errorMsg = 'An error occurred with the request.';

            switch (response.status) {
                case 400:
                    errorMsg = 'Bad request: The server could not understand the request.';
                    break;
                case 401:
                    errorMsg = 'Unauthorized: Authentication is required for this request.';
                    break;
                case 403:
                    errorMsg = 'Forbidden: You do not have permission to access this resource.';
                    break;
                case 404:
                    errorMsg = 'Not found: The requested resource could not be found.';
                    break;
                case 500:
                    errorMsg = 'Server error: An error occurred on the server.';
                    break;
                default:
                    errorMsg = `Error ${response.status}: ${response.statusText}`;
            }

            throw new Error(errorMsg);
        }
        return response;
    }
    // Function to show the delete confirmation dialog
    function showDeleteConfirmation(category) {
        const confirmDialog = document.getElementById('confirm-delete-dialog');
        confirmDialog.dataset.category = category;
        confirmDialog.style.display = 'block';
    }

    // Function to update delete button state
    function updateDeleteButtonState(category) {
        const deleteBtn = document.getElementById(`delete-${category}`);
        const checkboxes = document.querySelectorAll(`#${category}-list .item-checkbox`);
        const hasSelection = Array.from(checkboxes).some(cb => cb.checked);

        deleteBtn.disabled = !hasSelection;
    }

    // Close modal
    function closeModal() {
        const modal = document.getElementById('preference-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // Update delete button state based on selections
    function updateDeleteButtonState(category) {
        const deleteBtn = document.getElementById(`delete-${category}`);
        const checkboxes = document.querySelectorAll(`#${category}-list .item-checkbox`);
        const hasSelection = Array.from(checkboxes).some(cb => cb.checked);

        deleteBtn.disabled = !hasSelection;
    }

    // Helper to find category from edit button
    function findCategory(element) {
        const listItem = element.closest('.list-item');
        const listBody = listItem.parentElement;
        const categoryId = listBody.id.replace('-list', '');
        return categoryId;
    }

    // Open modal with appropriate title and fields
    function openModal(action, category, itemId = null) {
        const modal = document.getElementById('preference-modal');
        const modalTitle = document.getElementById('modal-title');

        if (category !== 'locations') {
            resetFormToDefault();
        }

        const form = document.getElementById('preference-form');
        const additionalFields = document.getElementById('additional-fields');

        // Clear previous form data
        form.reset();

        // Clear previous attributes
        form.removeAttribute('data-id');
        form.removeAttribute('data-type');

        // Set modal title based on action
        if (action === 'add') {
            modalTitle.textContent = `Add New ${formatCategory(category)}`;
        } else {
            modalTitle.textContent = `Edit ${formatCategory(category)}`;
        }

        // Set the data-type attribute based on category
        switch(category) {
            case 'departments':
                form.setAttribute('data-type', 'department');
                break;
            case 'office-schedules':
                form.setAttribute('data-type', 'office-schedule');
                break;
            case 'work-schedules':
                form.setAttribute('data-type', 'work-schedule');
                break;
            case 'locations':
                // For locations, recreate the form without name and description fields
                const formHTML = `
                    <div class="form-group" id="additional-fields">
                        <div class="form-group">
                            <label for="city">City</label>
                            <input type="text" id="city" placeholder="City" required>
                        </div>
                        <div class="form-group">
                            <label for="country">Country</label>
                            <input type="text" id="country" placeholder="Country" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="cancel-btn" id="cancel-preference">Cancel</button>
                        <button type="submit" class="confirm-btn" id="save-preference">Save</button>
                    </div>
                `;
                form.innerHTML = formHTML;
                form.setAttribute('data-type', 'office-location');

                // Reattach the cancel event listener
                document.getElementById('cancel-preference').addEventListener('click', closeModal);
                break;
        }

        // If not locations, add category-specific fields (locations are handled above)
        if (category !== 'locations') {
            // Add category-specific fields
            addCategorySpecificFields(category, additionalFields);

            // For editing, populate with existing data
            if (action === 'edit' && itemId) {
                form.setAttribute('data-id', itemId);

                const nameInput = document.getElementById('item-name');
                const descInput = document.getElementById('item-description');

                const listItem = document.querySelector(`#${category}-list .edit-btn[data-id="${itemId}"]`).closest('.list-item');
                nameInput.value = listItem.querySelector('.list-item-title').textContent;
                descInput.value = listItem.querySelector('.list-item-description').textContent;
            }
        } else if (action === 'edit' && itemId) {
            // For locations edit, we need to load the data via API
            form.setAttribute('data-id', itemId);
            fetch(`/api/office-locations/${itemId}/`)
                .then(handleFetchError)
                .then(response => response.json())
                .then(location => {
                    document.getElementById('city').value = location.city || '';
                    document.getElementById('country').value = location.country || '';
                })
                .catch(error => {
                    console.error('Error fetching location details:', error);
                    showToast('error', 'Error!', error.message);
                });
        }

        // Show modal
        modal.style.display = 'block';
    }

    // Format category name for display
    function formatCategory(category) {
        // Handle special cases
        if (category === 'work-schedules') return 'Work Schedule';
        if (category === 'office-schedules') return 'Office Schedule';

        // Default formatting
        return category.charAt(0).toUpperCase() + category.slice(1).replace(/-/g, ' ');
    }

    function closeDialog() {
        const confirmDialog = document.getElementById('confirm-delete-dialog');
        if (confirmDialog) {
            confirmDialog.style.display = 'none';
        }
    }

    // Office Schedules functions
    function loadOfficeSchedules() {
        fetch('/api/office-schedules/')
            .then(handleFetchError)
            .then(response => response.json())
            .then(data => {
                const officeSchedulesList = document.getElementById('office-schedules-list');
                officeSchedulesList.innerHTML = '';

                data.office_schedules.forEach(schedule => {
                    const listItem = createOfficeScheduleListItem(schedule);
                    officeSchedulesList.appendChild(listItem);
                });

                // Add event listeners to the edit buttons
                document.querySelectorAll('#office-schedules-list .edit-btn').forEach(btn => {
                    btn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const scheduleId = this.getAttribute('data-id');
                        openModalForEditOfficeSchedule(scheduleId);
                    });
                });

                // Add event listeners to the checkboxes
                document.querySelectorAll('#office-schedules-list .item-checkbox').forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        updateDeleteButtonState('office-schedules');
                    });
                });
            })
            .catch(error => {
                console.error('Error loading office schedules:', error);
                showToast('error', 'Error!', error.message);
            });
    }

    function createOfficeScheduleListItem(schedule) {
        const listItem = document.createElement('div');
        listItem.className = 'list-item';
        listItem.dataset.id = schedule.id;

        listItem.innerHTML = `
            <div class="list-item-select">
                <input type="checkbox" id="os-${schedule.id}" class="item-checkbox">
                <label for="os-${schedule.id}"></label>
            </div>
            <div class="list-item-content">
                <div class="list-item-title">${schedule.name}</div>
                <div class="list-item-description">${schedule.description}</div>
            </div>
            <div class="list-item-actions">
                <button class="icon-btn edit-btn" data-id="${schedule.id}">
                    <span class="material-icons">edit</span>
                </button>
            </div>
        `;

        return listItem;
    }

    function validateOfficeScheduleForm() {
        const nameInput = document.getElementById('item-name');
        const name = nameInput.value.trim();

        // Clear any existing error indicators
        nameInput.classList.remove('error');

        // Validate name field
        if (!name) {
            nameInput.classList.add('error');
            showToast('error', 'Error!', 'Office schedule name is required.');
            return false;
        }

        return true;
    }

    function openModalForAddOfficeSchedule() {
        const modal = document.getElementById('preference-modal');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('preference-form');

        // Clear previous form data
        form.reset();

        // Set modal title
        modalTitle.textContent = `Add New Office Schedule`;

        // Clear the data-id attribute if it exists
        form.removeAttribute('data-id');
        form.removeAttribute('data-type');

        // Set form type attribute
        form.setAttribute('data-type', 'office-schedule');

        // Show modal
        modal.style.display = 'block';
    }

    function openModalForEditOfficeSchedule(scheduleId) {
        const modal = document.getElementById('preference-modal');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('preference-form');
        const nameInput = document.getElementById('item-name');
        const descInput = document.getElementById('item-description');

        // Set modal title
        modalTitle.textContent = `Edit Office Schedule`;

        // Clear previous attributes
        form.removeAttribute('data-type');

        // Set the data-id attribute
        form.setAttribute('data-id', scheduleId);

        // Set form type attribute
        form.setAttribute('data-type', 'office-schedule');

        // Fetch office schedule details
        fetch(`/api/office-schedules/${scheduleId}/`)
            .then(handleFetchError)
            .then(response => response.json())
            .then(schedule => {
                nameInput.value = schedule.name;
                descInput.value = schedule.description || '';

                // Show modal
                modal.style.display = 'block';
            })
            .catch(error => {
                console.error('Error fetching office schedule details:', error);
                showToast('error', 'Error!', error.message);
            });
    }

    function addOfficeSchedule() {
        if (!validateOfficeScheduleForm()) {
            return;
        }

        const nameInput = document.getElementById('item-name');
        const descInput = document.getElementById('item-description');

        const scheduleData = {
            name: nameInput.value.trim(),
            description: descInput.value.trim()
        };

        fetch('/api/office-schedules/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify(scheduleData),
        })
            .then(handleFetchError)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close the modal
                    document.getElementById('preference-modal').style.display = 'none';

                    // Reload office schedules
                    loadOfficeSchedules();

                    // Show success toast
                    showToast('success', 'Success!', 'Office schedule added successfully!');
                } else {
                    showToast('error', 'Error!', data.message);
                }
            })
            .catch(error => {
                console.error('Error adding office schedule:', error);
                showToast('error', 'Error!', 'An error occurred while adding the office schedule.');
            });
    }

    function updateOfficeSchedule() {
        if (!validateOfficeScheduleForm()) {
            return;
        }

        const form = document.getElementById('preference-form');
        const scheduleId = form.getAttribute('data-id');
        const nameInput = document.getElementById('item-name');
        const descInput = document.getElementById('item-description');

        const scheduleData = {
            name: nameInput.value.trim(),
            description: descInput.value.trim()
        };

        fetch(`/api/office-schedules/${scheduleId}/`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify(scheduleData),
        })
            .then(handleFetchError)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close the modal
                    document.getElementById('preference-modal').style.display = 'none';

                    // Reload office schedules
                    loadOfficeSchedules();

                    // Show success toast
                    showToast('success', 'Success!', 'Office schedule updated successfully!');
                } else {
                    showToast('error', 'Error!', data.message);
                }
            })
            .catch(error => {
                console.error('Error updating office schedule:', error);
                showToast('error', 'Error!', 'An error occurred while updating the office schedule.');
            });
    }

    function deleteOfficeSchedules() {
        const selectedSchedules = document.querySelectorAll('#office-schedules-list .item-checkbox:checked');
        const deletePromises = [];

        selectedSchedules.forEach(checkbox => {
            const scheduleId = checkbox.closest('.list-item').dataset.id;

            const deletePromise = fetch(`/api/office-schedules/${scheduleId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                },
            })
                .then(handleFetchError)
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        throw new Error(data.message);
                    }
                    return data;
                });

            deletePromises.push(deletePromise);
        });

        Promise.all(deletePromises)
            .then(() => {
                // Close the confirmation dialog
                closeDialog();

                // Reload office schedules
                loadOfficeSchedules();

                // Show success toast
                showToast('success', 'Success!', `${selectedSchedules.length} office schedule(s) deleted successfully!`);
            })
            .catch(error => {
                console.error('Error deleting office schedules:', error);
                showToast('error', 'Error!', 'An error occurred while deleting the office schedules.');
                closeDialog();
            });
    }

    // Work Schedules functions
    function loadWorkSchedules() {
        fetch('/api/work-schedules/')
            .then(handleFetchError)
            .then(response => response.json())
            .then(data => {
                const workSchedulesList = document.getElementById('work-schedules-list');
                workSchedulesList.innerHTML = '';

                data.work_schedules.forEach(schedule => {
                    const listItem = createWorkScheduleListItem(schedule);
                    workSchedulesList.appendChild(listItem);
                });

                // Add event listeners to the edit buttons
                document.querySelectorAll('#work-schedules-list .edit-btn').forEach(btn => {
                    btn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const scheduleId = this.getAttribute('data-id');
                        openModalForEditWorkSchedule(scheduleId);
                    });
                });

                // Add event listeners to the checkboxes
                document.querySelectorAll('#work-schedules-list .item-checkbox').forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        updateDeleteButtonState('work-schedules');
                    });
                });
            })
            .catch(error => {
                console.error('Error loading work schedules:', error);
                showToast('error', 'Error!', error.message);
            });
    }

    function createWorkScheduleListItem(schedule) {
        const listItem = document.createElement('div');
        listItem.className = 'list-item';
        listItem.dataset.id = schedule.id;

        listItem.innerHTML = `
            <div class="list-item-select">
                <input type="checkbox" id="ws-${schedule.id}" class="item-checkbox">
                <label for="ws-${schedule.id}"></label>
            </div>
            <div class="list-item-content">
                <div class="list-item-title">${schedule.name}</div>
                <div class="list-item-description">${schedule.description}</div>
            </div>
            <div class="list-item-actions">
                <button class="icon-btn edit-btn" data-id="${schedule.id}">
                    <span class="material-icons">edit</span>
                </button>
            </div>
        `;

        return listItem;
    }

    function validateWorkScheduleForm() {
        const nameInput = document.getElementById('item-name');
        const name = nameInput.value.trim();

        // Clear any existing error indicators
        nameInput.classList.remove('error');

        // Validate name field
        if (!name) {
            nameInput.classList.add('error');
            showToast('error', 'Error!', 'Work schedule name is required.');
            return false;
        }

        return true;
    }

    function openModalForAddWorkSchedule() {
        const modal = document.getElementById('preference-modal');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('preference-form');

        // Clear previous form data
        form.reset();

        // Clear previous attributes
        form.removeAttribute('data-id');
        form.removeAttribute('data-type');

        // Set modal title
        modalTitle.textContent = `Add New Work Schedule`;

        // Set form type attribute
        form.setAttribute('data-type', 'work-schedule');

        // Show modal
        modal.style.display = 'block';
    }

    function openModalForEditWorkSchedule(scheduleId) {
        const modal = document.getElementById('preference-modal');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('preference-form');
        const nameInput = document.getElementById('item-name');
        const descInput = document.getElementById('item-description');

        // Clear previous attributes
        form.removeAttribute('data-type');

        // Set modal title
        modalTitle.textContent = `Edit Work Schedule`;

        // Set the data-id attribute
        form.setAttribute('data-id', scheduleId);

        // Set form type attribute
        form.setAttribute('data-type', 'work-schedule');

        // Fetch work schedule details
        fetch(`/api/work-schedules/${scheduleId}/`)
            .then(handleFetchError)
            .then(response => response.json())
            .then(schedule => {
                nameInput.value = schedule.name;
                descInput.value = schedule.description || '';

                // Show modal
                modal.style.display = 'block';
            })
            .catch(error => {
                console.error('Error fetching work schedule details:', error);
                showToast('error', 'Error!', error.message);
            });
    }

    function addWorkSchedule() {
        if (!validateWorkScheduleForm()) {
            return;
        }

        const nameInput = document.getElementById('item-name');
        const descInput = document.getElementById('item-description');

        const scheduleData = {
            name: nameInput.value.trim(),
            description: descInput.value.trim()
        };


        fetch('/api/work-schedules/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify(scheduleData),
        })
            .then(handleFetchError)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close the modal
                    document.getElementById('preference-modal').style.display = 'none';

                    // Reload work schedules
                    loadWorkSchedules();

                    // Show success toast
                    showToast('success', 'Success!', 'Work schedule added successfully!');
                } else {
                    showToast('error', 'Error!', data.message);
                }
            })
            .catch(error => {
                console.error('Error adding work schedule:', error);
                showToast('error', 'Error!', 'An error occurred while adding the work schedule.');
            });
    }

    function updateWorkSchedule() {
        if (!validateWorkScheduleForm()) {
            return;
        }

        const form = document.getElementById('preference-form');
        const scheduleId = form.getAttribute('data-id');
        const nameInput = document.getElementById('item-name');
        const descInput = document.getElementById('item-description');

        const scheduleData = {
            name: nameInput.value.trim(),
            description: descInput.value.trim()
        };

        fetch(`/api/work-schedules/${scheduleId}/`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify(scheduleData),
        })
            .then(handleFetchError)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close the modal
                    document.getElementById('preference-modal').style.display = 'none';

                    // Reload work schedules
                    loadWorkSchedules();

                    // Show success toast
                    showToast('success', 'Success!', 'Work schedule updated successfully!');
                } else {
                    showToast('error', 'Error!', data.message);
                }
            })
            .catch(error => {
                console.error('Error updating work schedule:', error);
                showToast('error', 'Error!', 'An error occurred while updating the work schedule.');
            });
    }

    function deleteWorkSchedules() {
        const selectedSchedules = document.querySelectorAll('#work-schedules-list .item-checkbox:checked');
        const deletePromises = [];

        selectedSchedules.forEach(checkbox => {
            const scheduleId = checkbox.closest('.list-item').dataset.id;

            const deletePromise = fetch(`/api/work-schedules/${scheduleId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                },
            })
                .then(handleFetchError)
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        throw new Error(data.message);
                    }
                    return data;
                });

            deletePromises.push(deletePromise);
        });

        Promise.all(deletePromises)
            .then(() => {
                // Close the confirmation dialog
                closeDialog();

                // Reload work schedules
                loadWorkSchedules();

                // Show success toast
                showToast('success', 'Success!', `${selectedSchedules.length} work schedule(s) deleted successfully!`);
            })
            .catch(error => {
                console.error('Error deleting work schedules:', error);
                showToast('error', 'Error!', 'An error occurred while deleting the work schedules.');
                closeDialog();
            });
    }

    // Office Locations functions
    function loadOfficeLocations() {
        fetch('/api/office-locations/')
            .then(handleFetchError)
            .then(response => response.json())
            .then(data => {
                const locationsList = document.getElementById('locations-list');
                locationsList.innerHTML = '';

                data.office_locations.forEach(location => {
                    const listItem = createOfficeLocationListItem(location);
                    locationsList.appendChild(listItem);
                });

                // Add event listeners to the edit buttons
                document.querySelectorAll('#locations-list .edit-btn').forEach(btn => {
                    btn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const locationId = this.getAttribute('data-id');
                        openModalForEditOfficeLocation(locationId);
                    });
                });

                // Add event listeners to the checkboxes
                document.querySelectorAll('#locations-list .item-checkbox').forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        updateDeleteButtonState('locations');
                    });
                });
            })
            .catch(error => {
                console.error('Error loading office locations:', error);
                showToast('error', 'Error!', error.message);
            });
    }

    function createOfficeLocationListItem(location) {
        const listItem = document.createElement('div');
        listItem.className = 'list-item';
        listItem.dataset.id = location.id;

        // Format the title as "City, Country"
        const locationTitle = `${location.city}, ${location.country}`;

        listItem.innerHTML = `
            <div class="list-item-select">
                <input type="checkbox" id="loc-${location.id}" class="item-checkbox">
                <label for="loc-${location.id}"></label>
            </div>
            <div class="list-item-content">
                <div class="list-item-title">${locationTitle}</div>
                <div class="list-item-description">${location.address}</div>
            </div>
            <div class="list-item-actions">
                <button class="icon-btn edit-btn" data-id="${location.id}">
                    <span class="material-icons">edit</span>
                </button>
            </div>
        `;

        return listItem;
    }

    function validateOfficeLocationForm() {
        const cityInput = document.getElementById('city');
        const countryInput = document.getElementById('country');

        let isValid = true;

        // Clear any existing error indicators
        cityInput.classList.remove('error');
        countryInput.classList.remove('error');

        // Validate city field
        if (!cityInput.value.trim()) {
            cityInput.classList.add('error');
            showToast('error', 'Error!', 'City is required.');
            isValid = false;
        }

        // Validate country field
        if (!countryInput.value.trim()) {
            countryInput.classList.add('error');
            showToast('error', 'Error!', 'Country is required.');
            isValid = false;
        }

        return isValid;
    }

    function openModalForAddOfficeLocation() {
        const modal = document.getElementById('preference-modal');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('preference-form');
        const additionalFields = document.getElementById('additional-fields');

        // Clear previous form data
        form.reset();

        // Clear previous attributes
        form.removeAttribute('data-id');
        form.removeAttribute('data-type');

        // Set modal title
        modalTitle.textContent = `Add New Office Location`;

        // Set form type attribute
        form.setAttribute('data-type', 'office-location');

        // Add location-specific fields
        additionalFields.innerHTML = `
            <div class="form-group">
                <label for="city">City</label>
                <input type="text" id="city" placeholder="City" required>
            </div>
            <div class="form-group">
                <label for="country">Country</label>
                <input type="text" id="country" placeholder="Country" required>
            </div>
        `;

        // Show modal
        modal.style.display = 'block';
    }

    function openModalForEditOfficeLocation(locationId) {
        const modal = document.getElementById('preference-modal');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('preference-form');

        // Clear previous form data
        form.reset();

        // Set modal title
        modalTitle.textContent = `Edit Office Location`;

        // Clear previous attributes
        form.removeAttribute('data-type');

        // Set attributes
        form.setAttribute('data-id', locationId);
        form.setAttribute('data-type', 'office-location');

        // Recreate the form for locations
        const formHTML = `
            <div class="form-group" id="additional-fields">
                <div class="form-group">
                    <label for="city">City</label>
                    <input type="text" id="city" placeholder="City" required>
                </div>
                <div class="form-group">
                    <label for="country">Country</label>
                    <input type="text" id="country" placeholder="Country" required>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="cancel-btn" id="cancel-preference">Cancel</button>
                <button type="submit" class="confirm-btn" id="save-preference">Save</button>
            </div>
        `;
        form.innerHTML = formHTML;

        // Reattach the cancel event listener
        document.getElementById('cancel-preference').addEventListener('click', closeModal);

        // Fetch office location details
        fetch(`/api/office-locations/${locationId}/`)
            .then(handleFetchError)
            .then(response => response.json())
            .then(location => {
                document.getElementById('city').value = location.city || '';
                document.getElementById('country').value = location.country || '';

                // Show modal
                modal.style.display = 'block';
            })
            .catch(error => {
                console.error('Error fetching office location details:', error);
                showToast('error', 'Error!', error.message);
            });
    }

    function addOfficeLocation() {
        if (!validateOfficeLocationForm()) {
            return;
        }

        const cityInput = document.getElementById('city');
        const countryInput = document.getElementById('country');

        const locationData = {
            city: cityInput.value.trim(),
            country: countryInput.value.trim()
        };

        fetch('/api/office-locations/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify(locationData),
        })
            .then(handleFetchError)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close the modal
                    document.getElementById('preference-modal').style.display = 'none';

                    // Reload office locations
                    loadOfficeLocations();

                    // Show success toast
                    showToast('success', 'Success!', 'Office location added successfully!');
                } else {
                    showToast('error', 'Error!', data.message || 'An error occurred while adding the location.');
                }
            })
            .catch(error => {
                console.error('Error adding office location:', error);
                showToast('error', 'Error!', 'An error occurred while adding the office location.');
            });
    }

    function updateOfficeLocation() {
        if (!validateOfficeLocationForm()) {
            return;
        }

        const form = document.getElementById('preference-form');
        const locationId = form.getAttribute('data-id');
        const cityInput = document.getElementById('city');
        const countryInput = document.getElementById('country');

        const locationData = {
            city: cityInput.value.trim(),
            country: countryInput.value.trim()
        };

        fetch(`/api/office-locations/${locationId}/`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify(locationData),
        })
            .then(handleFetchError)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close the modal
                    document.getElementById('preference-modal').style.display = 'none';

                    // Reload office locations
                    loadOfficeLocations();

                    // Show success toast
                    showToast('success', 'Success!', 'Office location updated successfully!');
                } else {
                    showToast('error', 'Error!', data.message);
                }
            })
            .catch(error => {
                console.error('Error updating office location:', error);
                showToast('error', 'Error!', 'An error occurred while updating the office location.');
            });
    }

    function deleteOfficeLocations() {
        const selectedLocations = document.querySelectorAll('#locations-list .item-checkbox:checked');
        const deletePromises = [];

        selectedLocations.forEach(checkbox => {
            const locationId = checkbox.closest('.list-item').dataset.id;

            const deletePromise = fetch(`/api/office-locations/${locationId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                },
            })
                .then(handleFetchError)
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        throw new Error(data.message);
                    }
                    return data;
                });

            deletePromises.push(deletePromise);
        });

        Promise.all(deletePromises)
            .then(() => {
                // Close the confirmation dialog
                closeDialog();

                // Reload office locations
                loadOfficeLocations();

                // Show success toast
                showToast('success', 'Success!', `${selectedLocations.length} office location(s) deleted successfully!`);
            })
            .catch(error => {
                console.error('Error deleting office locations:', error);
                showToast('error', 'Error!', 'An error occurred while deleting the office locations.');
                closeDialog();
            });
    }

document.addEventListener('DOMContentLoaded', function() {

    if (!document.getElementById('toast-container')) {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container';
        document.body.appendChild(container);
    }

    document.querySelector('[data-tab="departments"]').addEventListener('click', loadDepartments);
    document.querySelector('[data-tab="office-schedules"]').addEventListener('click', loadOfficeSchedules);
    document.querySelector('[data-tab="work-schedules"]').addEventListener('click', loadWorkSchedules);
    document.querySelector('[data-tab="locations"]').addEventListener('click', loadOfficeLocations);


    if (document.querySelector('[data-tab="departments"]').classList.contains('active')) {
        loadDepartments();
    }
    if (document.querySelector('[data-tab="office-schedules"]').classList.contains('active')) {
        loadOfficeSchedules();
    }
    if (document.querySelector('[data-tab="work-schedules"]').classList.contains('active')) {
        loadWorkSchedules();
    }
    if (document.querySelector('[data-tab="locations"]').classList.contains('active')) {
        loadOfficeLocations();
    }

    document.getElementById('add-office-schedule-btn').addEventListener('click', function() {
        openModalForAddOfficeSchedule();
    });
    document.getElementById('add-department-btn').addEventListener('click', function() {
        openModalForAdd('departments');
    });
    document.getElementById('add-work-schedule-btn').addEventListener('click', function() {
        openModalForAddWorkSchedule();
    });
    document.getElementById('add-locations-btn').addEventListener('click', function() {
        openModalForAddOfficeLocation();
    });


    // Form submission handler
    document.getElementById('preference-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const modalTitle = document.getElementById('modal-title').textContent.toLowerCase();
        const formType = this.getAttribute('data-type');
        console.log(modalTitle);
        if (modalTitle.includes('department')) {
            if (modalTitle.includes('add')) {
                addDepartment();
            } else if (modalTitle.includes('edit')) {
                updateDepartment();
            }
        } else if (modalTitle.includes('office schedule') || formType === 'office-schedule') {
            if (modalTitle.includes('add')) {
                addOfficeSchedule();
            } else if (modalTitle.includes('edit')) {
                updateOfficeSchedule();
            }
        } else if (modalTitle.includes('work schedule') || formType === 'work-schedule') {
            if (modalTitle.includes('add')) {
                console.log("add work schedule called");
                addWorkSchedule();
            } else if (modalTitle.includes('edit')) {
                updateWorkSchedule();
            }
        } else if (modalTitle.includes('office location') || formType === 'office-location') {
            if (modalTitle.includes('add')) {
                addOfficeLocation();
            } else if (modalTitle.includes('edit')) {
                updateOfficeLocation();
            }
        }
    });

    document.getElementById('select-all-office-schedules').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('#office-schedules-list .item-checkbox');
        const isAllSelected = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(cb => {
            cb.checked = !isAllSelected;
        });

        updateDeleteButtonState('office-schedules');
    });
    document.getElementById('select-all-departments').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('#departments-list .item-checkbox');
        const isAllSelected = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(cb => {
            cb.checked = !isAllSelected;
        });

        updateDeleteButtonState('departments');
    });
    document.getElementById('select-all-work-schedules').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('#work-schedules-list .item-checkbox');
        const isAllSelected = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(cb => {
            cb.checked = !isAllSelected;
        });

        updateDeleteButtonState('work-schedules');
    });
    document.getElementById('select-all-locations').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('#locations-list .item-checkbox');
        const isAllSelected = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(cb => {
            cb.checked = !isAllSelected;
        });

        updateDeleteButtonState('locations');
    });

    document.getElementById('delete-office-schedules').addEventListener('click', function() {
        if (!this.disabled) {
            showDeleteConfirmation('office-schedules');
        }
    });
    document.getElementById('delete-departments').addEventListener('click', function() {
        if (!this.disabled) {
            showDeleteConfirmation('departments');
        }
    });
    document.getElementById('delete-work-schedules').addEventListener('click', function() {
        if (!this.disabled) {
            showDeleteConfirmation('work-schedules');
        }
    });
    document.getElementById('delete-locations').addEventListener('click', function() {
        if (!this.disabled) {
            showDeleteConfirmation('locations');
        }
    });

    document.getElementById('search-office-schedules').addEventListener('input', function() {
        const query = this.value.toLowerCase();
        const items = document.querySelectorAll('#office-schedules-list .list-item');

        items.forEach(item => {
            const title = item.querySelector('.list-item-title').textContent.toLowerCase();
            const description = item.querySelector('.list-item-description').textContent.toLowerCase();

            if (title.includes(query) || description.includes(query)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    });
    document.getElementById('search-work-schedules').addEventListener('input', function() {
        const query = this.value.toLowerCase();
        const items = document.querySelectorAll('#work-schedules-list .list-item');

        items.forEach(item => {
            const title = item.querySelector('.list-item-title').textContent.toLowerCase();
            const description = item.querySelector('.list-item-description').textContent.toLowerCase();

            if (title.includes(query) || description.includes(query)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    });
    document.getElementById('search-departments').addEventListener('input', function() {
        const query = this.value.toLowerCase();
        const items = document.querySelectorAll('#departments-list .list-item');

        items.forEach(item => {
            const title = item.querySelector('.list-item-title').textContent.toLowerCase();
            const description = item.querySelector('.list-item-description').textContent.toLowerCase();

            if (title.includes(query) || description.includes(query)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    });
    document.getElementById('search-locations').addEventListener('input', function() {
        const query = this.value.toLowerCase();
        const items = document.querySelectorAll('#locations-list .list-item');

        items.forEach(item => {
            const title = item.querySelector('.list-item-title').textContent.toLowerCase();
            const description = item.querySelector('.list-item-description').textContent.toLowerCase();

            if (title.includes(query) || description.includes(query)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    });

    document.getElementById('confirm-delete').addEventListener('click', function() {
        const currentCategory = document.getElementById('confirm-delete-dialog').dataset.category;
        if (currentCategory === 'departments') {
            deleteDepartments();
        } else if (currentCategory === 'office-schedules') {
            deleteOfficeSchedules();
        } else if (currentCategory === 'work-schedules') {
            deleteWorkSchedules();
        } else if (currentCategory === 'locations') {
            deleteOfficeLocations();
        }

    });

    // Tab switching functionality
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons and panes
            tabBtns.forEach(b => b.classList.remove('active'));
            tabPanes.forEach(p => p.classList.remove('active'));

            // Add active class to clicked button and corresponding pane
            this.classList.add('active');
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // Checkbox select all functionality
    const selectAllBtns = document.querySelectorAll('[id^="select-all-"]');
    selectAllBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const category = this.id.replace('select-all-', '');
            const checkboxes = document.querySelectorAll(`#${category}-list .item-checkbox`);
            const isAllSelected = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(cb => {
                cb.checked = !isAllSelected;
            });

            updateDeleteButtonState(category);
        });
    });

    // Individual checkbox functionality
    document.querySelectorAll('.item-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const category = this.id.split('-')[0];
            updateDeleteButtonState(category);
        });
    });



    // Search functionality
    const searchInputs = document.querySelectorAll('[id^="search-"]');

    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            const category = this.id.replace('search-', '');
            const query = this.value.toLowerCase();
            const items = document.querySelectorAll(`#${category}-list .list-item`);

            items.forEach(item => {
                const title = item.querySelector('.list-item-title').textContent.toLowerCase();
                const description = item.querySelector('.list-item-description').textContent.toLowerCase();

                if (title.includes(query) || description.includes(query)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });

    // Modal functionality
    const modal = document.getElementById('preference-modal');
    const addBtns = document.querySelectorAll('[id^="add-"]');
    const editBtns = document.querySelectorAll('.edit-btn');
    const closeModalBtn = document.querySelector('.close-modal');
    const cancelBtn = document.getElementById('cancel-preference');

    // Open modal for adding new item
    addBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const category = this.id.replace('add-', '').replace('-btn', '');
            openModal('add', category);
        });
    });

    // Open modal for editing item
    editBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const itemId = this.getAttribute('data-id');
            const category = findCategory(this);
            openModal('edit', category, itemId);
        });
    });

    // Close modal
    document.querySelector('.close-modal').addEventListener('click', function() {
        document.getElementById('preference-modal').style.display = 'none';
    });

    document.getElementById('cancel-preference').addEventListener('click', function() {
        document.getElementById('preference-modal').style.display = 'none';
    });

    // Close confirmation dialog
    document.querySelector('.close-dialog').addEventListener('click', function() {
        document.getElementById('confirm-delete-dialog').style.display = 'none';
    });

    document.getElementById('cancel-delete').addEventListener('click', function() {
        document.getElementById('confirm-delete-dialog').style.display = 'none';
    });




    closeModalBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('preference-modal');
        if (event.target === modal) {
            closeModal();
        }
    });

    // Form submission
    const preferenceForm = document.getElementById('preference-form');

    preferenceForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Here you would normally send the data to the server
        // For now, we'll just simulate success and close the modal

        closeModal();
    });

    // Delete functionality
    const deleteBtns = document.querySelectorAll('[id^="delete-"]');
    const confirmDialog = document.getElementById('confirm-delete-dialog');
    const closeDialogBtn = document.querySelector('.close-dialog');
    const cancelDeleteBtn = document.getElementById('cancel-delete');
    const confirmDeleteBtn = document.getElementById('confirm-delete');
    let currentCategory = null;

    deleteBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            if (!this.disabled) {
                currentCategory = this.id.replace('delete-', '');
                confirmDialog.style.display = 'block';
            }
        });
    });



    closeDialogBtn.addEventListener('click', closeDialog);
    cancelDeleteBtn.addEventListener('click', closeDialog);

    // Confirm delete action
    confirmDeleteBtn.addEventListener('click', function() {
        if (currentCategory) {
            // Here you would normally send the delete request to the server
            // For now, we'll just simulate success and close the dialog

            // Get all checked items in the current category
            const checkedItems = document.querySelectorAll(`#${currentCategory}-list .item-checkbox:checked`);

            // Remove them from the DOM (simulation only)
            checkedItems.forEach(item => {
                item.closest('.list-item').remove();
            });

            // Disable delete button since no items are selected anymore
            document.getElementById(`delete-${currentCategory}`).disabled = true;

            closeDialog();
        }
    });

    // Close dialog when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === confirmDialog) {
            closeDialog();
        }
    });


});

// Language change function
function changeLanguage(languageCode) {
    console.log('[LANGUAGE-JS] Changing language to:', languageCode);

    // Show loading indicator
    const languageOptions = document.querySelectorAll('.language-option');
    languageOptions.forEach(option => {
        option.style.opacity = '0.5';
        option.style.pointerEvents = 'none';
    });

    // Set cookie immediately for faster response
    document.cookie = `django_language=${languageCode}; path=/; max-age=${60*60*24*365}; SameSite=Lax`;

    // Create a form to submit the language change
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{% url "preferences" %}';

    // Add CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrfmiddlewaretoken';
    csrfInput.value = csrfToken;
    form.appendChild(csrfInput);

    // Add action
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'change_language';
    form.appendChild(actionInput);

    // Add language
    const languageInput = document.createElement('input');
    languageInput.type = 'hidden';
    languageInput.name = 'language';
    languageInput.value = languageCode;
    form.appendChild(languageInput);

    // Add timestamp to force refresh
    const timestampInput = document.createElement('input');
    timestampInput.type = 'hidden';
    timestampInput.name = 'timestamp';
    timestampInput.value = Date.now();
    form.appendChild(timestampInput);

    console.log('[LANGUAGE-JS] Submitting form with language:', languageCode);

    // Submit form
    document.body.appendChild(form);
    form.submit();
}
</script>
{% endblock %}