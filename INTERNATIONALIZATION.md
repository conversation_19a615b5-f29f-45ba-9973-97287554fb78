# Internationalization (i18n) Guide for Canvider

## Overview
This document explains how to manage translations and add new languages to the Canvider ATS application.

## Current Language Support
The application currently supports the following languages:
- **English (en)** - Default language
- **Spanish (es)** - Español
- **French (fr)** - Français
- **German (de)** - Deutsch
- **Turkish (tr)** - Türkçe

## Language Selection
Users can change their language preference by:
1. Going to **Settings** → **Preferences**
2. Clicking on the **Language** tab
3. Selecting their preferred language
4. The page will refresh automatically to apply the new language

## For Developers: Managing Translations

### Adding New Translatable Strings

1. **In Templates**: Use `{% trans "Your text" %}` for simple strings
   ```html
   {% load i18n %}
   <h1>{% trans "Dashboard" %}</h1>
   ```

2. **In Python Code**: Use `gettext_lazy` for model fields and forms
   ```python
   from django.utils.translation import gettext_lazy as _

   class MyModel(models.Model):
       status = models.CharField(choices=[("active", _("Active"))])
   ```

### Updating Translations

1. **Extract new strings** from code:
   ```bash
   python manage.py makemessages -a --ignore=venv
   ```

2. **Edit translation files** in `locale/[language]/LC_MESSAGES/django.po`:
   - Find empty `msgstr ""` entries
   - Add appropriate translations

3. **Compile translations**:
   ```bash
   python manage.py compilemessages
   ```

### Adding a New Language

1. **Add to settings.py**:
   ```python
   LANGUAGES = [
       ('en', 'English'),
       ('es', 'Español'),
       ('new_code', 'New Language Name'),
   ]
   ```

2. **Create translation files**:
   ```bash
   python manage.py makemessages -l new_code --ignore=venv
   ```

3. **Translate strings** in `locale/new_code/LC_MESSAGES/django.po`

4. **Compile translations**:
   ```bash
   python manage.py compilemessages
   ```

## Translation File Structure

Translation files are located in:
```
locale/
├── es/LC_MESSAGES/
│   ├── django.po    # Editable translation file
│   └── django.mo    # Compiled translation file (auto-generated)
├── fr/LC_MESSAGES/
├── de/LC_MESSAGES/
└── tr/LC_MESSAGES/
```

## Key Translated Areas

The following areas of the application are currently translated:

### Navigation & Core UI
- Navigation menu items (Feed, Jobs, Applicants, Settings)
- Common buttons (Save, Cancel, Edit, Delete)
- User menu (Profile, Settings, Logout)

### Job Management
- Job statuses (Active, Draft, Closed, On-Hold, Archived, Reviewing)
- Application states (New, Review #1-5, Hired, Rejected, etc.)
- Appointment types (Phone Call, Video Call, Interview types)

### Forms & Settings
- Preferences page sections
- Language selection interface
- Search placeholders

### Models & Admin
- Model field choices and labels
- Admin interface strings

## Best Practices

1. **Keep strings simple** - Avoid complex HTML in translatable strings
2. **Use context** - Add comments for translators when meaning is unclear
3. **Test thoroughly** - Check all languages after adding new strings
4. **Consistent terminology** - Use the same translation for the same concept
5. **Consider text expansion** - Some languages need more space than English

## Manual Translation Editing

To manually adjust translations:

1. Open the appropriate `.po` file in `locale/[language]/LC_MESSAGES/django.po`
2. Find the string you want to modify
3. Edit the `msgstr` value
4. Run `python manage.py compilemessages`
5. Restart the development server

Example:
```po
#: templates/navbar.html:23
msgid "Feed"
msgstr "Your Custom Translation"
```

## Security Considerations

- All user input is properly escaped in templates
- Translation strings are marked as safe only when necessary
- No executable code is included in translation files
- Language selection is validated against available languages

## Docker Environment Considerations

When running in Docker, language switching requires special handling:

1. **Custom Middleware**: The app includes `ForceLanguageMiddleware` to ensure language activation works in Docker
2. **Multiple Storage**: Language preferences are stored in both session and cookies for reliability
3. **Debug Endpoint**: Use `/language-test/` to check current language status

## Troubleshooting

**Translations not showing:**
1. Check if `compilemessages` was run
2. Verify the language code is in `LANGUAGES` setting
3. Ensure the browser language or session language is set correctly
4. **Docker**: Check container logs for `[LANGUAGE-DEBUG]` messages
5. **Docker**: Visit `/language-test/` to verify language detection

**Missing strings:**
1. Run `makemessages` to extract new strings
2. Check if the string is properly marked with `{% trans %}` or `_()`
3. Verify the translation exists in the `.po` file

**Docker-specific issues:**
1. Ensure session middleware is properly configured
2. Check that cookies are being set correctly
3. Verify the custom middleware is in the correct order
4. Check container logs for middleware debug messages

**Performance:**
- Translation files are cached in production
- Use `gettext_lazy` for strings that are evaluated at import time
- In Docker, language detection happens on every request for reliability
