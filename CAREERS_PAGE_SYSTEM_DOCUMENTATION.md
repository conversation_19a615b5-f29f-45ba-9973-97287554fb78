# Careers Page Builder System Documentation

## Overview

The Careers Page Builder System is a comprehensive solution that allows ATS clients to create and deploy job listings on their websites using multiple integration methods. The system provides four main integration options:

1. **Full Careers Page Builder** - Complete standalone careers page
2. **Widget Builder** - Embeddable widget for existing websites  
3. **WordPress Integration** - Shortcodes, widgets, and plugins for WordPress sites
4. **Workloupe Platform** - Company profile pages on the Workloupe platform

## System Architecture

### Frontend Templates
- `careers_page.html` - Main setup interface with integration options
- `create_careers_page_full.html` - Full page builder with customization controls
- `create_careers_widget.html` - Widget builder with live preview
- `wordpress_integration.html` - WordPress integration methods
- `workloupe_platform.html` - Company profile builder for Workloupe platform

### Backend API Endpoints
- `/careers-page/` - Main careers page setup interface
- `/careers-page/full/` - Full page builder interface
- `/careers-page/widget/` - Widget builder interface  
- `/careers-page/wordpress/` - WordPress integration interface
- `/careers-page/workloupe/` - Workloupe platform builder interface
- `/create-workloupe-profile/` - API to create/update company profiles
- `/api/company/<slug>/jobs/` - JSON API for job listings
- `/api/company/<slug>/rss/` - RSS feed for job listings
- `/api/generate-widget-code/` - Generate widget embed code
- `/api/generate-full-page-code/` - Generate full page HTML/CSS

## Features

### 1. Full Careers Page Builder
- **Customization Options:**
  - Company branding (logo, colors, fonts)
  - Page layout and styling
  - Content sections (about, values, stats)
  - Responsive design controls
- **Output:** Complete HTML/CSS page with embedded JavaScript for job loading
- **Integration:** Download and host on any web server

### 2. Widget Builder  
- **Customization Options:**
  - Widget dimensions and styling
  - Color scheme customization
  - Job display limits
  - Branding elements
- **Output:** Embeddable HTML/JavaScript widget code
- **Integration:** Copy/paste into any website

### 3. WordPress Integration
- **Three Integration Methods:**
  - **Shortcode:** `[workloupe_jobs company="company-name"]`
  - **Widget:** Drag-and-drop WordPress widget
  - **Plugin:** Full WordPress plugin with admin interface
- **Features:** WordPress-native styling and admin integration

### 4. Workloupe Platform
- **Company Profile Features:**
  - Complete company information management
  - Logo and banner image uploads
  - Photo gallery management
  - Social media integration
  - Digital Ocean Spaces integration for file storage
- **Output:** Public company profile page on Workloupe platform

## Technical Implementation

### Database Schema
The system uses the existing `Employer` model with these key fields:
- `employer_name` - Company name
- `employer_description` - Company description  
- `employer_logo_url` - Logo image URL
- `employer_banner_url` - Banner image URL
- `employer_website` - Company website
- `employer_industry` - Industry classification
- `employer_headcount` - Number of employees
- `employer_social_portals` - Social media links (JSON)

### File Upload Integration
- **Digital Ocean Spaces** integration for image storage
- **Supported file types:** Images (PNG, JPG, GIF)
- **Upload locations:**
  - Logos: `logos/{employer_id}_{filename}`
  - Banners: `banners/{employer_id}_{filename}`
  - Gallery: `gallery/{employer_id}_{index}_{filename}`

### API Response Format
```json
{
  "success": true,
  "company": {
    "name": "Company Name",
    "description": "Company description",
    "logo": "https://cdn.example.com/logo.png",
    "website": "https://company.com"
  },
  "jobs": [
    {
      "id": "job-123",
      "title": "Software Engineer",
      "location": "New York, NY",
      "description": "Job description...",
      "salary": "$80,000 - $120,000",
      "date": "2024-01-15",
      "url": "/apply/job-123/"
    }
  ]
}
```

## Configuration

### Environment Variables
Add these to your `.env` file for Digital Ocean Spaces integration:
```
DO_SPACES_KEY=your_spaces_key
DO_SPACES_SECRET=your_spaces_secret  
DO_SPACES_BUCKET=workloupe-assets
DO_SPACES_REGION=nyc3
DO_SPACES_ENDPOINT=https://nyc3.digitaloceanspaces.com
```

### Django Settings
The system automatically configures Digital Ocean Spaces settings in `settings.py`.

## Usage Examples

### Widget Integration
```html
<!-- Embed this code in any website -->
<div id="workloupe-careers-widget">
  <!-- Widget content loads here -->
</div>
<script src="https://your-domain.com/widget.js"></script>
```

### WordPress Shortcode
```php
// In WordPress posts/pages
[workloupe_jobs company="acme-corp" limit="5"]
```

### RSS Feed Integration
```xml
<!-- RSS feed URL -->
https://your-domain.com/api/company/acme-corp/rss/
```

## Styling and Customization

### CSS Framework
- **Bootstrap 5** for responsive design
- **Font Awesome** for icons
- **Custom CSS** for brand-specific styling

### Color Schemes
- Primary colors customizable per company
- Dark/light theme support
- Responsive design for mobile devices

### Typography
- Web-safe font stacks
- Custom font integration support
- Responsive typography scaling

## Security Considerations

- **CSRF Protection** on all form submissions
- **File Upload Validation** for image types and sizes
- **Input Sanitization** for all user-provided content
- **Access Control** for company profile management

## Performance Optimization

- **Database Query Optimization** with selective field loading
- **CDN Integration** for static assets via Digital Ocean Spaces
- **Caching** for frequently accessed job listings
- **Lazy Loading** for images and non-critical content

## Browser Compatibility

- **Modern Browsers:** Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile Support:** iOS Safari, Chrome Mobile, Samsung Internet
- **Responsive Design:** Works on all screen sizes from mobile to desktop

## Deployment

1. **Database Migration:** Ensure all Employer model fields are available
2. **Static Files:** Collect and serve static assets
3. **Environment Variables:** Configure Digital Ocean Spaces credentials
4. **URL Configuration:** Add careers page URLs to main URL configuration
5. **Testing:** Verify all integration methods work correctly

## Support and Maintenance

- **Error Logging:** All API endpoints include comprehensive error handling
- **Monitoring:** Track usage and performance metrics
- **Updates:** Regular updates for security and feature enhancements
- **Documentation:** Keep this documentation updated with system changes
