services:
  django-web:
    build: .
    container_name: django-docker
    ports:
      - "8000:8000"
    environment:
      DJANGO_SECRET_KEY: ${DJANGO_SECRET_KEY}
      DEBUG: ${DEBUG}
      DJANGO_LOGLEVEL: ${DJAN<PERSON><PERSON>_LOGLEVEL}
      DJ<PERSON><PERSON><PERSON>_ALLOWED_HOSTS: ${DJANGO_ALLOWED_HOSTS}
      DEEPSEEK_API_KEY: ${DEEPSEEK_SECRET_KEY}
      GEMINI_API_KEY: ${GEMINI_SECRET_KEY}
      OPENAI_API_KEY: ${OPENAI_SECRET_KEY}
      GEO_API_KEY: ${GEO_API_KEY}
    env_file:
      - .env
    volumes:
      - static_volume:/app/static
      - .:/app
    command: >
      sh -c "python manage.py collectstatic --noinput --verbosity 2 &&
           python manage.py runserver 0.0.0.0:8000"

volumes:
  static_volume:
