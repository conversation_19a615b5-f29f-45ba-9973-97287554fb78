# Run docker
- docker compose up --build
# Create Environment
- python -m venv canvider-env
# Activate Environment
- source canvider-env/bin/activate
# Deactivate Environment - Optional
- deactivate
# Install Django
- python -m pip install Django
# Install Bootstrap v5 
- python -m pip install django-bootstrap-v5
# Additional Packages
- python -m pip install django_compressor
- python -m pip install django-libsass
- pip install django-ckeditor
- pip install djangoql