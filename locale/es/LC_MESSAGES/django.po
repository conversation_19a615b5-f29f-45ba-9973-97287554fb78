# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-22 15:01+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: feed/models.py:64
msgid "Active"
msgstr "Activo"

#: feed/models.py:65
msgid "Draft"
msgstr "Borrador"

#: feed/models.py:66
msgid "Closed"
msgstr "Cerrado"

#: feed/models.py:67
msgid "On-Hold"
msgstr "En Espera"

#: feed/models.py:68
msgid "Archived"
msgstr "Archivado"

#: feed/models.py:69
msgid "Reviewing"
msgstr "Revisando"

#: feed/models.py:70
msgid "Deleted"
msgstr ""

#: feed/models.py:204 templates/applicant_dev.html:1044
msgid "New"
msgstr "Nuevo"

#: feed/models.py:205 templates/applicant_dev.html:1045
msgid "Review #1"
msgstr "Revisión #1"

#: feed/models.py:206 templates/applicant_dev.html:1046
msgid "Review #2"
msgstr "Revisión #2"

#: feed/models.py:207 templates/applicant_dev.html:1047
msgid "Review #3"
msgstr "Revisión #3"

#: feed/models.py:208 templates/applicant_dev.html:1048
msgid "Review #4"
msgstr "Revisión #4"

#: feed/models.py:209 templates/applicant_dev.html:1049
msgid "Review #5"
msgstr "Revisión #5"

#: feed/models.py:210 templates/applicant_dev.html:1050
msgid "Ready for Decision"
msgstr "Listo para Decisión"

#: feed/models.py:211 templates/applicant_dev.html:1051
msgid "Eliminated"
msgstr "Eliminado"

#: feed/models.py:212 templates/applicant_dev.html:1052
msgid "Offer Made"
msgstr "Oferta Realizada"

#: feed/models.py:213 templates/applicant_dev.html:1053
msgid "Candidate Accepted"
msgstr "Candidato Aceptado"

#: feed/models.py:214 templates/jobs.html:141
msgid "Hired"
msgstr "Contratado"

#: feed/models.py:215 templates/applicant_dev.html:1054
msgid "Candidate Rejected"
msgstr "Candidato Rechazado"

#: feed/models.py:241
msgid "Phone Call"
msgstr "Llamada Telefónica"

#: feed/models.py:242
msgid "Video Call"
msgstr "Videollamada"

#: feed/models.py:243
msgid "Online Interview"
msgstr "Entrevista Online"

#: feed/models.py:244
msgid "Technical Assessment"
msgstr "Evaluación Técnica"

#: feed/models.py:245
msgid "Final Interview"
msgstr "Entrevista Final"

#: feed/models.py:246
msgid "Face to Face Interview"
msgstr "Entrevista Presencial"

#: feed/models.py:247
msgid "Office Visit"
msgstr "Visita a la Oficina"

#: feed/models.py:248
msgid "Other"
msgstr "Otro"

#: feed/views.py:87
#, python-format
msgid "<strong>%(name)s</strong> applied for <strong>%(position)s</strong>"
msgstr ""

#: feed/views.py:123
#, python-format
msgid ""
"<strong>%(name)s</strong> moved to <strong>%(state)s</strong> for "
"<strong>%(position)s</strong>"
msgstr ""

#: feed/views.py:149
#, python-format
msgid "A new vacancy <strong>%(vacancy_title)s</strong> is published"
msgstr ""

#: feed/views.py:172
msgid "New comment on application of"
msgstr ""

#: feed/views.py:174
msgid "New comment on application ID"
msgstr ""

#: feed/views.py:858
msgid "Profile photo changed successfully!"
msgstr ""

#: feed/views.py:863
msgid "Please select a photo."
msgstr ""

#: feed/views.py:1264
#, python-format
msgid "Language changed to %(language)s"
msgstr ""

#: feed/views.py:1268
msgid "Invalid language selection"
msgstr ""

#: feed/views.py:1297 templates/feed.html:16
msgid "Dashboard"
msgstr "Panel de Control"

#: feed/views.py:1449
msgid "Invitation mail sent successfully!"
msgstr ""

#: feed/views.py:1452 feed/views.py:1595
msgid "Failed to send the invitation. Please check the form."
msgstr ""

#: feed/views.py:1484
msgid "Passwords do not match."
msgstr ""

#: feed/views.py:1521
msgid "No employer found to associate with this account."
msgstr ""

#: feed/views.py:1531
msgid "Registration completed successfully! You can now log in."
msgstr ""

#: feed/views.py:1535
#, python-format
msgid "Error creating account: %(error)s"
msgstr ""

#: feed/views.py:1577
msgid "Invitation sent successfully!"
msgstr ""

#: feed/views.py:1605
msgid "User removed successfully!"
msgstr ""

#: feed/views.py:1618
msgid "User status changed successfully!"
msgstr ""

#: feed/views.py:1731
msgid "Talent request sent successfully! Our team will get back to you soon."
msgstr ""

#: feed/views.py:1735
msgid "Invalid request method."
msgstr ""

#: templates/applicant_dev.html:30
msgid "Applied for:"
msgstr ""

#: templates/applicant_dev.html:89 templates/applicant_dev.html:825
#, fuzzy
#| msgid "Online Interview"
msgid "Schedule Interview"
msgstr "Entrevista Online"

#: templates/applicant_dev.html:99
msgid "Change State"
msgstr ""

#: templates/applicant_dev.html:114
#, fuzzy
#| msgid "Dashboard"
msgid "Dashboard & AI"
msgstr "Panel de Control"

#: templates/applicant_dev.html:123
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidate Background"
msgstr "Candidato Aceptado"

#: templates/applicant_dev.html:132 templates/applicant_dev.html:559
msgid "Resume"
msgstr ""

#: templates/applicant_dev.html:141
msgid "Journey"
msgstr ""

#: templates/applicant_dev.html:150 templates/applicant_dev.html:740
msgid "Comments"
msgstr ""

#: templates/applicant_dev.html:159 templates/applicant_dev.html:642
msgid "Emails"
msgstr ""

#: templates/applicant_dev.html:167 templates/applicant_dev.html:809
msgid "Job Details"
msgstr ""

#: templates/applicant_dev.html:191
msgid "Profile Match Analysis"
msgstr ""

#: templates/applicant_dev.html:199
msgid "Key Highlights"
msgstr ""

#: templates/applicant_dev.html:225
msgid "AI analysis will provide candidate highlights."
msgstr ""

#: templates/applicant_dev.html:235
msgid "Areas for Improvement"
msgstr ""

#: templates/applicant_dev.html:272
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidate Summary"
msgstr "Candidato Aceptado"

#: templates/applicant_dev.html:321
msgid ""
"Based on the AI analysis, when the resume is compared to the job "
"requirements,"
msgstr ""

#: templates/applicant_dev.html:323
msgid "This candidate is an"
msgstr ""

#: templates/applicant_dev.html:324
msgid "excellent match"
msgstr ""

#: templates/applicant_dev.html:326 templates/applicant_dev.html:329
#: templates/applicant_dev.html:332
msgid "This candidate is a"
msgstr ""

#: templates/applicant_dev.html:327
msgid "good match"
msgstr ""

#: templates/applicant_dev.html:330
msgid "fair match"
msgstr ""

#: templates/applicant_dev.html:333
msgid "weak match"
msgstr ""

#: templates/applicant_dev.html:336
msgid "Analyze the CV with AI to see match details."
msgstr ""

#: templates/applicant_dev.html:363
msgid "AI Analysis Available"
msgstr ""

#: templates/applicant_dev.html:366
msgid "Leverage AI to analyze this candidate's CV against the job description."
msgstr ""

#: templates/applicant_dev.html:387
msgid "Analyze with CanviderAI"
msgstr ""

#: templates/applicant_dev.html:402
msgid "Analyzing CV..."
msgstr ""

#: templates/applicant_dev.html:403
msgid "This may take a moment"
msgstr ""

#: templates/applicant_dev.html:428 templates/applicant_dev.html:449
msgid "Analysis complete"
msgstr ""

#: templates/applicant_dev.html:460
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidate Facts"
msgstr "Candidato Aceptado"

#: templates/applicant_dev.html:467
msgid "Applied Position"
msgstr ""

#: templates/applicant_dev.html:473
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidate's Address"
msgstr "Candidato Aceptado"

#: templates/applicant_dev.html:476 templates/applicant_dev.html:499
#: templates/applicant_dev.html:506 templates/applicant_dev.html:529
#: templates/applicant_dev.html:536 templates/people.html:118
#: templates/people.html:125 templates/people.html:132
msgid "Not analyzed"
msgstr ""

#: templates/applicant_dev.html:480 templates/people.html:61
#, fuzzy
#| msgid "Applicants"
msgid "Application Date"
msgstr "Candidatos"

#: templates/applicant_dev.html:486
#, fuzzy
#| msgid "Applicants"
msgid "Application Portal"
msgstr "Candidatos"

#: templates/applicant_dev.html:496
#, fuzzy
#| msgid "Create Job"
msgid "Latest/Current Position"
msgstr "Crear Empleo"

#: templates/applicant_dev.html:503
msgid "Latest/Current Employer"
msgstr ""

#: templates/applicant_dev.html:510 templates/jobs.html:69
#: templates/manage_permissions.html:117 templates/manage_permissions.html:264
#: templates/people.html:41 templates/people.html:88
#: templates/published_job_details.html:201
msgid "Status"
msgstr ""

#: templates/applicant_dev.html:516
#, fuzzy
#| msgid "Applicants"
msgid "Application ID"
msgstr "Candidatos"

#: templates/applicant_dev.html:526
msgid "Total Experience"
msgstr ""

#: templates/applicant_dev.html:533
msgid "Education Level"
msgstr ""

#: templates/applicant_dev.html:540
msgid "Notice Period"
msgstr ""

#: templates/applicant_dev.html:546
msgid "Latest Update"
msgstr ""

#: templates/applicant_dev.html:564
#, fuzzy
#| msgid "Closed"
msgid "Uploaded on"
msgstr "Cerrado"

#: templates/applicant_dev.html:581
msgid "Your browser does not support PDFs."
msgstr ""

#: templates/applicant_dev.html:585
msgid "Download the CV"
msgstr ""

#: templates/applicant_dev.html:601
#, fuzzy
#| msgid "Applicants"
msgid "Application Stages"
msgstr "Candidatos"

#: templates/applicant_dev.html:618
msgid "Started on:"
msgstr ""

#: templates/applicant_dev.html:630
msgid "No stages available for this application."
msgstr ""

#: templates/applicant_dev.html:648
msgid "Email History"
msgstr ""

#: templates/applicant_dev.html:658
msgid "From:"
msgstr ""

#: templates/applicant_dev.html:659
msgid "To:"
msgstr ""

#: templates/applicant_dev.html:662 templates/applicant_dev.html:1091
msgid "Subject:"
msgstr ""

#: templates/applicant_dev.html:677
msgid "No emails found."
msgstr ""

#: templates/applicant_dev.html:684 templates/applicant_dev.html:730
msgid "Send Email"
msgstr ""

#: templates/applicant_dev.html:699
msgid "Subject"
msgstr ""

#: templates/applicant_dev.html:709 templates/published_job_details.html:364
msgid "Email Body"
msgstr ""

#: templates/applicant_dev.html:752
msgid "Add a comment"
msgstr ""

#: templates/applicant_dev.html:758
msgid "Add your comment here..."
msgstr ""

#: templates/applicant_dev.html:763
msgid "Post Comment"
msgstr ""

#: templates/applicant_dev.html:796
msgid "No comments yet. Be the first to comment!"
msgstr ""

#: templates/applicant_dev.html:831 templates/feed.html:256
msgid "Event Title"
msgstr ""

#: templates/applicant_dev.html:842 templates/feed.html:266
msgid "Event Type"
msgstr ""

#: templates/applicant_dev.html:848 templates/feed.html:272
msgid "Select an event type"
msgstr ""

#: templates/applicant_dev.html:856 templates/feed.html:280
#: templates/manage_permissions.html:49
msgid "Recruiters"
msgstr ""

#: templates/applicant_dev.html:861 templates/feed.html:285
msgid "Select one or many recruiters"
msgstr ""

#: templates/applicant_dev.html:871 templates/feed.html:338
#: templates/people.html:31 templates/people.html:87 templates/profile.html:74
#: templates/profile.html:115
msgid "Position"
msgstr ""

#: templates/applicant_dev.html:888 templates/feed.html:360
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidate"
msgstr "Candidato Aceptado"

#: templates/applicant_dev.html:904
msgid "Date"
msgstr ""

#: templates/applicant_dev.html:913 templates/feed.html:374
msgid "Start Time"
msgstr ""

#: templates/applicant_dev.html:919 templates/feed.html:379
msgid "End Time"
msgstr ""

#: templates/applicant_dev.html:926 templates/feed.html:385
msgid "Meeting Link"
msgstr ""

#: templates/applicant_dev.html:945 templates/feed.html:403
msgid "Inform invitees by E-mail"
msgstr ""

#: templates/applicant_dev.html:950 templates/feed.html:408
msgid "Color"
msgstr ""

#: templates/applicant_dev.html:952 templates/feed.html:410
msgid "Blue"
msgstr ""

#: templates/applicant_dev.html:953 templates/feed.html:411
msgid "Light Blue"
msgstr ""

#: templates/applicant_dev.html:954 templates/feed.html:412
msgid "Purple"
msgstr ""

#: templates/applicant_dev.html:955 templates/feed.html:413
msgid "Pink"
msgstr ""

#: templates/applicant_dev.html:962 templates/applicant_dev.html:1119
#: templates/create_job_template.html:228 templates/feed.html:419
#: templates/job_details.html:157 templates/manage_permissions.html:413
#: templates/manage_permissions.html:463 templates/profile.html:125
#: templates/profile.html:152 templates/profile.html:184
#: templates/published_job_details.html:301
#: templates/published_job_details.html:386
#: templates/published_job_details.html:446
msgid "Cancel"
msgstr ""

#: templates/applicant_dev.html:965 templates/feed.html:422
msgid "Save Event"
msgstr ""

#: templates/applicant_dev.html:1024
#, fuzzy
#| msgid "Applicants"
msgid "Change Application Status"
msgstr "Candidatos"

#: templates/applicant_dev.html:1042
#, fuzzy
#| msgid "Search applicants..."
msgid "New Status"
msgstr "Buscar candidatos..."

#: templates/applicant_dev.html:1058 templates/published_job_details.html:353
msgid "Internal Notes"
msgstr ""

#: templates/applicant_dev.html:1058 templates/published_job_details.html:353
msgid "(visible only to recruiters)"
msgstr ""

#: templates/applicant_dev.html:1072
msgid "Notify candidate about this status change via email"
msgstr ""

#: templates/applicant_dev.html:1078
msgid "Email Message"
msgstr ""

#: templates/applicant_dev.html:1078
msgid "(will be included in the email to the candidate)"
msgstr ""

#: templates/applicant_dev.html:1089
msgid "Email Preview"
msgstr ""

#: templates/applicant_dev.html:1091
msgid "Your application status has been updated"
msgstr ""

#: templates/applicant_dev.html:1121
msgid "Save Change"
msgstr ""

#: templates/create_job.html:3
#, fuzzy
#| msgid "Create Job"
msgid "Create Job Position"
msgstr "Crear Empleo"

#: templates/create_job.html:8
msgid "Basic Information"
msgstr ""

#: templates/create_job.html:10 templates/job_details.html:1151
#: templates/job_preview_publish.html:590
msgid "Role Title"
msgstr ""

#: templates/create_job.html:14
msgid "e.g. Senior Software Engineer"
msgstr ""

#: templates/create_job.html:18 templates/job_details.html:1155
#: templates/job_preview_publish.html:594
#, fuzzy
#| msgid "Locations"
msgid "Office Location"
msgstr "Ubicaciones"

#: templates/create_job.html:21
msgid "Select office location"
msgstr ""

#: templates/create_job.html:31
msgid "No office locations found. Please"
msgstr ""

#: templates/create_job.html:32
msgid "add office locations"
msgstr ""

#: templates/create_job.html:32 templates/create_job.html:53
#: templates/create_job.html:74
msgid "in your preferences first."
msgstr ""

#: templates/create_job.html:36
msgid "No locations available"
msgstr ""

#: templates/create_job.html:41 templates/job_details.html:1159
#: templates/job_preview_publish.html:598
#, fuzzy
#| msgid "Work Schedules"
msgid "Work Schedule"
msgstr "Horarios de Trabajo"

#: templates/create_job.html:44 templates/create_job.html:65
msgid "Select an option"
msgstr ""

#: templates/create_job.html:52
msgid "No work schedules found. Please"
msgstr ""

#: templates/create_job.html:53
#, fuzzy
#| msgid "Work Schedules"
msgid "add work schedules"
msgstr "Horarios de Trabajo"

#: templates/create_job.html:57
msgid "No work schedules available"
msgstr ""

#: templates/create_job.html:62 templates/job_details.html:1163
#: templates/job_preview_publish.html:604
#, fuzzy
#| msgid "Office Schedules"
msgid "Office Schedule"
msgstr "Horarios de Oficina"

#: templates/create_job.html:73
msgid "No office schedules found. Please"
msgstr ""

#: templates/create_job.html:74
#, fuzzy
#| msgid "Office Schedules"
msgid "add office schedules"
msgstr "Horarios de Oficina"

#: templates/create_job.html:78
#, fuzzy
#| msgid "Office Schedules"
msgid "No office schedules available"
msgstr "Horarios de Oficina"

#: templates/create_job.html:86
msgid "Skills Requirements"
msgstr ""

#: templates/create_job.html:88
msgid "Skill"
msgstr ""

#: templates/create_job.html:93
msgid "e.g. JavaScript"
msgstr ""

#: templates/create_job.html:95 templates/create_job.html:169
msgid "Add"
msgstr ""

#: templates/create_job.html:98
msgid "Choose Skills"
msgstr ""

#: templates/create_job.html:112
msgid "Selected Skills"
msgstr ""

#: templates/create_job.html:115
msgid "No skills selected yet"
msgstr ""

#: templates/create_job.html:125
msgid "Salary Details (Optional)"
msgstr ""

#: templates/create_job.html:128
msgid "Minimum Salary"
msgstr ""

#: templates/create_job.html:132
msgid "Enter minimum salary"
msgstr ""

#: templates/create_job.html:136
msgid "Maximum Salary"
msgstr ""

#: templates/create_job.html:140
msgid "Enter maximum salary"
msgstr ""

#: templates/create_job.html:144
#, fuzzy
#| msgid "Current"
msgid "Currency"
msgstr "Actual"

#: templates/create_job.html:146
msgid "Select currency"
msgstr ""

#: templates/create_job.html:162
msgid "Benefits and Highlights (Optional)"
msgstr ""

#: templates/create_job.html:167
msgid "e.g. Yearly Bonuses"
msgstr ""

#: templates/create_job.html:172
msgid "Choose Benefits"
msgstr ""

#: templates/create_job.html:175
msgid "Dental Coverage"
msgstr ""

#: templates/create_job.html:178
msgid "Private Health Coverage"
msgstr ""

#: templates/create_job.html:181
msgid "Gym membership"
msgstr ""

#: templates/create_job.html:184
msgid "Sign-in Bonus"
msgstr ""

#: templates/create_job.html:187
msgid "Relocation Package"
msgstr ""

#: templates/create_job.html:190
msgid "Company Vehicle"
msgstr ""

#: templates/create_job.html:192
msgid "Food Card"
msgstr ""

#: templates/create_job.html:194
msgid "Snacks & Coffee"
msgstr ""

#: templates/create_job.html:197
msgid "Pet Friendly Office"
msgstr ""

#: templates/create_job.html:201
msgid "Selected Benefits & Highlights"
msgstr ""

#: templates/create_job.html:204
msgid "No benefits or highlights selected yet"
msgstr ""

#: templates/create_job.html:211 templates/job_details.html:130
#: templates/job_preview_publish.html:159
msgid "Discard"
msgstr ""

#: templates/create_job.html:212
msgid "Next"
msgstr ""

#: templates/create_job_template.html:13 templates/create_job_template.html:19
#: templates/settings.html:41
msgid "Templates"
msgstr ""

#: templates/create_job_template.html:14
#, fuzzy
#| msgid "Configure standard options to streamline your job creation process"
msgid "Create and manage reusable job description templates"
msgstr ""
"Configure opciones estándar para agilizar su proceso de creación de empleos"

#: templates/create_job_template.html:17 templates/job_preferences.html:15
#: templates/manage_permissions.html:15 templates/navbar.html:137
#: templates/settings.html:9
msgid "Settings"
msgstr "Configuración"

#: templates/create_job_template.html:31
#, fuzzy
#| msgid "Applicants"
msgid "Total Templates"
msgstr "Candidatos"

#: templates/create_job_template.html:41
#, fuzzy
#| msgid "Month"
msgid "Created This Month"
msgstr "Mes"

#: templates/create_job_template.html:51
msgid "Jobs Created from Templates"
msgstr ""

#: templates/create_job_template.html:61
msgid "Time Saved Using Templates"
msgstr ""

#: templates/create_job_template.html:71
msgid "My Templates"
msgstr ""

#: templates/create_job_template.html:74 templates/create_job_template.html:105
#: templates/create_job_template.html:118
msgid "New Template"
msgstr ""

#: templates/create_job_template.html:80
#, fuzzy
#| msgid "Search applicants..."
msgid "Search templates..."
msgstr "Buscar candidatos..."

#: templates/create_job_template.html:91
msgid "Updated"
msgstr ""

#: templates/create_job_template.html:94
msgid "Used 1 time"
msgstr ""

#: templates/create_job_template.html:96
msgid "Used"
msgstr ""

#: templates/create_job_template.html:96
msgid "times"
msgstr ""

#: templates/create_job_template.html:107
msgid "Not saved yet"
msgstr ""

#: templates/create_job_template.html:108
msgid "Not used yet"
msgstr ""

#: templates/create_job_template.html:118
msgid "Enter template title"
msgstr ""

#: templates/create_job_template.html:121
#: templates/create_job_template.html:219
msgid "Delete Template"
msgstr ""

#: templates/create_job_template.html:126 templates/job_details.html:158
msgid "Save Template"
msgstr ""

#: templates/create_job_template.html:136
msgid "Heading 1"
msgstr ""

#: templates/create_job_template.html:137
msgid "Heading 2"
msgstr ""

#: templates/create_job_template.html:138
msgid "Heading 3"
msgstr ""

#: templates/create_job_template.html:139
msgid "Paragraph"
msgstr ""

#: templates/create_job_template.html:143
msgid "Font Size"
msgstr ""

#: templates/create_job_template.html:144
msgid "Very Small"
msgstr ""

#: templates/create_job_template.html:145
msgid "Small"
msgstr ""

#: templates/create_job_template.html:146
msgid "Normal"
msgstr ""

#: templates/create_job_template.html:147
msgid "Medium"
msgstr ""

#: templates/create_job_template.html:148
msgid "Large"
msgstr ""

#: templates/create_job_template.html:149
msgid "Very Large"
msgstr ""

#: templates/create_job_template.html:150
msgid "Extra Large"
msgstr ""

#: templates/create_job_template.html:155
msgid "Bold"
msgstr ""

#: templates/create_job_template.html:156
msgid "Italic"
msgstr ""

#: templates/create_job_template.html:157
msgid "Underline"
msgstr ""

#: templates/create_job_template.html:161
msgid "Bullet List"
msgstr ""

#: templates/create_job_template.html:162
msgid "Numbered List"
msgstr ""

#: templates/create_job_template.html:166
msgid "Align Left"
msgstr ""

#: templates/create_job_template.html:167
msgid "Align Center"
msgstr ""

#: templates/create_job_template.html:168
msgid "Align Right"
msgstr ""

#: templates/create_job_template.html:172
msgid "Insert Link"
msgstr ""

#: templates/create_job_template.html:173
msgid "Remove Link"
msgstr ""

#: templates/create_job_template.html:177
msgid "Add Emoji"
msgstr ""

#: templates/create_job_template.html:202
msgid "Enter your template content here..."
msgstr ""

#: templates/create_job_template.html:208 templates/job_details.html:123
msgid "characters"
msgstr ""

#: templates/create_job_template.html:224
msgid "Are you sure you want to delete the"
msgstr ""

#: templates/create_job_template.html:224
msgid "template? This action cannot be undone."
msgstr ""

#: templates/create_job_template.html:229 templates/job_preferences.html:70
msgid "Delete"
msgstr ""

#: templates/feed.html:19 templates/feed.html:45
msgid "Loading..."
msgstr ""

#: templates/feed.html:31
msgid "Calendar"
msgstr "Calendario"

#: templates/feed.html:33
msgid "Day"
msgstr "Día"

#: templates/feed.html:34
msgid "Week"
msgstr "Semana"

#: templates/feed.html:35
msgid "Month"
msgstr "Mes"

#: templates/feed.html:50 templates/jobs.html:92 templates/people.html:64
msgid "Today"
msgstr "Hoy"

#: templates/feed.html:55
msgid "Click on a day with colored dots to view events"
msgstr ""

#: templates/feed.html:60 templates/feed.html:73
#, fuzzy
#| msgid "Month"
msgid "Mon"
msgstr "Mes"

#: templates/feed.html:61 templates/feed.html:74
msgid "Tue"
msgstr ""

#: templates/feed.html:62 templates/feed.html:75
msgid "Wed"
msgstr ""

#: templates/feed.html:63 templates/feed.html:76
msgid "Thu"
msgstr ""

#: templates/feed.html:64 templates/feed.html:77
msgid "Fri"
msgstr ""

#: templates/feed.html:65 templates/feed.html:78
msgid "Sat"
msgstr ""

#: templates/feed.html:66 templates/feed.html:79
msgid "Sun"
msgstr ""

#: templates/feed.html:95
msgid "Activity Feed"
msgstr "Feed de Actividad"

#: templates/feed.html:143
msgid "Hot"
msgstr ""

#: templates/feed.html:143 templates/navbar.html:28
msgid "Jobs"
msgstr "Empleos"

#: templates/feed.html:146
msgid "View All Jobs"
msgstr ""

#: templates/feed.html:162 templates/feed.html:199 templates/jobs.html:128
#: templates/navbar.html:33
msgid "Applicants"
msgstr "Candidatos"

#: templates/feed.html:182
msgid "Monthly Applicant Overview"
msgstr ""

#: templates/feed.html:221
msgid "Events for Date"
msgstr ""

#: templates/feed.html:243
msgid "Add New Event"
msgstr ""

#: templates/feed.html:253
msgid "Create New Event"
msgstr ""

#: templates/feed.html:260
msgid "Enter event title"
msgstr ""

#: templates/feed.html:347
msgid "Select the relevant position"
msgstr ""

#: templates/feed.html:354
msgid "No vacancies available"
msgstr ""

#: templates/feed.html:367
msgid "Pick a Vacancy to see candidates"
msgstr ""

#: templates/feed.html:389
msgid "Enter meeting link"
msgstr ""

#: templates/job_details.html:7 templates/job_details.html:20
#: templates/job_details.html:51 templates/job_preview_publish.html:28
msgid "Job Description"
msgstr ""

#: templates/job_details.html:11 templates/job_preview_publish.html:17
msgid "Job Summary"
msgstr ""

#: templates/job_details.html:14 templates/job_preview_publish.html:21
msgid "Loading job details..."
msgstr ""

#: templates/job_details.html:24
#, fuzzy
#| msgid "Create Job"
msgid "Create new description"
msgstr "Crear Empleo"

#: templates/job_details.html:28
msgid "Use saved template"
msgstr ""

#: templates/job_details.html:33
#, fuzzy
#| msgid "Applicants"
msgid "Choose a template:"
msgstr "Candidatos"

#: templates/job_details.html:35
#, fuzzy
#| msgid "Search applicants..."
msgid "Select a template"
msgstr "Buscar candidatos..."

#: templates/job_details.html:42
msgid "AI Job Description Generator"
msgstr ""

#: templates/job_details.html:43
msgid ""
"Let AI create a professional job description based on your job details above."
msgstr ""

#: templates/job_details.html:46
msgid "Generate with AI"
msgstr ""

#: templates/job_details.html:52
msgid ""
"Describe the position, responsibilities, qualifications, and any other "
"relevant details."
msgstr ""

#: templates/job_details.html:129 templates/job_preview_publish.html:158
msgid "Back"
msgstr ""

#: templates/job_details.html:134
#, fuzzy
#| msgid "Applicants"
msgid "Update Template"
msgstr "Candidatos"

#: templates/job_details.html:137 templates/job_details.html:147
#, fuzzy
#| msgid "Applicants"
msgid "Save as Template"
msgstr "Candidatos"

#: templates/job_details.html:140
msgid "Save & Continue"
msgstr ""

#: templates/job_details.html:152
msgid "Template Title"
msgstr ""

#: templates/job_details.html:153
msgid "Enter a name for this template"
msgstr ""

#: templates/job_details.html:1139
msgid ""
"No job information found. Please go back and fill out the job details form."
msgstr ""

#: templates/job_details.html:1168 templates/job_preview_publish.html:610
msgid "Salary Details"
msgstr ""

#: templates/job_details.html:1173 templates/job_preview_publish.html:617
msgid "Benefits & Highlights"
msgstr ""

#: templates/job_details.html:1183 templates/job_preview_publish.html:633
msgid "Skills"
msgstr ""

#: templates/job_preferences.html:11 templates/job_preferences.html:17
#: templates/settings.html:20
msgid "Preferences"
msgstr "Preferencias"

#: templates/job_preferences.html:12
msgid "Configure standard options to streamline your job creation process"
msgstr ""
"Configure opciones estándar para agilizar su proceso de creación de empleos"

#: templates/job_preferences.html:26 templates/job_preferences.html:51
msgid "Work Schedules"
msgstr "Horarios de Trabajo"

#: templates/job_preferences.html:30 templates/job_preferences.html:84
msgid "Office Schedules"
msgstr "Horarios de Oficina"

#: templates/job_preferences.html:34
msgid "Locations"
msgstr "Ubicaciones"

#: templates/job_preferences.html:38
msgid "Departments"
msgstr "Departamentos"

#: templates/job_preferences.html:42
msgid "Language"
msgstr "Idioma"

#: templates/job_preferences.html:52
msgid "Define standard work schedule types for your organization"
msgstr ""

#: templates/job_preferences.html:56
#, fuzzy
#| msgid "Work Schedules"
msgid "Add Work Schedule"
msgstr "Horarios de Trabajo"

#: templates/job_preferences.html:64
#, fuzzy
#| msgid "Work Schedules"
msgid "Search work schedules..."
msgstr "Horarios de Trabajo"

#: templates/job_preferences.html:67
msgid "Select All"
msgstr ""

#: templates/job_preferences.html:85
msgid "Define where and how employees work"
msgstr ""

#: templates/job_preferences.html:89
#, fuzzy
#| msgid "Office Schedules"
msgid "Add Office Schedule"
msgstr "Horarios de Oficina"

#: templates/job_preferences.html:183
msgid "Language Settings"
msgstr "Configuración de Idioma"

#: templates/job_preferences.html:184
msgid "Choose your preferred language for the application interface"
msgstr "Elija su idioma preferido para la interfaz de la aplicación"

#: templates/job_preferences.html:189
msgid "Interface Language"
msgstr "Idioma de la Interfaz"

#: templates/job_preferences.html:190
msgid "Select the language you want to use for the application interface"
msgstr "Seleccione el idioma que desea usar para la interfaz de la aplicación"

#: templates/job_preferences.html:210
msgid "Current"
msgstr "Actual"

#: templates/job_preferences.html:223 templates/published_job_details.html:412
msgid "Note:"
msgstr "Nota:"

#: templates/job_preferences.html:223
msgid ""
"Changing the language will refresh the page to apply the new language "
"settings."
msgstr ""
"Cambiar el idioma actualizará la página para aplicar la nueva configuración "
"de idioma."

#: templates/job_preview_publish.html:7 templates/job_preview_publish.html:162
msgid "Publish Job"
msgstr ""

#: templates/job_preview_publish.html:11
#, fuzzy
#| msgid "Final Interview"
msgid "Final Review"
msgstr "Entrevista Final"

#: templates/job_preview_publish.html:12
msgid "Please review the job details before publishing."
msgstr ""

#: templates/job_preview_publish.html:32
msgid "Loading job description..."
msgstr ""

#: templates/job_preview_publish.html:39
msgid "Publish To"
msgstr ""

#: templates/job_preview_publish.html:41
msgid ""
"Select the job portals where you want to publish this job posting. <br> <br> "
"<i> if the portal you want to publish to is grayed out, it means that you "
"have not yet adjusted the related configuration settings. </i>"
msgstr ""

#: templates/job_preview_publish.html:61
msgid ""
"Professional networking platform with over 750 million users worldwide. "
"Selecting this option will open a new tab for you to complete the job "
"posting."
msgstr ""

#: templates/job_preview_publish.html:82
msgid ""
"Job and company review site focusing on workplace transparency. Selecting "
"this option will open a new tab for you to complete the job posting."
msgstr ""

#: templates/job_preview_publish.html:103
#, python-format
msgid ""
"Specialized job platform for tech and creative professionals powered by "
"Workloupe. Workloupe is 100%% free to use."
msgstr ""

#: templates/job_preview_publish.html:125
msgid ""
"One of the biggest remote job focused job platforms in the world. Posting to "
"Himalayas is free. "
msgstr ""

#: templates/job_preview_publish.html:147
msgid ""
"PostJobFree has more than 7 million jobs, and it's free to post to. Their "
"job portal is focused on simplicity and ease of use."
msgstr ""

#: templates/job_preview_publish.html:580
msgid ""
"No job information found. Please go back and fill out the job details form. "
msgstr ""

#: templates/job_preview_publish.html:658
msgid "No job description found. Please go back and create a job description."
msgstr ""

#: templates/jobs.html:8
msgid "Job Listings"
msgstr ""

#: templates/jobs.html:18
#, fuzzy
#| msgid "Active"
msgid "Active Jobs"
msgstr "Activo"

#: templates/jobs.html:28
#, fuzzy
#| msgid "Applicants"
msgid "Total Applicants"
msgstr "Candidatos"

#: templates/jobs.html:38
#, fuzzy
#| msgid "Archived"
msgid "Archived Jobs"
msgstr "Archivado"

#: templates/jobs.html:48
#, fuzzy
#| msgid "On-Hold"
msgid "On-Hold Jobs"
msgstr "En Espera"

#: templates/jobs.html:59 templates/profile.html:119
#, fuzzy
#| msgid "Departments"
msgid "Department"
msgstr "Departamentos"

#: templates/jobs.html:61
#, fuzzy
#| msgid "Departments"
msgid "All Departments"
msgstr "Departamentos"

#: templates/jobs.html:71 templates/manage_permissions.html:212
#: templates/people.html:43
msgid "All Statuses"
msgstr ""

#: templates/jobs.html:79 templates/people.html:51 templates/people.html:89
#, fuzzy
#| msgid "Locations"
msgid "Location"
msgstr "Ubicaciones"

#: templates/jobs.html:81 templates/people.html:53
#, fuzzy
#| msgid "Locations"
msgid "All Locations"
msgstr "Ubicaciones"

#: templates/jobs.html:89
msgid "Posted Date"
msgstr ""

#: templates/jobs.html:91
msgid "All Time"
msgstr ""

#: templates/jobs.html:93 templates/people.html:65
#, fuzzy
#| msgid "Week"
msgid "This Week"
msgstr "Semana"

#: templates/jobs.html:94 templates/people.html:66
#, fuzzy
#| msgid "Month"
msgid "This Month"
msgstr "Mes"

#: templates/jobs.html:95
#, fuzzy
#| msgid "Month"
msgid "Last Month"
msgstr "Mes"

#: templates/jobs.html:106 templates/people.html:76
msgid "Clear all filters"
msgstr ""

#: templates/jobs.html:134
#, fuzzy
#| msgid "Final Interview"
msgid "Interviews"
msgstr "Entrevista Final"

#: templates/jobs.html:145
msgid "Days Open"
msgstr ""

#: templates/jobs.html:151
#, fuzzy
#| msgid "Closed"
msgid "Closed on:"
msgstr "Cerrado"

#: templates/jobs.html:151
msgid "Posted on:"
msgstr ""

#: templates/jobs.html:152
msgid "View Details"
msgstr ""

#: templates/jobs.html:162 templates/people.html:157
msgid "Showing"
msgstr ""

#: templates/jobs.html:162 templates/people.html:157
msgid "of"
msgstr ""

#: templates/jobs.html:162
msgid "jobs"
msgstr ""

#: templates/manage_permissions.html:11
msgid "Team & Invitations"
msgstr ""

#: templates/manage_permissions.html:12
msgid "Manage your recruitment team and invite new members"
msgstr ""

#: templates/manage_permissions.html:17 templates/manage_permissions.html:39
#: templates/manage_permissions.html:73 templates/settings.html:62
#, fuzzy
#| msgid "Locations"
msgid "Invitations"
msgstr "Ubicaciones"

#: templates/manage_permissions.html:29 templates/manage_permissions.html:69
msgid "Team Members"
msgstr ""

#: templates/manage_permissions.html:59
msgid "Administrators"
msgstr ""

#: templates/manage_permissions.html:84
#, fuzzy
#| msgid "Search applicants..."
msgid "Search team members..."
msgstr "Buscar candidatos..."

#: templates/manage_permissions.html:88 templates/manage_permissions.html:218
msgid "Role:"
msgstr ""

#: templates/manage_permissions.html:90 templates/manage_permissions.html:220
msgid "All Roles"
msgstr ""

#: templates/manage_permissions.html:105 templates/manage_permissions.html:244
#: templates/people.html:86 templates/profile.html:49
#: templates/published_job_details.html:198
msgid "Name"
msgstr ""

#: templates/manage_permissions.html:109 templates/manage_permissions.html:248
#: templates/profile.html:57 templates/profile.html:101
#: templates/register.html:24 templates/signin.html:15
msgid "Email"
msgstr ""

#: templates/manage_permissions.html:113 templates/manage_permissions.html:252
#: templates/manage_permissions.html:381
msgid "Role"
msgstr ""

#: templates/manage_permissions.html:120 templates/manage_permissions.html:267
#: templates/profile.html:80 templates/published_job_details.html:207
#, fuzzy
#| msgid "Active"
msgid "Actions"
msgstr "Activo"

#: templates/manage_permissions.html:143
#, fuzzy
#| msgid "Active"
msgid "Deactivate"
msgstr "Activo"

#: templates/manage_permissions.html:147
#, fuzzy
#| msgid "Active"
msgid "Activate"
msgstr "Activo"

#: templates/manage_permissions.html:206
#, fuzzy
#| msgid "Search applicants..."
msgid "Search invitations..."
msgstr "Buscar candidatos..."

#: templates/manage_permissions.html:210
#, fuzzy
#| msgid "Search applicants..."
msgid "Status:"
msgstr "Buscar candidatos..."

#: templates/manage_permissions.html:231
msgid "Invite New Member"
msgstr ""

#: templates/manage_permissions.html:256
msgid "Sent Date"
msgstr ""

#: templates/manage_permissions.html:260
msgid "Expiry Date"
msgstr ""

#: templates/manage_permissions.html:305
#, fuzzy
#| msgid "Locations"
msgid "No invitations found"
msgstr "Ubicaciones"

#: templates/manage_permissions.html:353
msgid "Invite New Team Member"
msgstr ""

#: templates/manage_permissions.html:360
msgid "Recipient Information"
msgstr ""

#: templates/manage_permissions.html:364 templates/profile.html:93
msgid "First Name"
msgstr ""

#: templates/manage_permissions.html:365
msgid "Enter first name"
msgstr ""

#: templates/manage_permissions.html:369 templates/profile.html:97
msgid "Last Name"
msgstr ""

#: templates/manage_permissions.html:370
msgid "Enter last name"
msgstr ""

#: templates/manage_permissions.html:375
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Email Address"
msgstr "Candidato Aceptado"

#: templates/manage_permissions.html:376
msgid "Enter email address"
msgstr ""

#: templates/manage_permissions.html:383 templates/manage_permissions.html:443
#, fuzzy
#| msgid "Search applicants..."
msgid "Select a role"
msgstr "Buscar candidatos..."

#: templates/manage_permissions.html:384 templates/manage_permissions.html:444
msgid "Administrator"
msgstr ""

#: templates/manage_permissions.html:385 templates/manage_permissions.html:437
#: templates/manage_permissions.html:445
msgid "Recruiter"
msgstr ""

#: templates/manage_permissions.html:386 templates/manage_permissions.html:446
msgid "Hiring Manager"
msgstr ""

#: templates/manage_permissions.html:387 templates/manage_permissions.html:447
#, fuzzy
#| msgid "Final Interview"
msgid "Interviewer"
msgstr "Entrevista Final"

#: templates/manage_permissions.html:388 templates/manage_permissions.html:448
msgid "Read Only"
msgstr ""

#: templates/manage_permissions.html:395
msgid "Permissions"
msgstr ""

#: templates/manage_permissions.html:396
msgid ""
"Permissions are determined by the selected role. You can customize them "
"after the user has accepted the invitation."
msgstr ""

#: templates/manage_permissions.html:400
#, fuzzy
#| msgid "Create Job"
msgid "Role Descriptions:"
msgstr "Crear Empleo"

#: templates/manage_permissions.html:402
msgid "Administrator: Full access to all system features and settings."
msgstr ""

#: templates/manage_permissions.html:403
msgid "Recruiter: Manage job postings, candidates, and interviews."
msgstr ""

#: templates/manage_permissions.html:404
msgid "Hiring Manager: Review candidates and make hiring decisions."
msgstr ""

#: templates/manage_permissions.html:405
msgid "Interviewer: Conduct interviews and provide feedback."
msgstr ""

#: templates/manage_permissions.html:406
msgid "Read Only: View-only access to recruitment data."
msgstr ""

#: templates/manage_permissions.html:414
#, fuzzy
#| msgid "Locations"
msgid "Send Invitation"
msgstr "Ubicaciones"

#: templates/manage_permissions.html:425 templates/manage_permissions.html:464
#, fuzzy
#| msgid "Create Job"
msgid "Change Role"
msgstr "Crear Empleo"

#: templates/manage_permissions.html:431
msgid "Team Member"
msgstr ""

#: templates/manage_permissions.html:436
#, fuzzy
#| msgid "Current"
msgid "Current Role"
msgstr "Actual"

#: templates/manage_permissions.html:441
msgid "New Role*"
msgstr ""

#: templates/manage_permissions.html:453
msgid "Reason for Change (Optional)"
msgstr ""

#: templates/manage_permissions.html:454
msgid "Provide a reason for this role change"
msgstr ""

#: templates/manage_permissions.html:459
msgid ""
"Changing roles will update the user's permissions. They will be notified of "
"this change."
msgstr ""

#: templates/navbar.html:23
msgid "Feed"
msgstr "Inicio"

#: templates/navbar.html:41
msgid "Create Job"
msgstr "Crear Empleo"

#: templates/navbar.html:121
msgid "Guest User"
msgstr ""

#: templates/navbar.html:128
msgid "Not logged in"
msgstr ""

#: templates/navbar.html:134
msgid "Profile"
msgstr "Perfil"

#: templates/navbar.html:141
msgid "Logout"
msgstr "Cerrar Sesión"

#: templates/people.html:10
msgid "Applicant Tracking"
msgstr "Seguimiento de Candidatos"

#: templates/people.html:14 templates/people.html:967
#, fuzzy
#| msgid "Applicants"
msgid "Refresh Applicants"
msgstr "Candidatos"

#: templates/people.html:18
msgid "Search applicants..."
msgstr "Buscar candidatos..."

#: templates/people.html:33
msgid "All Positions"
msgstr ""

#: templates/people.html:63
msgid "All Dates"
msgstr ""

#: templates/people.html:90
msgid "Experience (Years)"
msgstr ""

#: templates/people.html:91 templates/published_job_details.html:204
msgid "Score"
msgstr ""

#: templates/people.html:92
#, fuzzy
#| msgid "Applicants"
msgid "Applied On"
msgstr "Candidatos"

#: templates/people.html:93
#, fuzzy
#| msgid "Active"
msgid "Action"
msgstr "Activo"

#: templates/people.html:140
#, fuzzy
#| msgid "Applicants"
msgid "View Application"
msgstr "Candidatos"

#: templates/people.html:147
msgid "No applicants found matching the current filters."
msgstr ""

#: templates/people.html:157
#, fuzzy
#| msgid "Applicants"
msgid "applicants"
msgstr "Candidatos"

#: templates/people.html:210
msgid "Show"
msgstr ""

#: templates/people.html:217
msgid "per page"
msgstr ""

#: templates/people.html:902
msgid "Processing..."
msgstr ""

#: templates/people.html:948
msgid "Success!"
msgstr ""

#: templates/people.html:955
msgid "Error:"
msgstr ""

#: templates/people.html:960
msgid "An error occurred while processing applications. Please try again."
msgstr ""

#: templates/profile.html:10
#, fuzzy
#| msgid "Profile"
msgid "Your Profile"
msgstr "Perfil"

#: templates/profile.html:29
msgid "Change Photo"
msgstr ""

#: templates/profile.html:32
#, fuzzy
#| msgid "Profile"
msgid "Profile Activity"
msgstr "Perfil"

#: templates/profile.html:33
#, fuzzy
#| msgid "Month"
msgid "Last Login:"
msgstr "Mes"

#: templates/profile.html:36
msgid "Account Created:"
msgstr ""

#: templates/profile.html:46 templates/profile.html:91
msgid "Personal Information"
msgstr ""

#: templates/profile.html:63 templates/profile.html:109
msgid "Company Information"
msgstr ""

#: templates/profile.html:66 templates/profile.html:111
msgid "Company"
msgstr ""

#: templates/profile.html:83 templates/profile.html:163
#: templates/profile.html:185
#, fuzzy
#| msgid "Create Job"
msgid "Change Password"
msgstr "Crear Empleo"

#: templates/profile.html:105
#, fuzzy
#| msgid "Phone Call"
msgid "Phone"
msgstr "Llamada Telefónica"

#: templates/profile.html:124
msgid "Save Changes"
msgstr ""

#: templates/profile.html:139
msgid "Upload Profile Photo"
msgstr ""

#: templates/profile.html:146
msgid "Choose a photo (PNG or JPEG only)"
msgstr ""

#: templates/profile.html:153
msgid "Upload"
msgstr ""

#: templates/profile.html:170
#, fuzzy
#| msgid "Create Job"
msgid "Current Password"
msgstr "Crear Empleo"

#: templates/profile.html:174
#, fuzzy
#| msgid "Create Job"
msgid "New Password"
msgstr "Crear Empleo"

#: templates/profile.html:178
#, fuzzy
#| msgid "Create Job"
msgid "Confirm New Password"
msgstr "Crear Empleo"

#: templates/published_job_details.html:58
#, fuzzy
#| msgid "Locations"
msgid "Notification"
msgstr "Ubicaciones"

#: templates/published_job_details.html:82
#, fuzzy
#| msgid "Applicants"
msgid "Total Applicants:"
msgstr "Candidatos"

#: templates/published_job_details.html:87
msgid "Published At:"
msgstr ""

#: templates/published_job_details.html:111
msgid "Bulk Communication"
msgstr ""

#: templates/published_job_details.html:123
msgid "Expert Support Options"
msgstr ""

#: templates/published_job_details.html:136
#: templates/published_job_details.html:401
msgid "Change Vacancy Status"
msgstr ""

#: templates/published_job_details.html:150
#, fuzzy
#| msgid "Applicants"
msgid "Applicants Over Time"
msgstr "Candidatos"

#: templates/published_job_details.html:162
#, fuzzy
#| msgid "Applicants"
msgid "Number of Applicants by Job Portal"
msgstr "Candidatos"

#: templates/published_job_details.html:170
msgid "Distribution of Applicants by Status"
msgstr ""

#: templates/published_job_details.html:183
#, fuzzy
#| msgid "Applicants"
msgid "Top Applicants"
msgstr "Candidatos"

#: templates/published_job_details.html:189
#, fuzzy
#| msgid "Applicants"
msgid "View All Applicants"
msgstr "Candidatos"

#: templates/published_job_details.html:254
msgid "Not Rated"
msgstr ""

#: templates/published_job_details.html:265
msgid "View"
msgstr ""

#: templates/published_job_details.html:281
msgid "Request Support From Experts"
msgstr ""

#: templates/published_job_details.html:286
msgid ""
"We can provide vetted candidates from our talent pool, or help you during "
"the technical interviews to pick best fit for your expectations."
msgstr ""

#: templates/published_job_details.html:291
msgid "Enter Details"
msgstr ""

#: templates/published_job_details.html:302
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Request Candidates"
msgstr "Candidato Aceptado"

#: templates/published_job_details.html:303
#, fuzzy
#| msgid "Online Interview"
msgid "Request Interview Help"
msgstr "Entrevista Online"

#: templates/published_job_details.html:317
msgid "Send Bulk Mail to Applicants"
msgstr ""

#: templates/published_job_details.html:322
msgid ""
"Use this form to send bulk emails to applicants based on their application "
"statuses."
msgstr ""

#: templates/published_job_details.html:327
#, fuzzy
#| msgid "Applicants"
msgid "Select Application Status"
msgstr "Candidatos"

#: templates/published_job_details.html:334
#, fuzzy
#| msgid "Search applicants..."
msgid "Select a status"
msgstr "Buscar candidatos..."

#: templates/published_job_details.html:341
msgid "Email Subject"
msgstr ""

#: templates/published_job_details.html:360
msgid "Enter internal notes for your team (optional)"
msgstr ""

#: templates/published_job_details.html:364
msgid "(sent to candidates)"
msgstr ""

#: templates/published_job_details.html:371
msgid "Enter your email message"
msgstr ""

#: templates/published_job_details.html:383
msgid "Send notification emails to candidates"
msgstr ""

#: templates/published_job_details.html:387
msgid "Send Emails"
msgstr ""

#: templates/published_job_details.html:406
#, fuzzy
#| msgid "Create Job"
msgid "Current Status:"
msgstr "Crear Empleo"

#: templates/published_job_details.html:412
msgid "Changing the status will affect the visibility of the vacancy."
msgstr ""

#: templates/published_job_details.html:415
msgid ""
"The vacancy will no longer exist on boards and be closed but be accesible "
"internally."
msgstr ""

#: templates/published_job_details.html:416
msgid "The vacancy will stop accepting new applications until changed."
msgstr ""

#: templates/published_job_details.html:417
msgid "The vacancy will be re-opened for new applications."
msgstr ""

#: templates/published_job_details.html:418
msgid "The vacancy will be permanently deleted. This action cannot be undone."
msgstr ""

#: templates/published_job_details.html:424
#, fuzzy
#| msgid "Search applicants..."
msgid "Select New Status"
msgstr "Buscar candidatos..."

#: templates/published_job_details.html:447
msgid "Confirm Status"
msgstr ""

#: templates/register.html:11
msgid "Accept Invitation"
msgstr ""

#: templates/register.html:15
msgid "This invitation has expired or already been used."
msgstr ""

#: templates/register.html:18
msgid "Hello"
msgstr ""

#: templates/register.html:18
msgid "you've been invited to join"
msgstr ""

#: templates/register.html:18
msgid "as a"
msgstr ""

#: templates/register.html:29
#, fuzzy
#| msgid "Create Job"
msgid "Create Password"
msgstr "Crear Empleo"

#: templates/register.html:34
msgid "Confirm Password"
msgstr ""

#: templates/register.html:39
msgid "Complete Registration"
msgstr ""

#: templates/register.html:58
msgid "Passwords do not match"
msgstr ""

#: templates/registration_complete.html:14
msgid "Registration Complete!"
msgstr ""

#: templates/registration_complete.html:15
msgid ""
"Your account has been created successfully. You can now log in to access the "
"system."
msgstr ""

#: templates/registration_complete.html:17
msgid "Go to Login"
msgstr ""

#: templates/settings.html:10
msgid "Configure your recruitment workflow and manage your ATS settings"
msgstr ""
"Configure su flujo de trabajo de reclutamiento y administre la configuración "
"de su ATS"

#: templates/settings.html:21
msgid ""
"Configure default options for job creation including work schedules, office "
"locations, and role titles."
msgstr ""

#: templates/settings.html:23
msgid "Define company work schedules"
msgstr ""

#: templates/settings.html:24
msgid "Set up office locations"
msgstr ""

#: templates/settings.html:25
msgid "Standardize role titles"
msgstr ""

#: templates/settings.html:26
msgid "Configure office schedule options"
msgstr ""

#: templates/settings.html:29
#, fuzzy
#| msgid "Preferences"
msgid "Manage Preferences"
msgstr "Preferencias"

#: templates/settings.html:42
#, fuzzy
#| msgid "Configure standard options to streamline your job creation process"
msgid ""
"Create, edit, and manage job description templates to streamline your job "
"posting process."
msgstr ""
"Configure opciones estándar para agilizar su proceso de creación de empleos"

#: templates/settings.html:44
msgid "Build reusable job templates"
msgstr ""

#: templates/settings.html:45
msgid "Save time on repetitive descriptions"
msgstr ""

#: templates/settings.html:46
msgid "Maintain consistent job postings"
msgstr ""

#: templates/settings.html:47
msgid "Organize templates by department"
msgstr ""

#: templates/settings.html:50
msgid "Manage Templates"
msgstr ""

#: templates/settings.html:63
msgid ""
"Invite team members to collaborate on your recruitment process and manage "
"user access."
msgstr ""

#: templates/settings.html:65
msgid "Add colleagues to your ATS"
msgstr ""

#: templates/settings.html:66
msgid "Set user permissions"
msgstr ""

#: templates/settings.html:67
msgid "Track invitation status"
msgstr ""

#: templates/settings.html:68
msgid "Manage team collaboration"
msgstr ""

#: templates/settings.html:71
#, fuzzy
#| msgid "Language Settings"
msgid "Manage Invitations"
msgstr "Configuración de Idioma"

#: templates/settings.html:84
msgid "Job Portals"
msgstr ""

#: templates/settings.html:85
msgid ""
"Configure connections to external job boards and manage API credentials for "
"job publishing."
msgstr ""

#: templates/settings.html:87
msgid "Connect to major job boards"
msgstr ""

#: templates/settings.html:88
msgid "Manage API tokens securely"
msgstr ""

#: templates/settings.html:89
msgid "Customize portal preferences"
msgstr ""

#: templates/settings.html:90
msgid "Track portal integration status"
msgstr ""

#: templates/settings.html:93
msgid "(Coming Soon!)"
msgstr ""

#: templates/settings.html:103
msgid "Need Help?"
msgstr ""

#: templates/settings.html:104
msgid ""
"Our support team is ready to assist you with any questions about configuring "
"your ATS."
msgstr ""

#: templates/settings.html:105
msgid "Contact Support"
msgstr ""

#: templates/signin.html:16
msgid "<EMAIL>"
msgstr ""

#: templates/signin.html:20
msgid "Password"
msgstr ""

#: templates/signin.html:48
msgid "Signin"
msgstr ""

#, fuzzy
#~| msgid "Candidate Accepted"
#~ msgid "Candidates Hired"
#~ msgstr "Candidato Aceptado"

#, fuzzy
#~| msgid "Jobs"
#~ msgid "Hot Jobs"
#~ msgstr "Empleos"
