#!/usr/bin/env python
"""
Test script for PostJobFree integration.
This script can be used to test the PDF extraction and parsing functionality.
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'canvider.settings')
django.setup()

from feed.utils.pdf_utils import extract_text_from_pdf, validate_pdf_content, get_pdf_info
from feed.utils.application_parser import PostJobFreeParser

def test_email_parsing():
    """Test email subject and body parsing functionality."""
    print("Testing email parsing...")

    parser = PostJobFreeParser()

    # Test subject parsing (vacancy ID only)
    test_subjects = [
        "#Workloupe-36 Web Development Intern in Warsaw, Masovian Voivodeship, 03,...",
        "#Workloupe-42 Senior Developer Position",
        "Workloupe-123 Data Scientist Role",
    ]

    print("=== Testing Subject Parsing ===")
    for subject in test_subjects:
        result = parser.parse_email_subject(subject)
        print(f"Subject: {subject}")
        print(f"Parsed: {result}")
        print("-" * 50)

    # Test body parsing (candidate info)
    test_bodies = [
        "<EMAIL> applied to Web Development Intern job in Warsaw, Masovian Voivodeship, 03, Poland\nSent from Wroclaw, Lower Silesia, Poland\n\nResume:\nBirhan Özçelik...",
        "<EMAIL> applied to Senior Developer job in Krakow, Poland\nSent from Remote\n\nResume:\nJohn Doe...",
        "<EMAIL> applied to Data Scientist job in Remote, Europe\n\nResume content here...",
    ]

    print("\n=== Testing Body Parsing ===")
    for body in test_bodies:
        result = parser.parse_email_body(body)
        print(f"Body preview: {body[:100]}...")
        print(f"Parsed: {result}")
        print("-" * 50)

def test_pdf_processing():
    """Test PDF processing functionality."""
    print("Testing PDF processing...")

    # Check if test PDF exists
    test_pdf_path = "feed/docs/efe_short_cv_2023.pdf"
    if not os.path.exists(test_pdf_path):
        print(f"Test PDF not found at {test_pdf_path}")
        return

    # Read PDF content
    with open(test_pdf_path, 'rb') as f:
        pdf_content = f.read()

    # Test PDF validation
    is_valid = validate_pdf_content(pdf_content)
    print(f"PDF is valid: {is_valid}")

    # Get PDF info
    pdf_info = get_pdf_info(pdf_content)
    print(f"PDF info: {pdf_info}")

    # Extract text
    extracted_text = extract_text_from_pdf(pdf_content)
    print(f"Extracted text length: {len(extracted_text)}")
    print(f"First 200 characters: {extracted_text[:200]}...")

    return extracted_text

def test_candidate_info_extraction():
    """Test AI-powered candidate information extraction."""
    print("Testing AI-powered candidate information extraction...")

    # Sample resume text (from the example provided)
    sample_resume = """
    Birhan Özçelik
    Software Engineer
    Wroclaw, Poland +48 451 033 660 <EMAIL>
    Software Engineer NOKIA 07/2022 - Present Wroclaw, Poland
    • Implementation of 4G (LTE)/5G (NR) features in Physical Layer (L1) for SoC in C/C++ on daily basis
    • Maintaining signal processing and DSP algorithms
    • Parallel programming on GPU with CUDA
    • Developing of testing environment in Python
    • Bug-fixing of internal/customer issues
    • Preparation of technical documents for developers Software Engineer Rider.ai / Acrome Robotics 10/2020 - 06/2022 Remote
    • Implementing C++ plugins in order to control robots in simulation
    • Developing Python modules to abstract the code complexity for users
    • Preparation technical documents for customers
    • Developing bash scripts for workspace in the server
    • Developing Python scripts for data extraction
    • Technical support for customers
    Master's Degree, Embedded Robotics / Control Engineering and Robotics Wroclaw University of Science and Technology 02/2023 - 07/2024 Wroclaw, Poland Thesis Subject: An algorithm of cooperative landing of a multi-rotor drone on a mobile platform Bachelor's Degree, Computer Engineering
    Suleyman Demirel University 09/2016 - 06/2020 Isparta, Türkiye Scientific and Technological Research Council of Turkey
    • 2242 University Students Research Project Competitions Konya Area WINNER
    • 2209-A Research Project Support Program for Undergraduate Students Professional Experience
    Programming: C/C++ (Advanced), Python (Advanced), Bash, CUDA Frameworks & Libraries: Django, Pytest, CppUTest, GTest, Selenium DevOps & Tools: Git, CMake, Docker, Jenkins, Gerrit, GitLab Jira, Confluence, PowerBI, Zuul Platforms: Linux,
    Skills
    Education
    Awards
    LinkedIn: https://www.linkedin.com/in/birhanozcelik Github: https://github.com/birhanozcelik
    Professional Summary
    Experienced software engineer with 4+ years of expertise in developing high-performance applications using C/C++ and Python. Specialized in embedded systems, telecommunications, and robotics with a strong background in algorithm development and parallel programming. Seeking senior-level opportunities to leverage my technical skills and leadership experience in innovative software development
    """

    try:
        from canviderAi.services import extract_candidate_info_from_cv
        candidate_info = extract_candidate_info_from_cv(sample_resume)

        print("AI-extracted candidate information:")
        for key, value in candidate_info.items():
            print(f"{key}: {value}")
    except Exception as e:
        print(f"AI extraction failed: {str(e)}")

def test_manual_processing():
    """Test the manual processing function."""
    print("Testing manual processing function...")

    try:
        from feed.views import process_postjobfree_applications_manual
        processed_count = process_postjobfree_applications_manual()
        print(f"Manual processing completed. Processed {processed_count} applications.")
    except Exception as e:
        print(f"Manual processing failed: {str(e)}")

def main():
    """Run all tests."""
    print("=" * 60)
    print("PostJobFree Integration Test Suite")
    print("=" * 60)

    try:
        test_email_parsing()
        print("\n")

        test_pdf_processing()
        print("\n")

        test_candidate_info_extraction()
        print("\n")

        test_manual_processing()
        print("\n")

        print("All tests completed!")

    except Exception as e:
        print(f"Test suite failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
